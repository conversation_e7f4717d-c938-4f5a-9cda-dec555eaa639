package org.jeecg.modules.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 豆包文生图API服务
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@Slf4j
public class DouBaoImageApiService {

    @Value("${doubao.image.api.key:57475db6-f038-4305-8b83-1b361b16df8b}")
    private String douBaoApiKey;

    @Value("${doubao.image.api.url:https://ark.cn-beijing.volces.com}")
    private String douBaoApiUrl;

    @Value("${doubao.image.api.version:v3}")
    private String apiVersion;

    // 默认参数值
    private static final String MODEL_NAME = "doubao-seedream-3-0-t2i-250415";
    private static final String DEFAULT_SIZE = "1024x1024";
    private static final Integer DEFAULT_SEED = -1;
    private static final Float DEFAULT_GUIDANCE_SCALE = 2.5f;
    private static final Boolean DEFAULT_WATERMARK = false;
    private static final String DEFAULT_RESPONSE_FORMAT = "url";

    // 参数验证范围
    private static final Float MIN_GUIDANCE_SCALE = 1.0f;
    private static final Float MAX_GUIDANCE_SCALE = 10.0f;
    private static final Integer MIN_SEED = -1;
    private static final Integer MAX_SEED = 2147483647;

    // 图片尺寸限制
    private static final int MIN_SIZE = 512;
    private static final int MAX_SIZE = 2048;

    /**
     * 生成图片
     */
    public Map<String, Object> generateImage(Map<String, Object> params) {
        try {
            log.info("开始调用豆包文生图API生成图片，参数: {}", params);

            // 1. 参数验证和标准化
            validateAndNormalizeParams(params);

            // 2. 构建请求URL
            String url = String.format("%s/api/%s/images/generations", douBaoApiUrl, apiVersion);

            // 3. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(douBaoApiKey);

            // 4. 构建请求体
            Map<String, Object> requestBody = buildImageRequest(params);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 5. 调用豆包API
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            log.info("豆包文生图API响应状态: {}", response.getStatusCode());
            log.info("豆包文生图API响应内容: {}", response.getBody());

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JSONObject responseJson = JSON.parseObject(response.getBody());

                // 6. 解析响应
                return parseImageResponse(responseJson);
            } else {
                throw new RuntimeException("豆包API调用失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用豆包文生图API失败", e);
            throw new RuntimeException("调用豆包API失败: " + e.getMessage());
        }
    }

    /**
     * 参数验证和标准化
     */
    private void validateAndNormalizeParams(Map<String, Object> params) {
        // 验证必填参数（只检查非空，长度限制由豆包插件校验）
        String prompt = (String) params.get("prompt");
        if (!StringUtils.hasText(prompt)) {
            throw new IllegalArgumentException("prompt不能为空");
        }
        // 移除长度限制，由豆包插件处理

        // 验证可选参数
        String size = (String) params.get("size");
        if (size != null && !isValidSize(size)) {
            throw new IllegalArgumentException("不支持的图片尺寸: " + size + "，要求格式为'宽x高'，尺寸介于 [" + MIN_SIZE + " x " + MIN_SIZE + ", " + MAX_SIZE + " x " + MAX_SIZE + "] 之间");
        }

        Integer seed = (Integer) params.get("seed");
        if (seed != null && (seed < MIN_SEED || seed > MAX_SEED)) {
            throw new IllegalArgumentException("seed取值范围为[-1, 2147483647]");
        }

        Float guidanceScale = (Float) params.get("guidance_scale");
        if (guidanceScale != null && 
            (guidanceScale < MIN_GUIDANCE_SCALE || guidanceScale > MAX_GUIDANCE_SCALE)) {
            throw new IllegalArgumentException("guidance_scale取值范围为[1.0, 10.0]");
        }

        log.info("参数验证通过");
    }

    /**
     * 构建图片生成请求体
     */
    private Map<String, Object> buildImageRequest(Map<String, Object> params) {
        Map<String, Object> request = new HashMap<>();

        // 必填参数
        request.put("model", MODEL_NAME);
        request.put("prompt", params.get("prompt"));
        request.put("response_format", DEFAULT_RESPONSE_FORMAT);

        // 可选参数，应用默认值
        request.put("size", params.getOrDefault("size", DEFAULT_SIZE));
        request.put("seed", params.getOrDefault("seed", DEFAULT_SEED));
        request.put("guidance_scale", params.getOrDefault("guidance_scale", DEFAULT_GUIDANCE_SCALE));
        request.put("watermark", params.getOrDefault("watermark", DEFAULT_WATERMARK));

        log.info("构建的请求体: {}", request);
        return request;
    }

    /**
     * 解析图片生成响应
     */
    private Map<String, Object> parseImageResponse(JSONObject responseJson) {
        try {
            // 解析基本信息
            String model = responseJson.getString("model");
            Long created = responseJson.getLong("created");
            
            // 解析图片数据
            JSONArray dataArray = responseJson.getJSONArray("data");
            if (dataArray == null || dataArray.isEmpty()) {
                throw new RuntimeException("响应中没有图片数据");
            }

            JSONObject imageData = dataArray.getJSONObject(0);
            String imageUrl = imageData.getString("url");
            
            if (!StringUtils.hasText(imageUrl)) {
                throw new RuntimeException("响应中没有图片URL");
            }

            // 解析使用量信息
            JSONObject usage = responseJson.getJSONObject("usage");
            Integer generatedImages = usage != null ? usage.getInteger("generated_images") : 1;
            Integer outputTokens = usage != null ? usage.getInteger("output_tokens") : 0;
            Integer totalTokens = usage != null ? usage.getInteger("total_tokens") : 0;

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("image_url", imageUrl);
            result.put("model", model);
            result.put("created", created);
            result.put("generated_images", generatedImages);
            result.put("output_tokens", outputTokens);
            result.put("total_tokens", totalTokens);
            result.put("message", "图片生成成功");

            log.info("图片生成成功: imageUrl={}, model={}, generatedImages={}, 完整响应: {}",
                    imageUrl, model, generatedImages, responseJson.toJSONString());
            return result;

        } catch (Exception e) {
            log.error("解析豆包API响应失败", e);
            throw new RuntimeException("解析API响应失败: " + e.getMessage());
        }
    }

    /**
     * 验证图片尺寸是否有效
     */
    private boolean isValidSize(String size) {
        if (size == null || size.trim().isEmpty()) {
            return false;
        }

        try {
            String[] parts = size.split("x");
            if (parts.length != 2) {
                return false;
            }

            int width = Integer.parseInt(parts[0].trim());
            int height = Integer.parseInt(parts[1].trim());

            return width >= MIN_SIZE && width <= MAX_SIZE &&
                   height >= MIN_SIZE && height <= MAX_SIZE;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取推荐的图片尺寸列表
     */
    public Set<String> getSupportedSizes() {
        Set<String> recommendedSizes = new HashSet<>();
        recommendedSizes.add("1024x1024");
        recommendedSizes.add("864x1152");
        recommendedSizes.add("1152x864");
        recommendedSizes.add("1280x720");
        recommendedSizes.add("720x1280");
        recommendedSizes.add("832x1248");
        recommendedSizes.add("1248x832");
        recommendedSizes.add("1512x648");
        return recommendedSizes;
    }

    /**
     * 获取默认参数值
     */
    public Map<String, Object> getDefaultParams() {
        Map<String, Object> defaults = new HashMap<>();
        defaults.put("size", DEFAULT_SIZE);
        defaults.put("seed", DEFAULT_SEED);
        defaults.put("guidance_scale", DEFAULT_GUIDANCE_SCALE);
        defaults.put("watermark", DEFAULT_WATERMARK);
        return defaults;
    }
}
