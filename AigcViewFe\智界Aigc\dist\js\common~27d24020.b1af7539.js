(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~27d24020"],{"113a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"withdrawal-management"},[e._m(0),a("div",{staticClass:"search-section"},[a("a-card",{attrs:{bordered:!1}},[a("a-form",{attrs:{layout:"inline",model:e.searchForm},on:{submit:e.handleSearch}},[a("a-form-item",{attrs:{label:"申请状态"}},[a("a-select",{staticStyle:{width:"120px"},attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待审核")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("审核拒绝")]),a("a-select-option",{attrs:{value:4}},[e._v("已取消")])],1)],1),a("a-form-item",{attrs:{label:"申请时间"}},[a("a-range-picker",{attrs:{format:"YYYY-MM-DD",placeholder:["开始时间","结束时间"]},model:{value:e.searchForm.dateRange,callback:function(t){e.$set(e.searchForm,"dateRange",t)},expression:"searchForm.dateRange"}})],1),a("a-form-item",{attrs:{label:"用户名"}},[a("a-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入用户名"},model:{value:e.searchForm.username,callback:function(t){e.$set(e.searchForm,"username",t)},expression:"searchForm.username"}})],1),a("a-form-item",{attrs:{label:"支付宝信息"}},[a("a-input",{staticStyle:{width:"150px"},attrs:{placeholder:"支付宝账号或姓名"},model:{value:e.searchForm.alipayInfo,callback:function(t){e.$set(e.searchForm,"alipayInfo",t)},expression:"searchForm.alipayInfo"}})],1),a("a-form-item",[a("a-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSearch}},[a("a-icon",{attrs:{type:"search"}}),e._v("\n            搜索\n          ")],1),a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleReset}},[a("a-icon",{attrs:{type:"reload"}}),e._v("\n            重置\n          ")],1)],1)],1)],1)],1),a("div",{staticClass:"table-section"},[a("a-card",{attrs:{bordered:!1}},[a("a-table",{attrs:{columns:e.columns,"data-source":e.dataSource,loading:e.loading,pagination:e.pagination,"row-key":"id",scroll:{x:1200}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"userInfo",fn:function(t,r){return[r?a("div",{staticClass:"user-info"},[a("div",{staticClass:"username"},[e._v(e._s(r.username||"-"))]),a("div",{staticClass:"user-id"},[e._v("ID: "+e._s(r.user_id||"-"))])]):a("span",[e._v("-")])]}},{key:"amount",fn:function(t,r){return[r?a("div",{staticClass:"amount-info"},[a("div",{staticClass:"amount"},[e._v("¥"+e._s(e.formatNumber(r.withdrawal_amount)))])]):a("span",[e._v("-")])]}},{key:"alipayInfo",fn:function(t,r){return[r?a("div",{staticClass:"alipay-info"},[a("div",{staticClass:"name"},[e._v(e._s(r.alipay_name||"-"))]),a("div",{staticClass:"account"},[e._v(e._s(r.alipay_account||"-"))])]):a("span",[e._v("-")])]}},{key:"status",fn:function(t,r){return[r?a("a-tag",{attrs:{color:e.getStatusColor(r&&r.status)}},[e._v("\n            "+e._s(e.getStatusText(r.status,r.review_remark))+"\n          ")]):a("span",[e._v("-")])]}},{key:"applyTime",fn:function(t,r){return[a("span",[e._v(e._s(r&&r.apply_time?e.formatDateTime(r.apply_time):"-"))])]}},{key:"reviewTime",fn:function(t,r){return[a("span",[e._v(e._s(r&&r.review_time?e.formatDateTime(r.review_time):"-"))])]}},{key:"action",fn:function(t,r){return[r?a("div",{staticClass:"action-buttons"},[1===r.status?a("a-button",{attrs:{type:"primary",size:"small",loading:r.approving},on:{click:function(t){return e.handleApprove(r)}}},[e._v("\n              审核通过\n            ")]):e._e(),1===r.status?a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"danger",size:"small",loading:r.rejecting},on:{click:function(t){return e.handleReject(r)}}},[e._v("\n              审核拒绝\n            ")]):e._e(),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{size:"small"},on:{click:function(t){return e.handleViewDetail(r)}}},[e._v("\n              查看详情\n            ")])],1):a("span",[e._v("-")])]}}])})],1)],1),a("a-modal",{attrs:{title:"审核拒绝",footer:null,width:"500px"},model:{value:e.showRejectModal,callback:function(t){e.showRejectModal=t},expression:"showRejectModal"}},[a("div",{staticClass:"reject-modal"},[a("a-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{message:"请填写拒绝原因",type:"warning","show-icon":""}}),a("a-form",{attrs:{layout:"vertical"}},[a("a-form-item",{attrs:{label:"拒绝原因",required:""}},[a("a-textarea",{attrs:{placeholder:"请输入拒绝原因",rows:4,maxLength:200},model:{value:e.rejectReason,callback:function(t){e.rejectReason=t},expression:"rejectReason"}})],1)],1),a("div",{staticClass:"modal-actions"},[a("a-button",{on:{click:function(t){e.showRejectModal=!1}}},[e._v("\n          取消\n        ")]),a("a-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"danger",loading:e.rejecting,disabled:!e.rejectReason.trim()},on:{click:e.confirmReject}},[e._v("\n          确认拒绝\n        ")])],1)],1)]),a("a-modal",{attrs:{title:"提现申请详情",footer:null,width:"600px"},model:{value:e.showDetailModal,callback:function(t){e.showDetailModal=t},expression:"showDetailModal"}},[e.currentRecord?a("div",{staticClass:"detail-modal"},[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"申请ID"}},[e._v("\n          "+e._s(e.currentRecord.id)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"用户名"}},[e._v("\n          "+e._s(e.currentRecord.username)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"提现金额"}},[a("span",{staticClass:"amount-text"},[e._v("¥"+e._s(e.formatNumber(e.currentRecord.withdrawal_amount)))])]),a("a-descriptions-item",{attrs:{label:"申请状态"}},[a("a-tag",{attrs:{color:e.getStatusColor(e.currentRecord.status)}},[e._v("\n            "+e._s(e.getStatusText(e.currentRecord.status,e.currentRecord.review_remark))+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"真实姓名"}},[e._v("\n          "+e._s(e.currentRecord.alipay_name)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"支付宝账号"}},[e._v("\n          "+e._s(e.currentRecord.alipay_account)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"申请时间"}},[e._v("\n          "+e._s(e.currentRecord.apply_time?e.formatDateTime(e.currentRecord.apply_time):"-")+"\n        ")]),a("a-descriptions-item",{attrs:{label:"审核时间"}},[e._v("\n          "+e._s(e.currentRecord.review_time?e.formatDateTime(e.currentRecord.review_time):"-")+"\n        ")]),e.currentRecord.review_by?a("a-descriptions-item",{attrs:{label:"审核人"}},[e._v("\n          "+e._s(e.currentRecord.review_by)+"\n        ")]):e._e(),e.currentRecord.review_remark?a("a-descriptions-item",{attrs:{label:"审核备注"}},[e._v("\n          "+e._s(e.currentRecord.review_remark)+"\n        ")]):e._e()],1)],1):e._e()])],1)},i=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"page-header"},[a("h2",[e._v("提现管理")]),a("p",[e._v("管理用户提现申请，审核通过或拒绝申请")])])}],n=a("a34a"),s=a.n(n);function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){c(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){d(n,r,i,s,o,"next",e)}function o(e){d(n,r,i,s,o,"throw",e)}s(void 0)}))}}var m={name:"AigcWithdrawalList",data:function(){return{loading:!1,searchForm:{status:void 0,dateRange:[],username:"",alipayInfo:""},dataSource:[],pagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e){return"共 ".concat(e," 条记录")}},columns:[{title:"用户信息",key:"userInfo",width:150,scopedSlots:{customRender:"userInfo"}},{title:"提现金额",key:"amount",width:120,align:"right",scopedSlots:{customRender:"amount"}},{title:"支付宝信息",key:"alipayInfo",width:180,scopedSlots:{customRender:"alipayInfo"}},{title:"申请时间",dataIndex:"apply_time",key:"applyTime",width:150,scopedSlots:{customRender:"applyTime"}},{title:"审核时间",dataIndex:"review_time",key:"reviewTime",width:150,scopedSlots:{customRender:"reviewTime"}},{title:"状态",dataIndex:"status",key:"status",width:100,scopedSlots:{customRender:"status"}},{title:"操作",key:"action",width:280,fixed:"right",scopedSlots:{customRender:"action"}}],showRejectModal:!1,showDetailModal:!1,currentRecord:null,rejectReason:"",rejecting:!1}},mounted:function(){this.loadData()},methods:{loadData:function(){var e=u(s.a.mark((function e(){var t,a,r,i,n;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.loading=!0,t=l({current:this.pagination.current,size:this.pagination.pageSize},this.getSearchParams()),e.next=5,this.$http.get("/api/usercenter/admin/withdrawalList",{params:t});case 5:a=e.sent,r=a.data||a,r&&r.success?(this.dataSource=r.result.records||[],this.pagination.total=r.result.total||0,this.dataSource[0]&&(i=this.dataSource[0],Object.keys(i).forEach((function(e){})))):(n=r&&r.message||"获取数据失败",this.$message.error(n),this.dataSource=[],this.pagination.total=0),e.next=16;break;case 12:e.prev=12,e.t0=e["catch"](0),this.$message.error("加载数据失败");case 16:return e.prev=16,this.loading=!1,e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[0,12,16,19]])})));function t(){return e.apply(this,arguments)}return t}(),getSearchParams:function(){var e={};return void 0!==this.searchForm.status&&(e.status=this.searchForm.status),this.searchForm.username&&(e.username=this.searchForm.username.trim()),this.searchForm.alipayInfo&&(e.alipayInfo=this.searchForm.alipayInfo.trim()),this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(e.startDate=this.searchForm.dateRange[0].format("YYYY-MM-DD"),e.endDate=this.searchForm.dateRange[1].format("YYYY-MM-DD")),e},handleSearch:function(){this.pagination.current=1,this.loadData()},handleReset:function(){this.searchForm={status:void 0,dateRange:[],username:"",alipayInfo:""},this.pagination.current=1,this.loadData()},handleTableChange:function(e){this.pagination=l(l({},this.pagination),e),this.loadData()},handleApprove:function(){var e=u(s.a.mark((function e(t){var a=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm({title:"确认审核通过",content:"确定要审核通过用户 ".concat(t.username," 的提现申请吗？\n提现金额：¥").concat(this.formatNumber(t.withdrawal_amount)),onOk:function(){var e=u(s.a.mark((function e(){var r,i;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,a.$set(t,"approving",!0),e.next=4,a.$http.post("/api/usercenter/admin/approveWithdrawal",{id:t.id});case 4:r=e.sent,i=r.data||r,i.success?(a.$message.success("审核通过成功"),a.loadData()):a.$message.error(i.message||"审核失败"),e.next=13;break;case 9:e.prev=9,e.t0=e["catch"](0),a.$message.error("审核失败，请重试");case 13:return e.prev=13,a.$set(t,"approving",!1),e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[0,9,13,16]])})));function r(){return e.apply(this,arguments)}return r}()});case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleReject:function(e){this.currentRecord=e,this.rejectReason="",this.showRejectModal=!0},confirmReject:function(){var e=u(s.a.mark((function e(){var t,a;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.rejectReason.trim()){e.next=3;break}return this.$message.warning("请填写拒绝原因"),e.abrupt("return");case 3:return e.prev=3,this.rejecting=!0,e.next=7,this.$http.post("/api/usercenter/admin/rejectWithdrawal",{id:this.currentRecord.id,reason:this.rejectReason.trim()});case 7:t=e.sent,a=t.data||t,a.success?(this.$message.success("审核拒绝成功"),this.showRejectModal=!1,this.loadData()):this.$message.error(a.message||"审核失败"),e.next=16;break;case 12:e.prev=12,e.t0=e["catch"](3),this.$message.error("审核失败，请重试");case 16:return e.prev=16,this.rejecting=!1,e.finish(16);case 19:case"end":return e.stop()}}),e,this,[[3,12,16,19]])})));function t(){return e.apply(this,arguments)}return t}(),handleViewDetail:function(e){this.currentRecord=e,this.showDetailModal=!0},getStatusColor:function(e){var t={1:"orange",2:"green",3:"red",4:"gray"};return t[e]||"volcano"},getStatusText:function(e,t){var a={1:"待审核",2:"已发放",3:"审核拒绝",4:"已取消"},r=a[e]||"未知状态";return 3===e&&t&&(r+="（".concat(t,"）")),r},formatNumber:function(e){return e?parseFloat(e).toFixed(2):"0.00"},formatDateTime:function(e){if(!e)return"-";try{var t=new Date(e);return t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}}}},p=m,h=(a("155d"),a("2877")),f=Object(h["a"])(p,r,i,!1,null,"e299c5fe",null);t["default"]=f.exports},"12bc":function(e,t,a){},"155d":function(e,t,a){"use strict";var r=a("94d2"),i=a.n(r);i.a},2418:function(e,t,a){},"2b44":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-video-tutorial-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("7bf4"),s={name:"AigcVideoTutorialModal",components:{AigcVideoTutorialForm:n["default"]},data:function(){return{title:"操作",width:896,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("5672"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"3b54e72b",null);t["default"]=c.exports},"2f42":function(e,t,a){},"33d4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-video-teacher-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("aff5"),s={name:"AigcVideoTeacherModal",components:{AigcVideoTeacherForm:n["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},3491:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否系列视频"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否系列视频",dictCode:"isTrue"},model:{value:e.queryParam.isseries,callback:function(t){e.$set(e.queryParam,"isseries",t)},expression:"queryParam.isseries"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"系列名称"}},[a("a-input",{attrs:{placeholder:"请输入系列名称"},model:{value:e.queryParam.seriesname,callback:function(t){e.$set(e.queryParam,"seriesname",t)},expression:"queryParam.seriesname"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择讲师",dictCode:"aigc_video_teacher,teachername,id"},model:{value:e.queryParam.teacher,callback:function(t){e.$set(e.queryParam,"teacher",t)},expression:"queryParam.teacher"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"点击量"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.clicknum_begin,callback:function(t){e.$set(e.queryParam,"clicknum_begin",t)},expression:"queryParam.clicknum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.clicknum_end,callback:function(t){e.$set(e.queryParam,"clicknum_end",t)},expression:"queryParam.clicknum_end"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"标题"}},[a("a-input",{attrs:{placeholder:"请输入标题"},model:{value:e.queryParam.titile,callback:function(t){e.$set(e.queryParam,"titile",t)},expression:"queryParam.titile"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"设置等级"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择设置等级",dictCode:"setLevel"},model:{value:e.queryParam.setlevel,callback:function(t){e.$set(e.queryParam,"setlevel",t)},expression:"queryParam.setlevel"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"课程标签"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.tag_begin,callback:function(t){e.$set(e.queryParam,"tag_begin",t)},expression:"queryParam.tag_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.tag_end,callback:function(t){e.$set(e.queryParam,"tag_end",t)},expression:"queryParam.tag_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"上传日期"}},[a("j-date",{staticClass:"query-group-cust",attrs:{placeholder:"请选择开始日期"},model:{value:e.queryParam.uptime_begin,callback:function(t){e.$set(e.queryParam,"uptime_begin",t)},expression:"queryParam.uptime_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("j-date",{staticClass:"query-group-cust",attrs:{placeholder:"请选择结束日期"},model:{value:e.queryParam.uptime_end,callback:function(t){e.$set(e.queryParam,"uptime_end",t)},expression:"queryParam.uptime_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("视频教程")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-video-tutorial-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("b7b2"),l=(a("89f2"),{name:"AigcVideoTutorialList",mixins:[s["a"],n["b"]],components:{AigcVideoTutorialModal:o["default"]},data:function(){return{description:"视频教程管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"是否系列视频",align:"center",dataIndex:"isseries_dictText"},{title:"系列名称",align:"center",dataIndex:"seriesname"},{title:"视频文件",align:"center",dataIndex:"videofile",scopedSlots:{customRender:"fileSlot"}},{title:"讲师",align:"center",dataIndex:"teacher_dictText"},{title:"点击量",align:"center",dataIndex:"clicknum"},{title:"标题",align:"center",dataIndex:"titile"},{title:"设置等级",align:"center",dataIndex:"setlevel_dictText"},{title:"课程介绍",align:"center",dataIndex:"intro"},{title:"课程标签",align:"center",dataIndex:"tag_dictText"},{title:"上传日期",align:"center",dataIndex:"uptime",customRender:function(e){return e?e.length>10?e.substr(0,10):e:""}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/videotutorial/aigcVideoTutorial/list",delete:"/videotutorial/aigcVideoTutorial/delete",deleteBatch:"/videotutorial/aigcVideoTutorial/deleteBatch",exportXlsUrl:"/videotutorial/aigcVideoTutorial/exportXls",importExcelUrl:"videotutorial/aigcVideoTutorial/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"isseries",text:"是否系列视频",dictCode:"isTrue"}),e.push({type:"string",value:"seriesname",text:"系列名称",dictCode:""}),e.push({type:"string",value:"videofile",text:"视频文件",dictCode:""}),e.push({type:"string",value:"teacher",text:"讲师",dictCode:"aigc_video_teacher,teachername,id"}),e.push({type:"int",value:"clicknum",text:"点击量",dictCode:""}),e.push({type:"string",value:"titile",text:"标题",dictCode:""}),e.push({type:"string",value:"setlevel",text:"设置等级",dictCode:"setLevel"}),e.push({type:"string",value:"intro",text:"课程介绍",dictCode:""}),e.push({type:"string",value:"tag",text:"课程标签",dictCode:"tag"}),e.push({type:"date",value:"uptime",text:"上传日期"}),this.superFieldList=e}}}),c=l,d=(a("e292"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"6aaa529c",null);t["default"]=u.exports},"34d1":function(e,t,a){},"3ac8":function(e,t,a){},"3d1f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"程序类型",prop:"programType"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择程序类型",dictCode:"program_type",disabled:!!e.model.id},on:{change:e.handleProgramTypeChange},model:{value:e.model.programType,callback:function(t){e.$set(e.model,"programType",t)},expression:"model.programType"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"版本号",prop:"versionNumber"}},[a("a-input",{attrs:{placeholder:"请输入版本号，如：1.0.0"},model:{value:e.model.versionNumber,callback:function(t){e.$set(e.model,"versionNumber",t)},expression:"model.versionNumber"}},[a("a-button",{attrs:{slot:"addonAfter",disabled:!e.model.programType},on:{click:e.getSuggestedVersion},slot:"addonAfter"},[e._v("建议")])],1)],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"发布日期",prop:"releaseDate"}},[a("j-date",{attrs:{placeholder:"请选择发布日期","show-time":!0,"date-format":"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.releaseDate,callback:function(t){e.$set(e.model,"releaseDate",t)},expression:"model.releaseDate"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否最新版本",prop:"isLatest"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择",dictCode:"isTrue"},model:{value:e.model.isLatest,callback:function(t){e.$set(e.model,"isLatest",t)},expression:"model.isLatest"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择状态",dictCode:"valid_status"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"更新内容",prop:"updateContent"}},[a("a-textarea",{attrs:{rows:6,placeholder:"请输入详细的更新内容说明"},model:{value:e.model.updateContent,callback:function(t){e.$set(e.model,"updateContent",t)},expression:"model.updateContent"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"下载链接",prop:"downloadUrl"}},[a("a-input",{attrs:{placeholder:"请输入下载链接，如：https://aigcview.cn/download/desktop/v1.0.0"},model:{value:e.model.downloadUrl,callback:function(t){e.$set(e.model,"downloadUrl",t)},expression:"model.downloadUrl"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),a("c1df")),o=a.n(s),l={name:"AigcVersionControlModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,validatorRules:{programType:[{required:!0,message:"请选择程序类型!"}],versionNumber:[{required:!0,message:"请输入版本号!"},{pattern:/^\d+\.\d+\.\d+$/,message:"版本号格式不正确，请使用x.y.z格式!"},{validator:this.validateVersionNumber}],releaseDate:[{required:!0,message:"请选择发布日期!"}],updateContent:[{max:5e3,message:"更新内容不能超过5000个字符!"}],downloadUrl:[{required:!0,message:"请输入下载链接!"},{type:"url",message:"请输入正确的URL格式!"}]},url:{add:"/aigcview/versioncontrol/add",edit:"/aigcview/versioncontrol/edit",queryById:"/aigcview/versioncontrol/queryById",checkVersionExists:"/aigcview/versioncontrol/checkVersionExists",getNextSuggestedVersion:"/aigcview/versioncontrol/getNextSuggestedVersion"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){this.model=Object.assign({},e),this.visible=!0,this.model.id||(this.model.isLatest=2,this.model.status=1,this.model.releaseDate=o()().format("YYYY-MM-DD HH:mm:ss"))},close:function(){this.$refs.form.resetFields(),this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},validateVersionNumber:function(e,t,a){if(t)if(this.model.programType){var r={programType:this.model.programType,versionNumber:t,excludeId:this.model.id||""};Object(n["c"])(this.url.checkVersionExists,r).then((function(e){e.success&&e.result?a(new Error("该程序类型下版本号已存在!")):a()})).catch((function(){a()}))}else a();else a()},getSuggestedVersion:function(){var e=this;if(this.model.programType){var t={programType:this.model.programType,versionType:"patch"};Object(n["c"])(this.url.getNextSuggestedVersion,t).then((function(t){t.success?(e.model.versionNumber=t.result,e.$message.success("已自动填入建议版本号")):e.$message.warning("获取建议版本号失败")}))}else this.$message.warning("请先选择程序类型")},handleProgramTypeChange:function(){this.model.versionNumber=""}}},c=l,d=(a("b703"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"007a83d0",null);t["default"]=u.exports},"3dbb":function(e,t,a){},5672:function(e,t,a){"use strict";var r=a("12bc"),i=a.n(r);i.a},"6bf5":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"程序类型"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择程序类型",dictCode:"program_type"},model:{value:e.queryParam.programType,callback:function(t){e.$set(e.queryParam,"programType",t)},expression:"queryParam.programType"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"版本号"}},[a("a-input",{attrs:{placeholder:"请输入版本号"},model:{value:e.queryParam.versionNumber,callback:function(t){e.$set(e.queryParam,"versionNumber",t)},expression:"queryParam.versionNumber"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否最新"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择",dictCode:"isTrue"},model:{value:e.queryParam.isLatest,callback:function(t){e.$set(e.queryParam,"isLatest",t)},expression:"queryParam.isLatest"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{display:"block","white-space":"nowrap"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("程序版本控制")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1!==r.isLatest?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleSetLatest(r)}}},[e._v("设为最新")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-version-control-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("3d1f"),l={name:"AigcVersionControlList",mixins:[s["a"],n["b"]],components:{AigcVersionControlModal:o["default"]},data:function(){return{description:"程序版本控制管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"程序类型",align:"center",dataIndex:"programType",customRender:function(e){var t={frontend:"前端",backend:"后端",desktop:"桌面应用",plugin:"扣子插件"};return t[e]||e}},{title:"版本号",align:"center",dataIndex:"versionNumber"},{title:"更新内容",align:"center",dataIndex:"updateContent",width:200,customRender:function(e){return e?e.length>50?e.substring(0,50)+"...":e:"-"}},{title:"下载链接",align:"center",dataIndex:"downloadUrl",width:150,customRender:function(e){return e?'<a href="'.concat(e,'" target="_blank" style="color: #1890ff;">下载</a>'):"-"}},{title:"发布日期",align:"center",dataIndex:"releaseDate",customRender:function(e){return e?e.length>10?e.substr(0,10):e:""}},{title:"是否最新",align:"center",dataIndex:"isLatest",customRender:function(e){return 1===e?"是":"否"}},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){return 1===e?"启用":"禁用"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/aigcview/versioncontrol/list",delete:"/aigcview/versioncontrol/delete",deleteBatch:"/aigcview/versioncontrol/deleteBatch",exportXlsUrl:"/aigcview/versioncontrol/exportXls",importExcelUrl:"aigcview/versioncontrol/importExcel",setLatest:"/aigcview/versioncontrol/setAsLatest"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"programType",text:"程序类型",dictCode:"program_type"}),e.push({type:"string",value:"versionNumber",text:"版本号"}),e.push({type:"string",value:"updateContent",text:"更新内容"}),e.push({type:"date",value:"releaseDate",text:"发布日期"}),e.push({type:"int",value:"isLatest",text:"是否最新版本",dictCode:"isTrue"}),e.push({type:"int",value:"status",text:"状态",dictCode:"valid_status"}),this.superFieldList=e},handleSetLatest:function(e){var t=this;this.$confirm({title:"确认操作",content:"确定要将此版本设为最新版本吗？",onOk:function(){t.doSetLatest(e.id)}})},doSetLatest:function(e){var t=this;this.$http.put(this.url.setLatest,{},{params:{id:e}}).then((function(e){e.success?(t.$message.success(e.message),t.loadData()):t.$message.warning(e.message)})).catch((function(e){t.$message.error("设置最新版本失败，请稍后重试")}))}}},c=l,d=(a("d26c"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"fcaf148a",null);t["default"]=u.exports},"6dba":function(e,t,a){"use strict";var r=a("3ac8"),i=a.n(r);i.a},"727c":function(e,t,a){},7331:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-video-teacher-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("aff5"),s={name:"AigcVideoTeacherModal",components:{AigcVideoTeacherForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("8d93"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"3e23bd8c",null);t["default"]=c.exports},7695:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-home-carousel-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],n=a("b35e"),s={name:"AigcHomeCarouselModal",components:{AigcHomeCarouselForm:n["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=s,l=(a("78d6"),a("2877")),c=Object(l["a"])(o,r,i,!1,null,"323fd63d",null);t["default"]=c.exports},"78d6":function(e,t,a){"use strict";var r=a("9bce"),i=a.n(r);i.a},"7bf4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否系列视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"isseries"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"isTrue",placeholder:"请选择是否系列视频"},on:{change:e.handleSeriesChange},model:{value:e.model.isseries,callback:function(t){e.$set(e.model,"isseries",t)},expression:"model.isseries"}})],1)],1),1==e.model.isseries?a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"系列名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"seriesname"}},[a("a-input",{attrs:{placeholder:"请输入系列名称"},model:{value:e.model.seriesname,callback:function(t){e.$set(e.model,"seriesname",t)},expression:"model.seriesname"}})],1)],1):e._e()],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"视频链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"videoUrl"}},[a("a-input",{attrs:{placeholder:"请输入视频链接"},model:{value:e.model.videoUrl,callback:function(t){e.$set(e.model,"videoUrl",t)},expression:"model.videoUrl"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"讲师",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teacher"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"aigc_video_teacher,teachername,id",placeholder:"请选择讲师"},model:{value:e.model.teacher,callback:function(t){e.$set(e.model,"teacher",t)},expression:"model.teacher"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"titile"}},[a("a-input",{attrs:{placeholder:"请输入标题"},model:{value:e.model.titile,callback:function(t){e.$set(e.model,"titile",t)},expression:"model.titile"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"设置等级",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"setlevel"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"setLevel",placeholder:"请选择设置等级"},model:{value:e.model.setlevel,callback:function(t){e.$set(e.model,"setlevel",t)},expression:"model.setlevel"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"上传日期",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"uptime"}},[a("j-date",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择上传日期"},model:{value:e.model.uptime,callback:function(t){e.$set(e.model,"uptime",t)},expression:"model.uptime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"视频文件",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"videofile"}},[a("j-upload",{attrs:{disabled:""},model:{value:e.model.videofile,callback:function(t){e.$set(e.model,"videofile",t)},expression:"model.videofile"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("此字段已禁用")])],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"点击量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"clicknum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入点击量",disabled:""},model:{value:e.model.clicknum,callback:function(t){e.$set(e.model,"clicknum",t)},expression:"model.clicknum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("此字段已禁用")])],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程标签",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"tag"}},[a("j-multi-select-tag",{attrs:{type:"checkbox",dictCode:"tag",placeholder:"请选择课程标签"},model:{value:e.model.tag,callback:function(t){e.$set(e.model,"tag",t)},expression:"model.tag"}})],1)],1)],1),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"intro"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入课程介绍"},model:{value:e.model.intro,callback:function(t){e.$set(e.model,"intro",t)},expression:"model.intro"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea");function s(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function o(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?s(Object(a),!0).forEach((function(t){l(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function l(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var c={name:"AigcVideoTutorialForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{uptime:this.getCurrentDate(),isseries:2,clicknum:0},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{titile:[{required:!0,message:"请输入标题",trigger:"blur"}],videoUrl:[{required:!0,message:"请输入视频链接",trigger:"blur"},{type:"url",message:"请输入正确的URL格式",trigger:"blur"}],teacher:[{required:!0,message:"请选择讲师",trigger:"change"}],uptime:[{required:!0,message:"请选择上传日期",trigger:"change"}]},url:{add:"/videotutorial/aigcVideoTutorial/add",edit:"/videotutorial/aigcVideoTutorial/edit",queryById:"/videotutorial/aigcVideoTutorial/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{getCurrentDate:function(){var e=new Date,t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0");return"".concat(t,"-").concat(a,"-").concat(r)},handleSeriesChange:function(e){2==e&&(this.model.seriesname="")},add:function(){var e={uptime:this.getCurrentDate(),isseries:2,clicknum:0};this.edit(e)},edit:function(e){this.model=Object.assign({},e),this.model.id||this.model.uptime||(this.model.uptime=this.getCurrentDate()),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var s=o({},e.model);2==s.isseries&&(s.seriesname=""),Object(n["h"])(r,s,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}},d=c,u=a("2877"),m=Object(u["a"])(d,r,i,!1,null,null,null);t["default"]=m.exports},"8d93":function(e,t,a){"use strict";var r=a("34d1"),i=a.n(r);i.a},"94d2":function(e,t,a){},"9bce":function(e,t,a){},a214:function(e,t,a){"use strict";var r=a("727c"),i=a.n(r);i.a},aa09:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师名"}},[a("a-input",{attrs:{placeholder:"请输入讲师名"},model:{value:e.queryParam.teachername,callback:function(t){e.$set(e.queryParam,"teachername",t)},expression:"queryParam.teachername"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"讲师介绍"}},[a("a-input",{attrs:{placeholder:"请输入讲师介绍"},model:{value:e.queryParam.teacherinfo,callback:function(t){e.$set(e.queryParam,"teacherinfo",t)},expression:"queryParam.teacherinfo"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"学习人数"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.studyperson_begin,callback:function(t){e.$set(e.queryParam,"studyperson_begin",t)},expression:"queryParam.studyperson_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.studyperson_end,callback:function(t){e.$set(e.queryParam,"studyperson_end",t)},expression:"queryParam.studyperson_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"课程数量"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.coursenum_begin,callback:function(t){e.$set(e.queryParam,"coursenum_begin",t)},expression:"queryParam.coursenum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.coursenum_end,callback:function(t){e.$set(e.queryParam,"coursenum_end",t)},expression:"queryParam.coursenum_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("视频讲师")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-video-teacher-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("33d4"),l={name:"AigcVideoTeacherList",mixins:[s["a"],n["b"]],components:{AigcVideoTeacherModal:o["default"]},data:function(){return{description:"视频讲师管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"讲师名",align:"center",dataIndex:"teachername"},{title:"讲师介绍",align:"center",dataIndex:"teacherinfo"},{title:"学习人数",align:"center",dataIndex:"studyperson"},{title:"课程数量",align:"center",dataIndex:"coursenum"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/videoteacher/aigcVideoTeacher/list",delete:"/videoteacher/aigcVideoTeacher/delete",deleteBatch:"/videoteacher/aigcVideoTeacher/deleteBatch",exportXlsUrl:"/videoteacher/aigcVideoTeacher/exportXls",importExcelUrl:"videoteacher/aigcVideoTeacher/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"teachername",text:"讲师名",dictCode:""}),e.push({type:"string",value:"teacherinfo",text:"讲师介绍",dictCode:""}),e.push({type:"int",value:"studyperson",text:"学习人数",dictCode:""}),e.push({type:"int",value:"coursenum",text:"课程数量",dictCode:""}),this.superFieldList=e}}},c=l,d=(a("a214"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"4f35a537",null);t["default"]=u.exports},aff5:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"讲师名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teachername"}},[a("a-input",{attrs:{placeholder:"请输入讲师名"},model:{value:e.model.teachername,callback:function(t){e.$set(e.model,"teachername",t)},expression:"model.teachername"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"讲师介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"teacherinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入讲师介绍"},model:{value:e.model.teacherinfo,callback:function(t){e.$set(e.model,"teacherinfo",t)},expression:"model.teacherinfo"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"学习人数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"studyperson"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入学习人数"},model:{value:e.model.studyperson,callback:function(t){e.$set(e.model,"studyperson",t)},expression:"model.studyperson"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"课程数量",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"coursenum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入课程数量"},model:{value:e.model.coursenum,callback:function(t){e.$set(e.model,"coursenum",t)},expression:"model.coursenum"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),{name:"AigcVideoTeacherForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{teachername:[{required:!0,message:"请输入讲师名!"}],teacherinfo:[{required:!0,message:"请输入讲师介绍!"}]},url:{add:"/videoteacher/aigcVideoTeacher/add",edit:"/videoteacher/aigcVideoTeacher/edit",queryById:"/videoteacher/aigcVideoTeacher/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},b2e3:function(e,t,a){},b35e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图标题",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"title"}},[a("a-input",{attrs:{placeholder:"请输入轮播图标题"},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图描述",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"description"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入轮播图描述"},model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"轮播图",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"imageUrl"}},[a("j-image-upload",{attrs:{isMultiple:""},model:{value:e.model.imageUrl,callback:function(t){e.$set(e.model,"imageUrl",t)},expression:"model.imageUrl"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"标签文字",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"badge"}},[a("a-input",{attrs:{placeholder:"请输入标签文字"},model:{value:e.model.badge,callback:function(t){e.$set(e.model,"badge",t)},expression:"model.badge"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"按钮文字",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"buttonText"}},[a("a-input",{attrs:{placeholder:"请输入按钮文字"},model:{value:e.model.buttonText,callback:function(t){e.$set(e.model,"buttonText",t)},expression:"model.buttonText"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"按钮跳转链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"buttonLink"}},[a("a-input",{attrs:{placeholder:"请输入按钮跳转链接"},model:{value:e.model.buttonLink,callback:function(t){e.$set(e.model,"buttonLink",t)},expression:"model.buttonLink"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"排序序号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"sortOrder"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入排序序号"},model:{value:e.model.sortOrder,callback:function(t){e.$set(e.model,"sortOrder",t)},expression:"model.sortOrder"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"是否启用",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"status"}},[a("j-dict-select-tag",{attrs:{dictCode:"isTrue",placeholder:"请选择是否启用"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1)],1)],1)],1)},i=[],n=a("0fea"),s=(a("ca00"),{name:"AigcHomeCarouselForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{title:[{required:!0,message:"请输入轮播图标题!"}],imageUrl:[{required:!0,message:"请输入轮播图!"}],status:[{required:!0,message:"请输入是否启用!"}]},url:{add:"/aigc/aigcHomeCarousel/add",edit:"/aigc/aigcHomeCarousel/edit",queryById:"/aigc/aigcHomeCarousel/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(n["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},b703:function(e,t,a){"use strict";var r=a("2418"),i=a.n(r);i.a},b7b2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-video-tutorial-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("7bf4"),s={name:"AigcVideoTutorialModal",components:{AigcVideoTutorialForm:n["default"]},data:function(){return{title:"",width:1200,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},c9ab:function(e,t,a){"use strict";var r=a("3dbb"),i=a.n(r);i.a},c9ec:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"轮播图标题"}},[a("a-input",{attrs:{placeholder:"请输入轮播图标题"},model:{value:e.queryParam.title,callback:function(t){e.$set(e.queryParam,"title",t)},expression:"queryParam.title"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否启用"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否启用",dictCode:"isTrue"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("首页轮播图")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-home-carousel-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],n=(a("6eb7"),a("ac0d")),s=a("b65a"),o=a("d1d1"),l=a("89f2"),c={name:"AigcHomeCarouselList",mixins:[s["a"],n["b"]],components:{AigcHomeCarouselModal:o["default"]},data:function(){var e=this;return{description:"首页轮播图管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"轮播图标题",align:"center",dataIndex:"title"},{title:"轮播图描述",align:"center",dataIndex:"description"},{title:"轮播图",align:"center",dataIndex:"imageUrl",scopedSlots:{customRender:"imgSlot"}},{title:"标签文字",align:"center",dataIndex:"badge"},{title:"按钮文字",align:"center",dataIndex:"buttonText"},{title:"按钮跳转链接",align:"center",dataIndex:"buttonLink"},{title:"排序序号",align:"center",dataIndex:"sortOrder"},{title:"是否启用",align:"center",dataIndex:"status",customRender:function(t){return Object(l["c"])(e.dictOptions.isTrue,t)}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/aigc/aigcHomeCarousel/list",delete:"/aigc/aigcHomeCarousel/delete",deleteBatch:"/aigc/aigcHomeCarousel/deleteBatch",exportXlsUrl:"/aigc/aigcHomeCarousel/exportXls",importExcelUrl:"aigc/aigcHomeCarousel/importExcel"},dictOptions:{isTrue:[]},superFieldList:[]}},created:function(){this.getSuperFieldList(),this.initDictConfig()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){var e=this;Object(l["d"])("isTrue").then((function(t){t.success&&(e.dictOptions.isTrue=t.result)}))},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"title",text:"轮播图标题",dictCode:""}),e.push({type:"string",value:"description",text:"轮播图描述",dictCode:""}),e.push({type:"string",value:"imageUrl",text:"轮播图",dictCode:""}),e.push({type:"string",value:"badge",text:"标签文字",dictCode:""}),e.push({type:"string",value:"buttonText",text:"按钮文字",dictCode:""}),e.push({type:"string",value:"buttonLink",text:"按钮跳转链接",dictCode:""}),e.push({type:"int",value:"sortOrder",text:"排序序号",dictCode:""}),e.push({type:"string",value:"status",text:"是否启用",dictCode:"isTrue"}),this.superFieldList=e}}},d=c,u=(a("6dba"),a("2877")),m=Object(u["a"])(d,r,i,!1,null,"fdec17ca",null);t["default"]=m.exports},d1d1:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-home-carousel-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],n=a("b35e"),s={name:"AigcHomeCarouselModal",components:{AigcHomeCarouselForm:n["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=s,l=a("2877"),c=Object(l["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},d26c:function(e,t,a){"use strict";var r=a("2f42"),i=a.n(r);i.a},d600:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"user-center"},[a("a-card",{staticClass:"main-card",attrs:{title:"智界Aigc - 个人中心",bordered:!1}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"info-card",attrs:{size:"small",title:"基本信息"}},[a("div",{staticClass:"user-info"},[a("a-avatar",{staticClass:"user-avatar",attrs:{size:80,src:e.getAvatarUrl(e.userInfo.avatar),icon:"user"}}),a("div",{staticClass:"info-content"},[a("h3",[e._v(e._s(e.userProfile.nickname||e.userInfo.realname))]),a("p",[e._v("用户ID: "+e._s(e.userInfo.id))]),a("p",[e._v("注册时间: "+e._s(e._f("formatDate")(e.userInfo.createTime)))])])],1),a("a-button",{attrs:{type:"primary",block:""},on:{click:function(t){e.showEditNickname=!0}}},[e._v("\n            编辑昵称\n          ")])],1)],1),a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"balance-card",attrs:{size:"small",title:"账户余额"}},[a("div",{staticClass:"balance-info"},[a("h2",{staticClass:"balance-amount"},[e._v("¥"+e._s(e.userProfile.accountBalance||0))]),a("p",[e._v("累计消费: ¥"+e._s(e.userProfile.totalConsumption||0))]),a("p",[e._v("累计充值: ¥"+e._s(e.userProfile.totalRecharge||0))])]),a("a-button",{attrs:{type:"primary",block:""},on:{click:function(t){e.showRecharge=!0}}},[e._v("\n            充值\n          ")])],1)],1),a("a-col",{attrs:{span:8}},[a("a-card",{staticClass:"api-card",attrs:{size:"small",title:"API密钥"}},[a("div",{staticClass:"api-key-info"},[a("a-input",{staticStyle:{"margin-bottom":"8px"},attrs:{value:e.maskedApiKey,readonly:"",placeholder:"暂无API密钥"}}),a("a-button-group",{staticStyle:{width:"100%"}},[a("a-button",{staticStyle:{width:"50%"},on:{click:e.toggleApiKeyVisibility}},[e._v("\n                "+e._s(e.apiKeyVisible?"隐藏":"显示")+"\n              ")]),a("a-button",{staticStyle:{width:"50%"},on:{click:e.regenerateApiKey}},[e._v("\n                重新生成\n              ")])],1),a("p",{staticClass:"api-key-tips"},[e._v("请妥善保管您的API密钥")])],1)])],1)],1),a("a-row",{staticStyle:{"margin-top":"24px"},attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-card",{staticClass:"exchange-card",attrs:{size:"small",title:"兑换码"}},[a("a-input-search",{attrs:{placeholder:"请输入兑换码","enter-button":"兑换"},on:{search:e.useExchangeCode},model:{value:e.exchangeCode,callback:function(t){e.exchangeCode=t},expression:"exchangeCode"}}),a("div",{staticStyle:{"margin-top":"16px"}},[a("a-button",{attrs:{block:""},on:{click:function(t){e.loadUsedCodes(),e.showUsedCodes=!0}}},[e._v("\n              查看兑换记录\n            ")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-card",{staticClass:"member-card",attrs:{size:"small",title:"会员信息"}},[a("div",{staticClass:"member-info"},[a("a-tag",{staticClass:"member-tag",attrs:{color:e.getMemberColor(e.userProfile.currentRole)}},[e._v("\n              "+e._s(e.getMemberText(e.userProfile.currentRole))+"\n            ")]),e.userProfile.memberExpireTime?a("p",[e._v("\n              到期时间: "+e._s(e._f("formatDate")(e.userProfile.memberExpireTime))+"\n            ")]):a("p",[e._v("\n              永久有效\n            ")])],1)])],1)],1),a("a-card",{staticClass:"transaction-card",staticStyle:{"margin-top":"24px"},attrs:{title:"消费记录"}},[a("a-table",{attrs:{columns:e.transactionColumns,"data-source":e.transactionList,pagination:e.transactionPagination,size:"small",loading:e.transactionLoading},on:{change:e.handleTransactionTableChange},scopedSlots:e._u([{key:"transactionType",fn:function(t){return[a("a-tag",{attrs:{color:e.getTransactionTypeColor(t)}},[e._v("\n            "+e._s(e.getTransactionTypeText(t))+"\n          ")])]}},{key:"amount",fn:function(t,r){return[a("span",{class:1===r.transactionType?"text-red":"text-green"},[e._v("\n            "+e._s(1===r.transactionType?"-":"+")+"¥"+e._s(t)+"\n          ")])]}},{key:"transactionTime",fn:function(t){return[e._v("\n          "+e._s(e._f("formatDateTime")(t))+"\n        ")]}}])})],1)],1),a("a-modal",{attrs:{title:"编辑昵称",visible:e.showEditNickname,confirmLoading:e.nicknameLoading},on:{ok:e.updateNickname,cancel:e.cancelEditNickname}},[a("a-input",{attrs:{placeholder:"请输入新昵称",maxLength:20},on:{pressEnter:e.updateNickname},model:{value:e.newNickname,callback:function(t){e.newNickname=t},expression:"newNickname"}})],1),a("a-modal",{attrs:{title:"账户充值",visible:e.showRecharge,confirmLoading:e.rechargeLoading},on:{ok:e.handleRecharge,cancel:function(t){e.showRecharge=!1}}},[a("p",[e._v("请选择充值金额或输入自定义金额：")]),a("a-radio-group",{staticStyle:{"margin-bottom":"16px"},model:{value:e.rechargeAmount,callback:function(t){e.rechargeAmount=t},expression:"rechargeAmount"}},[a("a-radio-button",{attrs:{value:10}},[e._v("¥10")]),a("a-radio-button",{attrs:{value:50}},[e._v("¥50")]),a("a-radio-button",{attrs:{value:100}},[e._v("¥100")]),a("a-radio-button",{attrs:{value:500}},[e._v("¥500")])],1),a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:1e4,placeholder:"自定义金额"},model:{value:e.customAmount,callback:function(t){e.customAmount=t},expression:"customAmount"}})],1),a("a-modal",{attrs:{title:"兑换记录",visible:e.showUsedCodes,footer:null,width:"800px"},on:{cancel:function(t){e.showUsedCodes=!1}}},[a("a-table",{attrs:{columns:e.exchangeCodeColumns,"data-source":e.usedCodesList,size:"small",pagination:!1,loading:e.exchangeCodesLoading},scopedSlots:e._u([{key:"codeType",fn:function(t){return[e._v("\n        "+e._s(e.getExchangeCodeTypeText(t))+"\n      ")]}},{key:"value",fn:function(t){return[e._v("\n        ¥"+e._s(t)+"\n      ")]}},{key:"usedTime",fn:function(t){return[e._v("\n        "+e._s(e._f("formatDateTime")(t))+"\n      ")]}}])})],1)],1)},i=[],n=a("a34a"),s=a.n(n),o=a("77ea"),l=a("2f62");function c(e,t,a,r,i,n,s){try{var o=e[n](s),l=o.value}catch(c){return void a(c)}o.done?t(l):Promise.resolve(l).then(r,i)}function d(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){c(n,r,i,s,o,"next",e)}function o(e){c(n,r,i,s,o,"throw",e)}s(void 0)}))}}function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function m(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){p(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function p(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var h={name:"UserCenter",data:function(){return{userProfile:{},showEditNickname:!1,showRecharge:!1,showUsedCodes:!1,newNickname:"",exchangeCode:"",rechargeAmount:50,customAmount:null,apiKeyVisible:!1,transactionList:[],usedCodesList:[],profileLoading:!1,transactionLoading:!1,exchangeCodesLoading:!1,nicknameLoading:!1,rechargeLoading:!1,transactionPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(e,t){return"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 条")}},transactionColumns:[{title:"交易时间",dataIndex:"transactionTime",key:"transactionTime",width:150,scopedSlots:{customRender:"transactionTime"}},{title:"交易类型",dataIndex:"transactionType",key:"transactionType",width:100,scopedSlots:{customRender:"transactionType"}},{title:"金额",dataIndex:"amount",key:"amount",width:120,scopedSlots:{customRender:"amount"}},{title:"余额",dataIndex:"balanceAfter",key:"balanceAfter",width:120,render:function(e){return"¥".concat(e)}},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0}],exchangeCodeColumns:[{title:"兑换码",dataIndex:"code",key:"code",width:150},{title:"类型",dataIndex:"codeType",key:"codeType",width:80,scopedSlots:{customRender:"codeType"}},{title:"价值",dataIndex:"value",key:"value",width:100,scopedSlots:{customRender:"value"}},{title:"使用时间",dataIndex:"usedTime",key:"usedTime",width:150,scopedSlots:{customRender:"usedTime"}}]}},computed:m(m({},Object(l["c"])(["userInfo"])),{},{maskedApiKey:function(){if(!this.userProfile.apiKey)return"";if(this.apiKeyVisible)return this.userProfile.apiKey;var e=this.userProfile.apiKey;return e.substring(0,8)+"****"+e.substring(e.length-4)}}),mounted:function(){this.loadUserProfile(),this.loadTransactions()},methods:{getAvatarUrl:function(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:this.getFileAccessHttpUrl(e)||"":""},getFileAccessHttpUrl:function(e){return e?e.startsWith("http://")||e.startsWith("https://")?e:e.startsWith("uploads/")?window.getFileAccessHttpUrl?window.getFileAccessHttpUrl(e):e:this.$store.state.app.staticDomainURL+"/"+e:""},loadUserProfile:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.profileLoading=!0,e.next=4,Object(o["v"])();case 4:t=e.sent,t.success?this.userProfile=t.result:this.$message.error(t.message||"加载用户信息失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载用户信息失败");case 12:return e.prev=12,this.profileLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),loadTransactions:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.transactionLoading=!0,e.next=4,Object(o["x"])({pageNo:this.transactionPagination.current,pageSize:this.transactionPagination.pageSize});case 4:t=e.sent,t.success?(this.transactionList=t.result.records||[],this.transactionPagination.total=t.result.total||0):this.$message.error(t.message||"加载交易记录失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载交易记录失败");case 12:return e.prev=12,this.transactionLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),updateNickname:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.newNickname.trim()){e.next=3;break}return this.$message.warning("请输入昵称"),e.abrupt("return");case 3:return e.prev=3,this.nicknameLoading=!0,e.next=7,Object(o["E"])({nickname:this.newNickname});case 7:t=e.sent,t.success?(this.$message.success("昵称更新成功"),this.userProfile.nickname=this.newNickname,this.showEditNickname=!1,this.newNickname=""):this.$message.error(t.message||"昵称更新失败"),e.next=15;break;case 11:e.prev=11,e.t0=e["catch"](3),this.$message.error("昵称更新失败");case 15:return e.prev=15,this.nicknameLoading=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,this,[[3,11,15,18]])})));function t(){return e.apply(this,arguments)}return t}(),cancelEditNickname:function(){this.showEditNickname=!1,this.newNickname=""},regenerateApiKey:function(){var e=d(s.a.mark((function e(){var t=this;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm({title:"确认重新生成API密钥？",content:"重新生成后，原密钥将失效，请确保已更新相关配置",onOk:function(){var e=d(s.a.mark((function e(){var a;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["B"])();case 3:a=e.sent,a.success?(t.$message.success("API密钥重新生成成功"),t.userProfile.apiKey=a.result,t.apiKeyVisible=!0):t.$message.error(a.message||"API密钥重新生成失败"),e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),t.$message.error("API密钥重新生成失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));function a(){return e.apply(this,arguments)}return a}()});case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),toggleApiKeyVisibility:function(){this.apiKeyVisible=!this.apiKeyVisible},useExchangeCode:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.exchangeCode.trim()){e.next=3;break}return this.$message.warning("请输入兑换码"),e.abrupt("return");case 3:return e.prev=3,e.next=6,Object(o["I"])({code:this.exchangeCode});case 6:t=e.sent,t.success?(this.$message.success(t.message||"兑换成功"),this.exchangeCode="",this.loadUserProfile(),this.loadTransactions()):this.$message.error(t.message||"兑换失败"),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](3),this.$message.error("兑换失败");case 14:case"end":return e.stop()}}),e,this,[[3,10]])})));function t(){return e.apply(this,arguments)}return t}(),handleRecharge:function(){var e=this.customAmount||this.rechargeAmount;!e||e<=0?this.$message.warning("请选择或输入充值金额"):(this.$message.info("充值功能开发中，选择金额：¥".concat(e)),this.showRecharge=!1,this.customAmount=null)},loadUsedCodes:function(){var e=d(s.a.mark((function e(){var t;return s.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,this.exchangeCodesLoading=!0,e.next=4,Object(o["z"])();case 4:t=e.sent,t.success?this.usedCodesList=t.result||[]:this.$message.error(t.message||"加载兑换记录失败"),e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](0),this.$message.error("加载兑换记录失败");case 12:return e.prev=12,this.exchangeCodesLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[0,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),handleTransactionTableChange:function(e){this.transactionPagination.current=e.current,this.transactionPagination.pageSize=e.pageSize,this.loadTransactions()},getMemberColor:function(e){var t={"普通用户":"default",VIP:"gold",SVIP:"purple","管理员":"red"};return t[e]||"default"},getMemberText:function(e){return e||"普通用户"},getTransactionTypeColor:function(e){var t={1:"red",2:"green",3:"blue",4:"orange"};return t[e]||"default"},getTransactionTypeText:function(e){var t={1:"消费",2:"充值",3:"退款",4:"兑换"};return t[e]||"未知"},getExchangeCodeTypeText:function(e){var t={1:"余额",2:"会员",3:"积分"};return t[e]||"未知"}},filters:{formatDate:function(e){return e?new Date(e).toLocaleDateString("zh-CN"):""},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN"):""}}},f=h,g=(a("c9ab"),a("2877")),b=Object(g["a"])(f,r,i,!1,null,"652ee442",null);t["default"]=b.exports},e292:function(e,t,a){"use strict";var r=a("b2e3"),i=a.n(r);i.a}}]);