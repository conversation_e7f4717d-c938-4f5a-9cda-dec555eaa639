(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~f4560dfd"],{"0a27":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.queryParam.referrerId,callback:function(t){e.$set(e.queryParam,"referrerId",t)},expression:"queryParam.referrerId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"奖励类型"}},[a("a-select",{attrs:{placeholder:"请选择奖励类型",allowClear:""},model:{value:e.queryParam.rewardType,callback:function(t){e.$set(e.queryParam,"rewardType",t)},expression:"queryParam.rewardType"}},[a("a-select-option",{attrs:{value:1}},[e._v("注册奖励")]),a("a-select-option",{attrs:{value:2}},[e._v("首充奖励")]),a("a-select-option",{attrs:{value:3}},[e._v("升级奖励")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待发放")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("推荐奖励记录")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"primary",icon:"check"},on:{click:e.batchPay}},[e._v("批量发放")]):e._e(),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handlePay(r)}}},[e._v("发放奖励")])]):e._e(),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleCancel(r)}}},[e._v("取消奖励")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-referral-reward-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("6973"),s={name:"AicgUserReferralRewardList",mixins:[n["a"],l["b"]],components:{AicgUserReferralRewardModal:o["default"]},data:function(){return{description:"推荐奖励记录管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"推荐人ID",align:"center",dataIndex:"referrerId"},{title:"被推荐人ID",align:"center",dataIndex:"refereeId"},{title:"奖励类型",align:"center",dataIndex:"rewardType",customRender:function(e){var t={1:"注册奖励",2:"首充奖励",3:"升级奖励"};return t[e]||e}},{title:"奖励金额",align:"center",dataIndex:"rewardAmount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"触发事件",align:"center",dataIndex:"triggerEvent"},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){var t={1:"待发放",2:"已发放",3:"已取消"},a={1:"orange",2:"green",3:"red"};return'<span style="color: '.concat(a[e],'">').concat(t[e]||e,"</span>")},scopedSlots:{customRender:"htmlSlot"}},{title:"发放时间",align:"center",dataIndex:"rewardTime"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/referralreward/list",delete:"/demo/referralreward/delete",deleteBatch:"/demo/referralreward/deleteBatch",exportXlsUrl:"/demo/referralreward/exportXls",importExcelUrl:"demo/referralreward/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"referrerId",text:"推荐人ID"}),e.push({type:"string",value:"refereeId",text:"被推荐人ID"}),e.push({type:"int",value:"rewardType",text:"奖励类型"}),e.push({type:"BigDecimal",value:"rewardAmount",text:"奖励金额"}),e.push({type:"string",value:"triggerEvent",text:"触发事件"}),e.push({type:"int",value:"status",text:"状态"}),e.push({type:"Date",value:"rewardTime",text:"发放时间"}),this.superFieldList=e},handlePay:function(e){var t=this;this.$confirm({title:"发放奖励",content:"确定要发放 ¥".concat(e.rewardAmount," 的奖励吗？"),onOk:function(){t.$http.post("/demo/referralreward/pay?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("发放成功"),t.loadData()):t.$message.error(e.message)}))}})},handleCancel:function(e){var t=this;this.$confirm({title:"取消奖励",content:"确定要取消该奖励吗？",onOk:function(){t.$http.post("/demo/referralreward/cancel?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("取消成功"),t.loadData()):t.$message.error(e.message)}))}})},batchPay:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"批量发放奖励",content:"确定要发放选中的 ".concat(this.selectedRowKeys.length," 条奖励吗？"),onOk:function(){e.$http.post("/demo/referralreward/batchPay?ids=".concat(e.selectedRowKeys.join(","))).then((function(t){t.success?(e.$message.success(t.message),e.loadData(),e.onClearSelected()):e.$message.error(t.message)}))}}):this.$message.warning("请选择要发放的奖励")}}},c=s,d=(a("463f"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"69602dd8",null);t["default"]=u.exports},"0b61":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"会员等级"}},[a("a-select",{attrs:{placeholder:"请选择会员等级",allowClear:""},model:{value:e.queryParam.memberLevel,callback:function(t){e.$set(e.queryParam,"memberLevel",t)},expression:"queryParam.memberLevel"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通用户")]),a("a-select-option",{attrs:{value:2}},[e._v("VIP会员")]),a("a-select-option",{attrs:{value:3}},[e._v("SVIP会员")]),a("a-select-option",{attrs:{value:4}},[e._v("至尊会员")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("生效中")]),a("a-select-option",{attrs:{value:2}},[e._v("已过期")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("会员订阅历史")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"status",fn:function(t){return[a("a-tag",{attrs:{color:1===t?"green":2===t?"red":"orange"}},[e._v("\n          "+e._s(1===t?"生效中":2===t?"已过期":3===t?"已取消":t)+"\n        ")])]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleCancel(r)}}},[e._v("取消订阅")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-membership-history-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("192a"),s={name:"AicgUserMembershipHistoryList",mixins:[n["a"],l["b"]],components:{AicgUserMembershipHistoryModal:o["default"]},data:function(){return{description:"用户会员订阅历史管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"userId"},{title:"订单ID",align:"center",dataIndex:"orderId"},{title:"会员等级",align:"center",dataIndex:"memberLevel",customRender:function(e){var t={1:"普通用户",2:"VIP会员",3:"SVIP会员",4:"至尊会员"};return t[e]||e}},{title:"订阅时长(月)",align:"center",dataIndex:"durationMonths"},{title:"订阅金额",align:"center",dataIndex:"amount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"开始时间",align:"center",dataIndex:"startTime"},{title:"结束时间",align:"center",dataIndex:"endTime"},{title:"状态",align:"center",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/membershiphistory/list",delete:"/demo/membershiphistory/delete",deleteBatch:"/demo/membershiphistory/deleteBatch",exportXlsUrl:"/demo/membershiphistory/exportXls",importExcelUrl:"demo/membershiphistory/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"userId",text:"用户ID"}),e.push({type:"string",value:"orderId",text:"订单ID"}),e.push({type:"int",value:"memberLevel",text:"会员等级"}),e.push({type:"int",value:"durationMonths",text:"订阅时长(月)"}),e.push({type:"BigDecimal",value:"amount",text:"订阅金额"}),e.push({type:"Date",value:"startTime",text:"开始时间"}),e.push({type:"Date",value:"endTime",text:"结束时间"}),e.push({type:"int",value:"status",text:"状态"}),this.superFieldList=e},handleCancel:function(e){var t=this;this.$confirm({title:"确认取消",content:"确定要取消该会员订阅吗？",onOk:function(){t.$http.post("/demo/membershiphistory/cancel?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("取消成功"),t.loadData()):t.$message.error(e.message)}))}})}}},c=s,d=(a("84f8"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"4dc07baf",null);t["default"]=u.exports},"192a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"订单ID",prop:"orderId"}},[a("a-input",{attrs:{placeholder:"请输入订单ID"},model:{value:e.model.orderId,callback:function(t){e.$set(e.model,"orderId",t)},expression:"model.orderId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"升级前角色",prop:"fromRoleId"}},[a("a-select",{attrs:{placeholder:"请选择升级前角色",allowClear:""},model:{value:e.model.fromRoleId,callback:function(t){e.$set(e.model,"fromRoleId",t)},expression:"model.fromRoleId"}},e._l(e.roleOptions,(function(t){return a("a-select-option",{key:t.id,attrs:{value:t.id}},[e._v("\n                "+e._s(t.roleName)+"\n              ")])})),1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"升级后角色",prop:"toRoleId"}},[a("a-select",{attrs:{placeholder:"请选择升级后角色",allowClear:""},model:{value:e.model.toRoleId,callback:function(t){e.$set(e.model,"toRoleId",t)},expression:"model.toRoleId"}},e._l(e.roleOptions,(function(t){return a("a-select-option",{key:t.id,attrs:{value:t.id}},[e._v("\n                "+e._s(t.roleName)+"\n              ")])})),1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"会员等级",prop:"memberLevel"}},[a("a-select",{attrs:{placeholder:"请选择会员等级"},model:{value:e.model.memberLevel,callback:function(t){e.$set(e.model,"memberLevel",t)},expression:"model.memberLevel"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通用户")]),a("a-select-option",{attrs:{value:2}},[e._v("VIP会员")]),a("a-select-option",{attrs:{value:3}},[e._v("SVIP会员")]),a("a-select-option",{attrs:{value:4}},[e._v("至尊会员")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"订阅时长(月)",prop:"durationMonths"}},[a("a-input-number",{attrs:{min:1,max:120,placeholder:"请输入订阅时长"},model:{value:e.model.durationMonths,callback:function(t){e.$set(e.model,"durationMonths",t)},expression:"model.durationMonths"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"订阅金额",prop:"amount"}},[a("a-input-number",{attrs:{min:0,precision:2,placeholder:"请输入订阅金额"},model:{value:e.model.amount,callback:function(t){e.$set(e.model,"amount",t)},expression:"model.amount"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("生效中")]),a("a-select-option",{attrs:{value:2}},[e._v("已过期")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"开始时间",prop:"startTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择开始时间"},model:{value:e.model.startTime,callback:function(t){e.$set(e.model,"startTime",t)},expression:"model.startTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"结束时间",prop:"endTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择结束时间"},model:{value:e.model.endTime,callback:function(t){e.$set(e.model,"endTime",t)},expression:"model.endTime"}})],1)],1)],1)],1)],1)],1)},i=[],l=a("0fea"),n=a("c1df"),o=a.n(n),s={name:"AicgUserMembershipHistoryModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),roleOptions:[],validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],toRoleId:[{required:!0,message:"请选择升级后角色!"}],memberLevel:[{required:!0,message:"请选择会员等级!"}],durationMonths:[{required:!0,message:"请输入订阅时长!"}],amount:[{required:!0,message:"请输入订阅金额!"}],startTime:[{required:!0,message:"请选择开始时间!"}],endTime:[{required:!0,message:"请选择结束时间!"}]},url:{add:"/demo/membershiphistory/add",edit:"/demo/membershiphistory/edit",queryById:"/demo/membershiphistory/queryById"}}},created:function(){this.loadRoleOptions()},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,orderId:t.model.orderId,fromRoleId:t.model.fromRoleId,toRoleId:t.model.toRoleId,memberLevel:t.model.memberLevel,durationMonths:t.model.durationMonths,amount:t.model.amount,status:t.model.status};t.form.setFieldsValue(e),t.model.startTime&&(t.model.startTime=o()(t.model.startTime)),t.model.endTime&&(t.model.endTime=o()(t.model.endTime))})),e.id?this.title="编辑":this.title="新增"},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var n=Object.assign(e.model);n.startTime&&(n.startTime=n.startTime.format("YYYY-MM-DD HH:mm:ss")),n.endTime&&(n.endTime=n.endTime.format("YYYY-MM-DD HH:mm:ss")),Object(l["h"])(r,n,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},loadRoleOptions:function(){var e=this;Object(l["c"])("/sys/role/list").then((function(t){t.success&&(e.roleOptions=t.result.records||[])}))}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},"1a05":function(e,t,a){},"1de8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐人ID",prop:"referrerId"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.model.referrerId,callback:function(t){e.$set(e.model,"referrerId",t)},expression:"model.referrerId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"被推荐人ID",prop:"refereeId"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.model.refereeId,callback:function(t){e.$set(e.model,"refereeId",t)},expression:"model.refereeId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐码",prop:"referralCode"}},[a("a-input",{attrs:{placeholder:"请输入推荐码"},model:{value:e.model.referralCode,callback:function(t){e.$set(e.model,"referralCode",t)},expression:"model.referralCode"}},[a("a-button",{attrs:{slot:"suffix"},on:{click:e.generateCode},slot:"suffix"},[e._v("生成")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待确认")]),a("a-select-option",{attrs:{value:2}},[e._v("已确认")]),a("a-select-option",{attrs:{value:3}},[e._v("已奖励")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"注册时间",prop:"registerTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择注册时间"},model:{value:e.model.registerTime,callback:function(t){e.$set(e.model,"registerTime",t)},expression:"model.registerTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"首次充值时间",prop:"firstRechargeTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择首次充值时间"},model:{value:e.model.firstRechargeTime,callback:function(t){e.$set(e.model,"firstRechargeTime",t)},expression:"model.firstRechargeTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"首次充值金额",prop:"firstRechargeAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入首次充值金额"},model:{value:e.model.firstRechargeAmount,callback:function(t){e.$set(e.model,"firstRechargeAmount",t)},expression:"model.firstRechargeAmount"}})],1)],1)],1)],1)],1)],1)},i=[],l=a("0fea"),n=a("c1df"),o=a.n(n),s={name:"AicgUserReferralModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{referrerId:[{required:!0,message:"请输入推荐人ID!"}],refereeId:[{required:!0,message:"请输入被推荐人ID!"}],referralCode:[{required:!0,message:"请输入推荐码!"}],registerTime:[{required:!0,message:"请选择注册时间!"}]},url:{add:"/demo/referral/add",edit:"/demo/referral/edit",queryById:"/demo/referral/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={referrerId:t.model.referrerId,refereeId:t.model.refereeId,referralCode:t.model.referralCode,status:t.model.status,firstRechargeAmount:t.model.firstRechargeAmount};t.form.setFieldsValue(e),t.model.registerTime&&(t.model.registerTime=o()(t.model.registerTime)),t.model.firstRechargeTime&&(t.model.firstRechargeTime=o()(t.model.firstRechargeTime))})),e.id?this.title="编辑":(this.title="新增",this.model.registerTime=o()(),this.model.status=1)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var n=Object.assign(e.model);n.registerTime&&(n.registerTime=n.registerTime.format("YYYY-MM-DD HH:mm:ss")),n.firstRechargeTime&&(n.firstRechargeTime=n.firstRechargeTime.format("YYYY-MM-DD HH:mm:ss")),Object(l["h"])(r,n,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},generateCode:function(){var e=this;this.model.referrerId?Object(l["c"])("/demo/referral/generateCode?userId=".concat(this.model.referrerId)).then((function(t){t.success?(e.model.referralCode=t.result,e.$message.success("推荐码生成成功")):e.$message.error(t.message)})):this.$message.warning("请先输入推荐人ID")}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},"24e6":function(e,t,a){},"266d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-plub-author-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],l=a("d901"),n={name:"AigcPlubAuthorModal",components:{AigcPlubAuthorForm:l["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=n,s=a("2877"),c=Object(s["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},"31bf":function(e,t,a){},3450:function(e,t,a){"use strict";var r=a("1a05"),i=a.n(r);i.a},"384e":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户昵称"}},[a("a-input",{attrs:{placeholder:"请输入用户昵称"},model:{value:e.queryParam.userNickname,callback:function(t){e.$set(e.queryParam,"userNickname",t)},expression:"queryParam.userNickname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件名称"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.queryParam.pluginName,callback:function(t){e.$set(e.queryParam,"pluginName",t)},expression:"queryParam.pluginName"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件标识"}},[a("a-input",{attrs:{placeholder:"请输入插件Key"},model:{value:e.queryParam.pluginKey,callback:function(t){e.$set(e.queryParam,"pluginKey",t)},expression:"queryParam.pluginKey"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"调用状态"}},[a("a-select",{attrs:{placeholder:"请选择调用状态",allowClear:""},model:{value:e.queryParam.responseStatus,callback:function(t){e.$set(e.queryParam,"responseStatus",t)},expression:"queryParam.responseStatus"}},[a("a-select-option",{attrs:{value:200}},[e._v("成功")]),a("a-select-option",{attrs:{value:400}},[e._v("请求错误")]),a("a-select-option",{attrs:{value:401}},[e._v("未授权")]),a("a-select-option",{attrs:{value:500}},[e._v("服务器错误")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"调用时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始时间', '结束时间']"},model:{value:e.queryParam.callTimeRange,callback:function(t){e.$set(e.queryParam,"callTimeRange",t)},expression:"queryParam.callTimeRange"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件使用记录")}}},[e._v("导出")]),a("a-button",{attrs:{type:"primary",icon:"bar-chart"},on:{click:e.showStats}},[e._v("统计分析")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"danger",icon:"delete"},on:{click:e.batchDelete}},[e._v("批量删除")]):e._e()],1),a("div",[a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:e.rowSelection},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"responseStatus",fn:function(t){return[a("a-tag",{attrs:{color:200===t?"green":"red"}},[e._v("\n          "+e._s(200===t?"成功":"失败("+t+")")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}}])})],1),a("a-modal",{attrs:{title:"插件使用统计分析",width:1200,visible:e.statsVisible,footer:null},on:{cancel:function(t){e.statsVisible=!1}}},[e.statsData?a("div",[a("a-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总调用次数",value:e.statsData.totalCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功调用",value:e.statsData.successCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"失败调用",value:e.statsData.errorCalls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功率",value:e.statsData.successRate,suffix:"%",precision:2}})],1)],1),a("a-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总消耗Token",value:e.statsData.totalTokens}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总消耗金额",value:e.statsData.totalCost,prefix:"¥",precision:2}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"平均响应时间",value:e.statsData.avgResponseTime,suffix:"ms"}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"活跃用户数",value:e.statsData.activeUsers}})],1)],1),a("a-divider"),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:12}},[a("h4",[e._v("热门插件排行")]),a("a-table",{attrs:{columns:e.hotPluginColumns,dataSource:e.statsData.hotPlugins,pagination:!1,size:"small"}})],1),a("a-col",{attrs:{span:12}},[a("h4",[e._v("活跃用户排行")]),a("a-table",{attrs:{columns:e.activeUserColumns,dataSource:e.statsData.activeUserList,pagination:!1,size:"small"}})],1)],1)],1):e._e()]),a("a-modal",{attrs:{title:"插件使用详情",width:800,visible:e.detailVisible,footer:null},on:{cancel:function(t){e.detailVisible=!1}}},[e.detailData?a("div",[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.detailData.user_id))]),a("a-descriptions-item",{attrs:{label:"用户昵称"}},[e._v(e._s(e.detailData.userNickname||"-"))]),a("a-descriptions-item",{attrs:{label:"API密钥"}},[e._v(e._s(e.detailData.api_key))]),a("a-descriptions-item",{attrs:{label:"插件名称"}},[e._v(e._s(e.detailData.plugin_name))]),a("a-descriptions-item",{attrs:{label:"插件标识"}},[e._v(e._s(e.detailData.plugin_key))]),a("a-descriptions-item",{attrs:{label:"API接口"}},[e._v(e._s(e.detailData.api_endpoint))]),a("a-descriptions-item",{attrs:{label:"请求方法"}},[e._v(e._s(e.detailData.api_method))]),a("a-descriptions-item",{attrs:{label:"响应状态"}},[a("a-tag",{attrs:{color:200===e.detailData.response_status?"green":"red"}},[e._v("\n            "+e._s(e.detailData.response_status)+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"响应时间"}},[e._v(e._s(e.detailData.response_time)+"ms")]),a("a-descriptions-item",{attrs:{label:"消耗Token"}},[e._v(e._s(e.detailData.tokens_used||"-"))]),a("a-descriptions-item",{attrs:{label:"消耗金额"}},[e._v("¥"+e._s(e.detailData.cost_amount||"0.00"))]),a("a-descriptions-item",{attrs:{label:"IP地址"}},[e._v(e._s(e.detailData.ip_address))]),a("a-descriptions-item",{attrs:{label:"调用时间"}},[e._v(e._s(e.formatDateTime(e.detailData.call_time)))])],1),a("a-divider"),a("h4",[e._v("请求参数")]),a("pre",{staticStyle:{background:"#f5f5f5",padding:"10px","border-radius":"4px","max-height":"200px","overflow-y":"auto"}},[e._v(e._s(e.detailData.request_params))]),e.detailData.error_message?a("div",[a("h4",[e._v("错误信息")]),a("a-alert",{attrs:{message:e.detailData.error_message,type:"error"}})],1):e._e()],1):e._e()])],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o={name:"AicgUserPluginUsageList",mixins:[n["a"],l["b"]],components:{},data:function(){return{description:"用户插件使用记录管理页面",statsVisible:!1,statsData:null,detailVisible:!1,detailData:null,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"user_id",width:120,ellipsis:!0},{title:"用户昵称",align:"center",dataIndex:"userNickname",width:120,ellipsis:!0},{title:"插件名称",align:"center",dataIndex:"plugin_name",width:150,ellipsis:!0},{title:"插件标识",align:"center",dataIndex:"plugin_key",width:120,ellipsis:!0},{title:"API接口",align:"center",dataIndex:"api_endpoint",width:200,ellipsis:!0},{title:"调用状态",align:"center",dataIndex:"response_status",width:100,scopedSlots:{customRender:"responseStatus"}},{title:"响应时间",align:"center",dataIndex:"response_time",width:100,customRender:function(e){return e?"".concat(e,"ms"):"-"}},{title:"消耗Token",align:"center",dataIndex:"tokens_used",width:100,customRender:function(e){return e||"-"}},{title:"消耗金额",align:"center",dataIndex:"cost_amount",width:100,customRender:function(e){return e?"¥".concat(e):"-"}},{title:"调用时间",align:"center",dataIndex:"call_time",width:150,customRender:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:120,scopedSlots:{customRender:"action"}}],url:{list:"/demo/apiusage/pluginUsageList",delete:"/demo/apiusage/delete",deleteBatch:"/demo/apiusage/deleteBatch",exportXlsUrl:"/demo/apiusage/exportXls",importExcelUrl:"demo/apiusage/importExcel",stats:"/demo/apiusage/getPluginStats"},dictOptions:{},superFieldList:[],hotPluginColumns:[{title:"排名",dataIndex:"rank",width:60,align:"center"},{title:"插件名称",dataIndex:"pluginName",ellipsis:!0},{title:"调用次数",dataIndex:"callCount",width:100,align:"center"}],activeUserColumns:[{title:"排名",dataIndex:"rank",width:60,align:"center"},{title:"用户ID",dataIndex:"userId",ellipsis:!0},{title:"调用次数",dataIndex:"callCount",width:100,align:"center"}]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"user_id",text:"用户ID"}),e.push({type:"string",value:"userNickname",text:"用户昵称"}),e.push({type:"string",value:"plugin_name",text:"插件名称"}),e.push({type:"string",value:"plugin_key",text:"插件标识"}),e.push({type:"string",value:"api_endpoint",text:"API接口地址"}),e.push({type:"string",value:"api_method",text:"请求方法"}),e.push({type:"int",value:"response_status",text:"响应状态码"}),e.push({type:"int",value:"response_time",text:"响应时间"}),e.push({type:"int",value:"tokens_used",text:"消耗Token数量"}),e.push({type:"BigDecimal",value:"cost_amount",text:"消耗金额"}),e.push({type:"Date",value:"call_time",text:"调用时间"}),this.superFieldList=e},showStats:function(){var e=this;this.loading=!0,this.$http.get(this.url.stats).then((function(t){e.loading=!1,t.success?(e.statsData=t.result,e.statsVisible=!0):e.$message.error(t.message||"获取统计数据失败")})).catch((function(t){e.loading=!1,e.$message.error("获取统计数据失败")}))},handleDetail:function(e){this.detailData=e,this.detailVisible=!0},batchDelete:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"确认删除",content:"确定要删除选中的 ".concat(this.selectedRowKeys.length," 条记录吗？"),onOk:function(){e.loading=!0,e.$http.delete(e.url.deleteBatch,{data:e.selectedRowKeys}).then((function(t){e.loading=!1,t.success?(e.$message.success("删除成功"),e.loadData(),e.onClearSelected()):e.$message.error(t.message||"删除失败")})).catch((function(t){e.loading=!1,e.$message.error("删除失败")}))}}):this.$message.warning("请选择要删除的记录")},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"},loadData:function(e){var t=this;if(this.url.list){var a=Object.assign({},this.queryParam,this.isorter,this.filters);this.queryParam.callTimeRange&&2===this.queryParam.callTimeRange.length&&(a.callTimeStart=this.queryParam.callTimeRange[0].format("YYYY-MM-DD"),a.callTimeEnd=this.queryParam.callTimeRange[1].format("YYYY-MM-DD"),delete a.callTimeRange),a.hasPluginInfo=!0,a.pageNo=this.ipagination.current,a.pageSize=this.ipagination.pageSize,this.loading=!0,this.$http.get(this.url.list,{params:a}).then((function(e){t.loading=!1,e.success?(t.dataSource=e.result.records||e.result,t.ipagination.total=e.result.total):t.$message.error(e.message||"查询失败")})).catch((function(e){t.loading=!1,t.$message.error("查询失败")}))}else this.$message.error("请设置url.list属性!")}}},s=o,c=(a("d403"),a("2877")),d=Object(c["a"])(s,r,i,!1,null,"3b48e4c0",null);t["default"]=d.exports},"388b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID",disabled:!!e.model.id},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1),a("a-form-model-item",{attrs:{label:"API密钥",prop:"apiKey"}},[a("a-input",{attrs:{placeholder:"请输入API密钥",disabled:!!e.model.id},model:{value:e.model.apiKey,callback:function(t){e.$set(e.model,"apiKey",t)},expression:"model.apiKey"}})],1),a("a-form-model-item",{attrs:{label:"API接口",prop:"apiEndpoint"}},[a("a-input",{attrs:{placeholder:"请输入API接口地址",disabled:!!e.model.id},model:{value:e.model.apiEndpoint,callback:function(t){e.$set(e.model,"apiEndpoint",t)},expression:"model.apiEndpoint"}})],1),a("a-form-model-item",{attrs:{label:"请求方法",prop:"apiMethod"}},[a("a-select",{attrs:{placeholder:"请选择请求方法",disabled:!!e.model.id},model:{value:e.model.apiMethod,callback:function(t){e.$set(e.model,"apiMethod",t)},expression:"model.apiMethod"}},[a("a-select-option",{attrs:{value:"GET"}},[e._v("GET")]),a("a-select-option",{attrs:{value:"POST"}},[e._v("POST")]),a("a-select-option",{attrs:{value:"PUT"}},[e._v("PUT")]),a("a-select-option",{attrs:{value:"DELETE"}},[e._v("DELETE")])],1)],1),a("a-form-model-item",{attrs:{label:"插件ID",prop:"pluginId"}},[a("a-input",{attrs:{placeholder:"请输入插件ID",disabled:!!e.model.id},model:{value:e.model.pluginId,callback:function(t){e.$set(e.model,"pluginId",t)},expression:"model.pluginId"}})],1),a("a-form-model-item",{attrs:{label:"插件标识",prop:"pluginKey"}},[a("a-input",{attrs:{placeholder:"请输入插件Key",disabled:!!e.model.id},model:{value:e.model.pluginKey,callback:function(t){e.$set(e.model,"pluginKey",t)},expression:"model.pluginKey"}})],1),a("a-form-model-item",{attrs:{label:"插件名称",prop:"pluginName"}},[a("a-input",{attrs:{placeholder:"请输入插件名称",disabled:!!e.model.id},model:{value:e.model.pluginName,callback:function(t){e.$set(e.model,"pluginName",t)},expression:"model.pluginName"}})],1),a("a-form-model-item",{attrs:{label:"响应状态",prop:"responseStatus"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入响应状态码",disabled:!!e.model.id},model:{value:e.model.responseStatus,callback:function(t){e.$set(e.model,"responseStatus",t)},expression:"model.responseStatus"}})],1),a("a-form-model-item",{attrs:{label:"响应时间(ms)",prop:"responseTime"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入响应时间",disabled:!!e.model.id},model:{value:e.model.responseTime,callback:function(t){e.$set(e.model,"responseTime",t)},expression:"model.responseTime"}})],1),a("a-form-model-item",{attrs:{label:"消耗Token",prop:"tokensUsed"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入消耗Token数量",disabled:!!e.model.id},model:{value:e.model.tokensUsed,callback:function(t){e.$set(e.model,"tokensUsed",t)},expression:"model.tokensUsed"}})],1),a("a-form-model-item",{attrs:{label:"消耗金额",prop:"costAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入消耗金额",precision:2,disabled:!!e.model.id},model:{value:e.model.costAmount,callback:function(t){e.$set(e.model,"costAmount",t)},expression:"model.costAmount"}})],1),a("a-form-model-item",{attrs:{label:"IP地址",prop:"ipAddress"}},[a("a-input",{attrs:{placeholder:"请输入IP地址",disabled:!!e.model.id},model:{value:e.model.ipAddress,callback:function(t){e.$set(e.model,"ipAddress",t)},expression:"model.ipAddress"}})],1),a("a-form-model-item",{attrs:{label:"用户代理",prop:"userAgent"}},[a("a-textarea",{attrs:{placeholder:"请输入用户代理信息",rows:2,disabled:!!e.model.id},model:{value:e.model.userAgent,callback:function(t){e.$set(e.model,"userAgent",t)},expression:"model.userAgent"}})],1),a("a-form-model-item",{attrs:{label:"请求参数",prop:"requestParams"}},[a("a-textarea",{attrs:{placeholder:"请输入请求参数",rows:4,disabled:!!e.model.id},model:{value:e.model.requestParams,callback:function(t){e.$set(e.model,"requestParams",t)},expression:"model.requestParams"}})],1),e.model.errorMessage?a("a-form-model-item",{attrs:{label:"错误信息",prop:"errorMessage"}},[a("a-textarea",{attrs:{placeholder:"错误信息",rows:3,disabled:!0},model:{value:e.model.errorMessage,callback:function(t){e.$set(e.model,"errorMessage",t)},expression:"model.errorMessage"}})],1):e._e(),a("a-form-model-item",{attrs:{label:"调用时间",prop:"callTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择调用时间",format:"YYYY-MM-DD HH:mm:ss","show-time":"",disabled:!!e.model.id},model:{value:e.model.callTime,callback:function(t){e.$set(e.model,"callTime",t)},expression:"model.callTime"}})],1)],1)],1)],1)},i=[],l=a("0fea"),n={name:"AicgPluginUsageModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],apiEndpoint:[{required:!0,message:"请输入API接口地址!"}],apiMethod:[{required:!0,message:"请选择请求方法!"}],responseStatus:[{required:!0,message:"请输入响应状态码!"}]},url:{add:"/demo/apiusage/add",edit:"/demo/apiusage/edit"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,apiKey:t.model.apiKey,apiEndpoint:t.model.apiEndpoint,apiMethod:t.model.apiMethod,pluginId:t.model.pluginId,pluginKey:t.model.pluginKey,pluginName:t.model.pluginName,responseStatus:t.model.responseStatus,responseTime:t.model.responseTime,tokensUsed:t.model.tokensUsed,costAmount:t.model.costAmount,ipAddress:t.model.ipAddress,userAgent:t.model.userAgent,requestParams:t.model.requestParams,errorMessage:t.model.errorMessage,callTime:t.model.callTime};t.form.setFieldsValue(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(l["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},popupCallback:function(e){this.model=Object.assign(this.model,e)}}},o=n,s=a("2877"),c=Object(s["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},"42e3":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:1200,visible:e.visible,maskClosable:!1,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}}},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-agent-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],l=a("4a4a"),n={name:"AigcAgentModal",components:{AigcAgentForm:l["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$refs.realForm&&this.$refs.realForm.handleClose&&this.$refs.realForm.handleClose(),this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.handleOk()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=n,s=a("2877"),c=Object(s["a"])(o,r,i,!1,null,"c23b5a34",null);t["default"]=c.exports},4638:function(e,t,a){},"463f":function(e,t,a){"use strict";var r=a("a3f8"),i=a.n(r);i.a},"46c4":function(e,t,a){},"484b":function(e,t,a){},"49df":function(e,t,a){"use strict";var r=a("46c4"),i=a.n(r);i.a},"4a4a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"作者类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"authorType"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"author_type",placeholder:"请选择作者类型"},model:{value:e.model.authorType,callback:function(t){e.$set(e.model,"authorType",t)},expression:"model.authorType"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"智能体名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"agentName"}},[a("a-input",{attrs:{placeholder:"请输入智能体名称"},model:{value:e.model.agentName,callback:function(t){e.$set(e.model,"agentName",t)},expression:"model.agentName"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"智能体描述",labelCol:e.labelCol2,wrapperCol:e.wrapperCol2,prop:"agentDescription"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入智能体描述"},model:{value:e.model.agentDescription,callback:function(t){e.$set(e.model,"agentDescription",t)},expression:"model.agentDescription"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"智能体头像",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"agentAvatar"}},[a("j-image-upload-deferred",{ref:"avatarUpload",attrs:{isMultiple:!1,bizPath:"agent-avatar",text:"上传头像"},model:{value:e.model.agentAvatar,callback:function(t){e.$set(e.model,"agentAvatar",t)},expression:"model.agentAvatar"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"展示视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"demoVideo"}},[a("j-upload",{attrs:{beforeUpload:e.beforeVideoUpload,text:"上传视频(最大100MB)"},model:{value:e.model.demoVideo,callback:function(t){e.$set(e.model,"demoVideo",t)},expression:"model.demoVideo"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"体验链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"experienceLink"}},[a("a-input",{attrs:{placeholder:"请输入体验链接"},model:{value:e.model.experienceLink,callback:function(t){e.$set(e.model,"experienceLink",t)},expression:"model.experienceLink"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"价格（元）",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"price"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入价格（元）"},model:{value:e.model.price,callback:function(t){e.$set(e.model,"price",t)},expression:"model.price"}})],1)],1),a("a-col",{attrs:{span:8}},[a("a-form-model-item",{attrs:{label:"审核状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"auditStatus"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"audit_status",placeholder:"请选择审核状态"},model:{value:e.model.auditStatus,callback:function(t){e.$set(e.model,"auditStatus",t)},expression:"model.auditStatus"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"审核备注",labelCol:e.labelCol2,wrapperCol:e.wrapperCol2,prop:"auditRemark"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入审核备注"},model:{value:e.model.auditRemark,callback:function(t){e.$set(e.model,"auditRemark",t)},expression:"model.auditRemark"}})],1)],1)],1)],1)],1),a("a-tabs",{on:{change:e.handleChangeTabs},model:{value:e.activeKey,callback:function(t){e.activeKey=t},expression:"activeKey"}},[a("a-tab-pane",{key:e.refKeys[0],attrs:{tab:"工作流表",forceRender:!0}},[a("j-editable-table",{ref:e.refKeys[0],attrs:{loading:e.aigcWorkflowTable.loading,columns:e.aigcWorkflowTable.columns,dataSource:e.aigcWorkflowTable.dataSource,maxHeight:300,disabled:e.formDisabled,rowNumber:!0,rowSelection:!0,actionButton:!0}})],1)],1)],1)},i=[],l=a("a34a"),n=a.n(l),o=(a("0fea"),a("e2e0")),s=a("535e"),c=(a("ca00"),a("6745"));function d(e,t,a,r,i,l,n){try{var o=e[l](n),s=o.value}catch(c){return void a(c)}o.done?t(s):Promise.resolve(s).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var l=e.apply(t,a);function n(e){d(l,r,i,n,o,"next",e)}function o(e){d(l,r,i,n,o,"throw",e)}n(void 0)}))}}function m(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function p(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?m(Object(a),!0).forEach((function(t){h(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function h(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var f={name:"AigcAgentForm",mixins:[s["a"]],components:{JImageUploadDeferred:c["default"]},data:function(){return{labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:16}},labelCol2:{xs:{span:24},sm:{span:3}},wrapperCol2:{xs:{span:24},sm:{span:20}},model:{authorType:"1",auditStatus:"2"},addDefaultRowNum:1,validatorRules:{authorType:[{required:!0,message:"请输入作者类型!"}],agentName:[{required:!0,message:"请输入智能体名称!"}],agentDescription:[{required:!0,message:"请输入智能体描述!"}],agentAvatar:[{required:!0,message:"请输入智能体头像!"}],price:[{required:!0,message:"请输入价格（元）!"}],auditStatus:[{required:!0,message:"请输入审核状态!"}]},refKeys:["aigcWorkflow"],tableKeys:["aigcWorkflow"],activeKey:"aigcWorkflow",aigcWorkflowTable:{loading:!1,dataSource:[],columns:[{title:"智能体ID",key:"agentId",type:o["a"].hidden,defaultValue:""},{title:"工作流ID",key:"workflowId",type:o["a"].hidden,defaultValue:""},{title:"工作流名称",key:"workflowName",type:o["a"].input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"工作流描述",key:"workflowDescription",type:o["a"].input,width:"200px",placeholder:"请输入${title}",defaultValue:""},{title:"输入参数说明",key:"inputParamsDesc",type:o["a"].input,width:"300px",placeholder:"格式：参数:值 或 参数:\"值\" 或 参数:'值'（例如：name:\"张三\",age:25,city:'北京'）",defaultValue:"",validateRules:[{required:!0,message:"请输入参数说明"},{min:5,message:"参数说明至少需要5个字符"},{max:1e4,message:"参数说明长度不能超过10000个字符"},{pattern:/^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/,message:"请按照格式填写：参数:值 或 参数:\"值\" 或 参数:'值'"}]},{title:"工作流压缩包",key:"workflowPackage",type:o["a"].file,token:!0,responseName:"message",width:"200px",placeholder:"请选择文件",defaultValue:""}]},url:{add:"/aigc_agent/aigcAgent/add",edit:"/aigc_agent/aigcAgent/edit",queryById:"/aigc_agent/aigcAgent/queryById",aigcWorkflow:{list:"/aigc_agent/aigcAgent/queryAigcWorkflowByMainId"}}}},props:{disabled:{type:Boolean,default:!1,required:!1}},computed:{formDisabled:function(){return this.disabled}},created:function(){},methods:{addBefore:function(){this.aigcWorkflowTable.dataSource=[]},getAllTable:function(){var e=this,t=this.tableKeys.map((function(t){return Object(o["c"])(e,t)}));return Promise.all(t)},editAfter:function(){if(this.$nextTick((function(){})),this.model.id){var e={id:this.model.id};this.requestSubTableData(this.url.aigcWorkflow.list,e,this.aigcWorkflowTable)}},validateSubForm:function(e){var t=this;return new Promise((function(a,r){Promise.all([]).then((function(){a(e)})).catch((function(e){e.error===o["b"]&&(t.activeKey=null==e.index?t.activeKey:t.refKeys[e.index])}))}))},classifyIntoFormData:function(e){var t=Object.assign(this.model,e.formValue);return p(p({},t),{},{aigcWorkflowList:e.tablesValue[0].values})},validateError:function(e){this.$message.error(e)},beforeVideoUpload:function(e){var t=0===e.type.indexOf("video/");if(!t)return this.$message.error("只能上传视频文件!"),!1;var a=e.size/1024/1024<100;return!!a||(this.$message.error("视频大小不能超过 100MB!"),!1)},handleOk:function(){var e=u(n.a.mark((function e(){var t=this;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=1,e.next=4,this.uploadPendingImages();case 4:e.next=11;break;case 6:return e.prev=6,e.t0=e["catch"](1),this.$message.error("头像上传失败: "+(e.t0.message||"未知错误")),e.abrupt("return");case 11:this.getAllTable().then((function(e){var a=t.model.agentAvatar;return!t.model.agentAvatar&&t.$refs.avatarUpload&&t.$refs.avatarUpload.hasPendingFiles()&&(t.model.agentAvatar="pending_upload"),Object(o["d"])(t.$refs.form,t.model,e).then((function(e){return t.model.agentAvatar=a,e})).catch((function(e){throw t.model.agentAvatar=a,e}))})).then((function(e){return t.validateSubForm(e)})).then(function(){var e=u(n.a.mark((function e(a){var r,i;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,r=t.classifyIntoFormData(a),e.next=4,t.request(r);case 4:return i=e.sent,e.next=7,t.confirmDeleteOriginalFiles();case 7:return e.abrupt("return",i);case 10:throw e.prev=10,e.t0=e["catch"](0),e.t0;case 14:case"end":return e.stop()}}),e,null,[[0,10]])})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){e.error===o["b"]&&(t.activeKey=null==e.index?t.activeKey:t.refKeys[e.index])}));case 12:case"end":return e.stop()}}),e,this,[[1,6]])})));function t(){return e.apply(this,arguments)}return t}(),uploadPendingImages:function(){var e=u(n.a.mark((function e(){var t,a;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.$refs.avatarUpload){e.next=23;break}if(!this.$refs.avatarUpload.hasPendingFiles()){e.next=20;break}return e.prev=4,e.next=7,this.$refs.avatarUpload.performUpload();case 7:e.sent,t=this.$refs.avatarUpload.getCurrentValue(),this.model.agentAvatar=t,e.next=18;break;case 14:throw e.prev=14,e.t0=e["catch"](4),e.t0;case 18:e.next=23;break;case 20:a=this.$refs.avatarUpload.getCurrentValue(),this.model.agentAvatar=a;case 23:case 24:case"end":return e.stop()}}),e,this,[[4,14]])})));function t(){return e.apply(this,arguments)}return t}(),confirmDeleteOriginalFiles:function(){var e=u(n.a.mark((function e(){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.$refs.avatarUpload){e.next=10;break}return e.prev=1,e.next=4,this.$refs.avatarUpload.confirmDeleteOriginalFiles();case 4:e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1);case 10:case"end":return e.stop()}}),e,this,[[1,7]])})));function t(){return e.apply(this,arguments)}return t}(),handleClose:function(){this.$refs.avatarUpload&&this.$refs.avatarUpload.rollbackChanges(),this.$emit("close")}}},g=f,b=a("2877"),v=Object(b["a"])(g,r,i,!1,null,"6ce90ef4",null);t["default"]=v.exports},"5d40":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件名称"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.queryParam.plubname,callback:function(t){e.$set(e.queryParam,"plubname",t)},expression:"queryParam.plubname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件创作者"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择插件创作者",dictCode:"aigc_plub_author,authorname,id"},model:{value:e.queryParam.plubwrite,callback:function(t){e.$set(e.queryParam,"plubwrite",t)},expression:"queryParam.plubwrite"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件状态"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择插件状态",dictCode:"plugin_status"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否组合插件"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否组合插件",dictCode:"isTrue"},model:{value:e.queryParam.isCombined,callback:function(t){e.$set(e.queryParam,"isCombined",t)},expression:"queryParam.isCombined"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否SVIP免费"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否SVIP免费",dictCode:"isTrue"},model:{value:e.queryParam.isSvipFree,callback:function(t){e.$set(e.queryParam,"isSvipFree",t)},expression:"queryParam.isSvipFree"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"组合插件名"}},[a("a-input",{attrs:{placeholder:"请输入组合插件名"},model:{value:e.queryParam.combinedName,callback:function(t){e.$set(e.queryParam,"combinedName",t)},expression:"queryParam.combinedName"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"收益金额"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.income_begin,callback:function(t){e.$set(e.queryParam,"income_begin",t)},expression:"queryParam.income_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.income_end,callback:function(t){e.$set(e.queryParam,"income_end",t)},expression:"queryParam.income_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"调用次数"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.usernum_begin,callback:function(t){e.$set(e.queryParam,"usernum_begin",t)},expression:"queryParam.usernum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.usernum_end,callback:function(t){e.$set(e.queryParam,"usernum_end",t)},expression:"queryParam.usernum_end"}})],1)],1),a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"需要金额"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.neednum_begin,callback:function(t){e.$set(e.queryParam,"neednum_begin",t)},expression:"queryParam.neednum_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.neednum_end,callback:function(t){e.$set(e.queryParam,"neednum_end",t)},expression:"queryParam.neednum_end"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件商城")}}},[e._v("导出")]),e.isAdmin?[a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()]:e._e()],2),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"tutorialLinkSlot",fn:function(t){return[t?a("a",{staticStyle:{color:"#1890ff"},attrs:{href:t,target:"_blank"}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:"link"}}),e._v("\n          查看教程\n        ")],1):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("暂无")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),e.isAdmin?[a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)]:e._e()],2)}}])})],1),a("aigc-plub-shop-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("9caa"),s=(a("89f2"),{name:"AigcPlubShopList",mixins:[n["a"],l["b"]],components:{AigcPlubShopModal:o["default"]},data:function(){return{description:"插件商城管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"插件名称",align:"center",dataIndex:"plubname"},{title:"图片",align:"center",dataIndex:"plubimg",scopedSlots:{customRender:"imgSlot"}},{title:"插件创作者",align:"center",dataIndex:"plubwrite_dictText"},{title:"插件介绍",align:"center",dataIndex:"plubinfo"},{title:"插件分类",align:"center",dataIndex:"plubCategory_dictText"},{title:"插件状态",align:"center",dataIndex:"status_dictText"},{title:"是否组合插件",align:"center",dataIndex:"isCombined_dictText",width:120},{title:"组合插件名",align:"center",dataIndex:"combinedName",width:150,customRender:function(e){return e||"-"}},{title:"组合插件图片",align:"center",dataIndex:"combinedImage",width:120,scopedSlots:{customRender:"imgSlot"}},{title:"插件唯一标识",align:"center",dataIndex:"pluginKey"},{title:"教程链接",align:"center",dataIndex:"tutorialLink",scopedSlots:{customRender:"tutorialLinkSlot"}},{title:"排序权重",align:"center",dataIndex:"sortOrder",sorter:!0,defaultSortOrder:"ascend"},{title:"插件教程视频",align:"center",dataIndex:"plubvideo",scopedSlots:{customRender:"fileSlot"}},{title:"收益金额",align:"center",dataIndex:"income",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"调用次数",align:"center",dataIndex:"usernum"},{title:"需要金额",align:"center",dataIndex:"neednum",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"SVIP是否免费",align:"center",dataIndex:"isSvipFree_dictText"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:this.isAdmin?200:80,scopedSlots:{customRender:"action"}}],url:{list:"/plubshop/aigcPlubShop/list",delete:"/plubshop/aigcPlubShop/delete",deleteBatch:"/plubshop/aigcPlubShop/deleteBatch",exportXlsUrl:"/plubshop/aigcPlubShop/exportXls",importExcelUrl:"plubshop/aigcPlubShop/importExcel"},isorter:{column:"sortOrder",order:"asc"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"plubname",text:"插件名称",dictCode:""}),e.push({type:"string",value:"plubimg",text:"图片",dictCode:""}),e.push({type:"string",value:"plubwrite",text:"插件创作者",dictCode:"aigc_plub_author,authorname,id"}),e.push({type:"string",value:"plubinfo",text:"插件介绍",dictCode:""}),e.push({type:"string",value:"plubContent",text:"插件详细内容",dictCode:""}),e.push({type:"string",value:"plubCategory",text:"插件分类",dictCode:"plugin_category"}),e.push({type:"int",value:"status",text:"插件状态",dictCode:"plugin_status"}),e.push({type:"int",value:"isCombined",text:"是否组合插件",dictCode:"isTrue"}),e.push({type:"int",value:"isSvipFree",text:"SVIP是否免费",dictCode:"isTrue"}),e.push({type:"string",value:"combinedName",text:"组合插件名",dictCode:""}),e.push({type:"string",value:"combinedDescription",text:"组合插件介绍",dictCode:""}),e.push({type:"string",value:"combinedImage",text:"组合插件图片",dictCode:""}),e.push({type:"string",value:"pluginKey",text:"插件唯一标识",dictCode:""}),e.push({type:"int",value:"sortOrder",text:"排序权重",dictCode:""}),e.push({type:"string",value:"plubvideo",text:"插件教程视频",dictCode:""}),e.push({type:"BigDecimal",value:"income",text:"收益金额",dictCode:""}),e.push({type:"int",value:"usernum",text:"调用次数",dictCode:""}),e.push({type:"BigDecimal",value:"neednum",text:"需要金额",dictCode:""}),this.superFieldList=e}}}),c=s,d=(a("c3c7"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"0143d37a",null);t["default"]=u.exports},6198:function(e,t,a){},"63be":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否组合插件",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-dict-select-tag",{attrs:{dictCode:"isTrue",placeholder:"请选择是否为组合插件"},model:{value:e.model.isCombined,callback:function(t){e.$set(e.model,"isCombined",t)},expression:"model.isCombined"}})],1)],1),e.isCombinedPlugin?a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"组合插件名",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{attrs:{placeholder:"请输入组合插件名称"},model:{value:e.model.combinedName,callback:function(t){e.$set(e.model,"combinedName",t)},expression:"model.combinedName"}}),e.combinedNameError?a("div",{staticStyle:{color:"#f5222d","font-size":"12px","margin-top":"4px"}},[a("a-icon",{attrs:{type:"exclamation-circle"}}),e._v(" "+e._s(e.combinedNameError)+"\n            ")],1):e._e(),e.foundExistingCombined&&!e.combinedNameError?a("div",{staticStyle:{color:"#52c41a","font-size":"12px","margin-top":"4px"}},[a("a-icon",{attrs:{type:"check-circle"}}),e._v(" 已找到现有组合插件，相关信息已自动回填\n            ")],1):e._e()],1)],1):e._e()],1),e.isCombinedPlugin?a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"组合插件介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-textarea",{attrs:{rows:"3",placeholder:"请输入组合插件介绍"},model:{value:e.model.combinedDescription,callback:function(t){e.$set(e.model,"combinedDescription",t)},expression:"model.combinedDescription"}})],1)],1)],1):e._e(),e.isCombinedPlugin?a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"组合插件图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-image-upload-deferred",{ref:"combinedImageUpload",model:{value:e.model.combinedImage,callback:function(t){e.$set(e.model,"combinedImage",t)},expression:"model.combinedImage"}})],1)],1)],1):e._e(),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubname"}},[a("a-input",{attrs:{placeholder:"请输入插件名称"},model:{value:e.model.plubname,callback:function(t){e.$set(e.model,"plubname",t)},expression:"model.plubname"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件图片",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubimg"}},[a("j-image-upload-deferred",{ref:"pluginImageUpload",attrs:{isMultiple:""},model:{value:e.model.plubimg,callback:function(t){e.$set(e.model,"plubimg",t)},expression:"model.plubimg"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件创作者",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubwrite"}},[a("j-dict-select-tag",{attrs:{type:"list",dictCode:"aigc_plub_author,authorname,id",placeholder:"请选择插件创作者"},model:{value:e.model.plubwrite,callback:function(t){e.$set(e.model,"plubwrite",t)},expression:"model.plubwrite"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件分类",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubCategory"}},[a("j-dict-select-tag",{attrs:{dictCode:"plugin_category",placeholder:"请选择插件分类"},model:{value:e.model.plubCategory,callback:function(t){e.$set(e.model,"plubCategory",t)},expression:"model.plubCategory"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"适用场景",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"scenarios"}},[a("j-multi-select-tag",{attrs:{dictCode:"plugin_scenarios",placeholder:"请选择适用场景（可多选）"},model:{value:e.model.scenarios,callback:function(t){e.$set(e.model,"scenarios",t)},expression:"model.scenarios"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件介绍",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入插件介绍"},model:{value:e.model.plubinfo,callback:function(t){e.$set(e.model,"plubinfo",t)},expression:"model.plubinfo"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{staticClass:"j-field-content",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"插件详细内容",prop:"plubContent"}},[a("j-editor",{attrs:{placeholder:"请输入插件详细内容"},model:{value:e.model.plubContent,callback:function(t){e.$set(e.model,"plubContent",t)},expression:"model.plubContent"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"status"}},[a("j-dict-select-tag",{attrs:{dictCode:"plugin_status",placeholder:"请选择插件状态",disabled:!e.isAdmin},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}}),e.isAdmin?e._e():a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v('\n              非管理员用户默认为"审核中"状态\n            ')])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"排序权重",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"sortOrder"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入排序权重(数字越大越靠前)",disabled:!e.isAdmin},on:{change:e.handleSortOrderChange},model:{value:e.model.sortOrder,callback:function(t){e.$set(e.model,"sortOrder",t)},expression:"model.sortOrder"}}),e.isAdmin?e._e():a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              非管理员用户自动分配权重\n            ")])],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件唯一标识",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"pluginKey"}},[a("a-input-group",{attrs:{compact:""}},[a("a-input",{class:{"error-input":e.isDuplicate},staticStyle:{width:"70%"},attrs:{placeholder:"插件唯一标识（可手动编辑）",disabled:!1,suffix:e.checkingUnique?"检查中...":""},on:{blur:e.onPluginKeyBlur,input:e.onPluginKeyInput},model:{value:e.model.pluginKey,callback:function(t){e.$set(e.model,"pluginKey",t)},expression:"model.pluginKey"}}),a("a-button",{staticStyle:{width:"15%"},attrs:{type:"primary",loading:e.generating},on:{click:e.regeneratePluginKey}},[e._v("\n                重新生成\n              ")]),a("a-button",{staticStyle:{width:"15%"},attrs:{type:"default",loading:e.checkingUnique},on:{click:e.checkUnique}},[e._v("\n                检查重复\n              ")])],1),a("div",{staticStyle:{"margin-top":"4px","font-size":"12px"}},[e.isDuplicate?a("span",{staticStyle:{color:"#ff4d4f"}},[a("a-icon",{attrs:{type:"close-circle"}}),e._v(" 标识已存在，请重新生成或手动修改\n              ")],1):e.isUnique?a("span",{staticStyle:{color:"#52c41a"}},[a("a-icon",{attrs:{type:"check-circle"}}),e._v(" 标识可用\n              ")],1):e.model.pluginKey&&e.model.pluginKey.trim()?a("span",{staticStyle:{color:"#1890ff"}},[a("a-icon",{attrs:{type:"info-circle"}}),e._v(' 请点击"检查重复"验证标识可用性\n              ')],1):a("span",{staticStyle:{color:"#999"}},[e._v("\n                基于插件名称自动生成，也可手动编辑\n              ")])])],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"插件教程视频",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubvideo"}},[a("a-button",{staticStyle:{width:"100%"},attrs:{disabled:""},on:{click:e.showVideoUploadTip}},[a("a-icon",{attrs:{type:"upload"}}),e._v("\n              暂不支持上传文件\n            ")],1),a("div",{staticStyle:{"margin-top":"8px",color:"#999","font-size":"12px"}},[e._v("\n              视频上传功能暂时禁用，请使用下方的教程链接字段\n            ")])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"需要金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"neednum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入需要金额",precision:2},model:{value:e.model.neednum,callback:function(t){e.$set(e.model,"neednum",t)},expression:"model.neednum"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件教程链接",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"tutorialLink"}},[a("a-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入插件教程链接，如：https://www.example.com/tutorial"},model:{value:e.model.tutorialLink,callback:function(t){e.$set(e.model,"tutorialLink",t)},expression:"model.tutorialLink"}},[a("a-icon",{attrs:{slot:"prefix",type:"link"},slot:"prefix"})],1),a("div",{staticStyle:{"margin-top":"8px",color:"#666","font-size":"12px"}},[a("a-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:"info-circle"}}),e._v("\n              支持外部视频链接，如B站、YouTube、腾讯视频等平台链接\n            ")],1)],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"收益金额",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"income"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",precision:2,disabled:!0},model:{value:e.model.income,callback:function(t){e.$set(e.model,"income",t)},expression:"model.income"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              系统根据实际使用情况自动计算\n            ")])],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"调用次数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"usernum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.usernum,callback:function(t){e.$set(e.model,"usernum",t)},expression:"model.usernum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              系统根据实际调用自动统计\n            ")])],1)],1)],1)],1)],1)],1)},i=[],l=a("a34a"),n=a.n(l),o=a("0fea"),s=a("6745");function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e,t,a,r,i,l,n){try{var o=e[l](n),s=o.value}catch(c){return void a(c)}o.done?t(s):Promise.resolve(s).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var l=e.apply(t,a);function n(e){d(l,r,i,n,o,"next",e)}function o(e){d(l,r,i,n,o,"throw",e)}n(void 0)}))}}var m={name:"AigcPlubShopForm",components:{JImageUploadDeferred:s["default"]},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{usernum:0,income:0,isCombined:2,combinedName:"",combinedDescription:"",combinedImage:""},generating:!1,checkingUnique:!1,isDuplicate:!1,isUnique:!1,keyCounter:1,checkTimer:null,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,queryTimer:null,isAutoFilledDescription:!1,isAutoFilledImage:!1,isAutoFilledAuthor:!1,isAutoFilledSortOrder:!1,foundExistingCombined:!1,combinedNameError:"",validatorRules:{plubname:[{required:!0,message:"请输入插件名称!"}],plubwrite:[{required:!0,message:"请输入插件创作者!"}],plubinfo:[{required:!0,message:"请输入插件介绍!"}],plubCategory:[{required:!0,message:"请选择插件分类!"}],status:[{required:!0,message:"请选择插件状态!"}],pluginKey:[{required:!0,message:"请输入插件唯一标识!"},{pattern:/^[a-z0-9_]+$/,message:"插件标识只能包含小写字母、数字和下划线!"}],tutorialLink:[{pattern:/^https?:\/\/.+/,message:"请输入有效的链接地址，必须以http://或https://开头!"}]},url:{add:"/plubshop/aigcPlubShop/add",edit:"/plubshop/aigcPlubShop/edit",queryById:"/plubshop/aigcPlubShop/queryById"}}},computed:{formDisabled:function(){return this.disabled},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")},isCombinedPlugin:function(){return"1"===this.model.isCombined||1===this.model.isCombined}},watch:{"model.plubname":{handler:function(e,t){e&&e!==t&&(this.keyCounter=1,this.model.pluginKey&&""!==this.model.pluginKey.trim()&&!this.isAutoGeneratedKey()||this.generatePluginKey(),this.isAdmin||(this.model.status=2,this.setDefaultSortOrder()))},immediate:!0},"model.pluginKey":{handler:function(e){e&&this.debounceCheckUnique()}},"model.isCombined":{handler:function(e){"2"!==e&&2!==e||(this.model.combinedName="",this.model.combinedDescription="")},immediate:!0},"model.combinedName":{handler:function(e,t){this.isCombinedPlugin&&e&&e.trim()&&e!==t?this.debounceQueryCombinedPlugin():e&&e.trim()||(this.clearAutoFilledFields(),this.combinedNameError="",this.foundExistingCombined=!1)}}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},generatePluginKey:function(){var e=u(n.a.mark((function e(){var t,a;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.plubname){e.next=2;break}return e.abrupt("return");case 2:return this.generating=!0,this.isDuplicate=!1,this.isUnique=!1,e.prev=5,t=this.createBaseKey(this.model.plubname),a=t,this.keyCounter>1&&(a="".concat(t,"_").concat(this.keyCounter)),this.model.pluginKey=a,e.next=12,this.checkUnique();case 12:return e.prev=12,this.generating=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[5,,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),regeneratePluginKey:function(){var e=u(n.a.mark((function e(){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.plubname){e.next=3;break}return this.$message.warning("请先输入插件名称"),e.abrupt("return");case 3:return this.keyCounter++,e.next=6,this.generatePluginKey();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),createBaseKey:function(e){return e.toLowerCase().replace(/[\u4e00-\u9fa5]/g,(function(e){var t,a=(t={"扣":"kou","子":"zi","智":"zhi","能":"neng","体":"ti","插":"cha","件":"jian","机":"ji","器":"qi","人":"ren","助":"zhu","手":"shou","客":"ke","服":"fu","聊":"liao","天":"tian","对":"dui","话":"hua","问":"wen","答":"da","抖":"dou","音":"yin","快":"kuai"},c(t,"手","shou"),c(t,"小","xiao"),c(t,"红","hong"),c(t,"书","shu"),c(t,"微","wei"),c(t,"信","xin"),c(t,"博","bo"),c(t,"微","wei"),c(t,"博","bo"),c(t,"知","zhi"),c(t,"乎","hu"),c(t,"豆","dou"),c(t,"瓣","ban"),c(t,"贴","tie"),c(t,"吧","ba"),c(t,"论","lun"),c(t,"坛","tan"),c(t,"社","she"),c(t,"区","qu"),c(t,"群","qun"),c(t,"组","zu"),c(t,"圈","quan"),c(t,"飞","fei"),c(t,"书","shu"),c(t,"钉","ding"),c(t,"钉","ding"),c(t,"企","qi"),c(t,"微","wei"),c(t,"腾","teng"),c(t,"讯","xun"),c(t,"会","hui"),c(t,"议","yi"),c(t,"文","wen"),c(t,"档","dang"),c(t,"表","biao"),c(t,"格","ge"),c(t,"演","yan"),c(t,"示","shi"),c(t,"邮","you"),c(t,"件","jian"),c(t,"日","ri"),c(t,"历","li"),c(t,"任","ren"),c(t,"务","wu"),c(t,"项","xiang"),c(t,"目","mu"),c(t,"团","tuan"),c(t,"队","dui"),c(t,"协","xie"),c(t,"作","zuo"),c(t,"办","ban"),c(t,"公","gong"),c(t,"A","a"),c(t,"I","i"),c(t,"人","ren"),c(t,"工","gong"),c(t,"智","zhi"),c(t,"能","neng"),c(t,"学","xue"),c(t,"习","xi"),c(t,"深","shen"),c(t,"度","du"),c(t,"神","shen"),c(t,"经","jing"),c(t,"网","wang"),c(t,"络","luo"),c(t,"算","suan"),c(t,"法","fa"),c(t,"模","mo"),c(t,"型","xing"),c(t,"训","xun"),c(t,"练","lian"),c(t,"预","yu"),c(t,"测","ce"),c(t,"识","shi"),c(t,"别","bie"),c(t,"检","jian"),c(t,"测","ce"),c(t,"分","fen"),c(t,"析","xi"),c(t,"处","chu"),c(t,"理","li"),c(t,"自","zi"),c(t,"动","dong"),c(t,"化","hua"),c(t,"优","you"),c(t,"化","hua"),c(t,"创","chuang"),c(t,"作","zuo"),c(t,"编","bian"),c(t,"辑","ji"),c(t,"写","xie"),c(t,"作","zuo"),c(t,"文","wen"),c(t,"章","zhang"),c(t,"标","biao"),c(t,"题","ti"),c(t,"摘","zhai"),c(t,"要","yao"),c(t,"总","zong"),c(t,"结","jie"),c(t,"翻","fan"),c(t,"译","yi"),c(t,"润","run"),c(t,"色","se"),c(t,"校","jiao"),c(t,"对","dui"),c(t,"审","shen"),c(t,"核","he"),c(t,"发","fa"),c(t,"布","bu"),c(t,"推","tui"),c(t,"广","guang"),c(t,"营","ying"),c(t,"销","xiao"),c(t,"宣","xuan"),c(t,"传","chuan"),c(t,"图","tu"),c(t,"片","pian"),c(t,"照","zhao"),c(t,"片","pian"),c(t,"画","hua"),c(t,"像","xiang"),c(t,"视","shi"),c(t,"频","pin"),c(t,"音","yin"),c(t,"频","pin"),c(t,"声","sheng"),c(t,"音","yin"),c(t,"录","lu"),c(t,"制","zhi"),c(t,"剪","jian"),c(t,"辑","ji"),c(t,"合","he"),c(t,"成","cheng"),c(t,"特","te"),c(t,"效","xiao"),c(t,"滤","lv"),c(t,"镜","jing"),c(t,"美","mei"),c(t,"颜","yan"),c(t,"修","xiu"),c(t,"图","tu"),c(t,"水","shui"),c(t,"印","yin"),c(t,"压","ya"),c(t,"缩","suo"),c(t,"淘","tao"),c(t,"宝","bao"),c(t,"天","tian"),c(t,"猫","mao"),c(t,"京","jing"),c(t,"东","dong"),c(t,"拼","pin"),c(t,"多","duo"),c(t,"多","duo"),c(t,"苏","su"),c(t,"宁","ning"),c(t,"唯","wei"),c(t,"品","pin"),c(t,"会","hui"),c(t,"商","shang"),c(t,"品","pin"),c(t,"店","dian"),c(t,"铺","pu"),c(t,"货","huo"),c(t,"物","wu"),c(t,"订","ding"),c(t,"单","dan"),c(t,"支","zhi"),c(t,"付","fu"),c(t,"配","pei"),c(t,"送","song"),c(t,"物","wu"),c(t,"流","liu"),c(t,"退","tui"),c(t,"换","huan"),c(t,"大","da"),c(t,"师","shi"),c(t,"打","da"),c(t,"洞","dong"),c(t,"可","ke"),c(t,"口","kou"),c(t,"乐","le"),c(t,"生","sheng"),c(t,"成","cheng"),c(t,"工","gong"),c(t,"具","ju"),c(t,"功","gong"),c(t,"能","neng"),c(t,"应","ying"),c(t,"用","yong"),c(t,"软","ruan"),c(t,"件","jian"),c(t,"程","cheng"),c(t,"序","xu"),c(t,"系","xi"),c(t,"统","tong"),c(t,"平","ping"),c(t,"台","tai"),c(t,"框","kuang"),c(t,"架","jia"),c(t,"库","ku"),c(t,"包","bao"),c(t,"模","mo"),c(t,"块","kuai"),c(t,"组","zu"),c(t,"件","jian"),c(t,"接","jie"),c(t,"口","kou"),c(t,"服","fu"),c(t,"务","wu"),c(t,"端","duan"),c(t,"前","qian"),c(t,"后","hou"),c(t,"全","quan"),c(t,"栈","zhan"),c(t,"开","kai"),c(t,"发","fa"),c(t,"设","she"),c(t,"计","ji"),c(t,"实","shi"),c(t,"现","xian"),c(t,"部","bu"),c(t,"署","shu"),c(t,"运","yun"),c(t,"维","wei"),c(t,"测","ce"),c(t,"试","shi"),c(t,"调","tiao"),c(t,"试","shi"),c(t,"数","shu"),c(t,"据","ju"),c(t,"库","ku"),c(t,"表","biao"),c(t,"字","zi"),c(t,"段","duan"),c(t,"索","suo"),c(t,"引","yin"),c(t,"查","cha"),c(t,"询","xun"),c(t,"增","zeng"),c(t,"删","shan"),c(t,"改","gai"),c(t,"查","cha"),c(t,"存","cun"),c(t,"储","chu"),c(t,"备","bei"),c(t,"份","fen"),c(t,"恢","hui"),c(t,"复","fu"),c(t,"同","tong"),c(t,"步","bu"),c(t,"异","yi"),c(t,"步","bu"),c(t,"缓","huan"),c(t,"存","cun"),c(t,"内","nei"),c(t,"存","cun"),c(t,"磁","ci"),c(t,"盘","pan"),c(t,"网","wang"),c(t,"络","luo"),c(t,"互","hu"),c(t,"联","lian"),c(t,"网","wang"),c(t,"协","xie"),c(t,"议","yi"),c(t,"传","chuan"),c(t,"输","shu"),c(t,"通","tong"),c(t,"信","xin"),c(t,"连","lian"),c(t,"接","jie"),c(t,"请","qing"),c(t,"求","qiu"),c(t,"响","xiang"),c(t,"应","ying"),c(t,"状","zhuang"),c(t,"态","tai"),c(t,"码","ma"),c(t,"头","tou"),c(t,"部","bu"),c(t,"体","ti"),c(t,"参","can"),c(t,"数","shu"),c(t,"安","an"),c(t,"全","quan"),c(t,"权","quan"),c(t,"限","xian"),c(t,"角","jiao"),c(t,"色","se"),c(t,"用","yong"),c(t,"户","hu"),c(t,"认","ren"),c(t,"证","zheng"),c(t,"授","shou"),c(t,"权","quan"),c(t,"登","deng"),c(t,"录","lu"),c(t,"注","zhu"),c(t,"册","ce"),c(t,"退","tui"),c(t,"出","chu"),c(t,"密","mi"),c(t,"码","ma"),c(t,"加","jia"),c(t,"密","mi"),c(t,"解","jie"),c(t,"密","mi"),c(t,"签","qian"),c(t,"名","ming"),c(t,"验","yan"),c(t,"证","zheng"),c(t,"令","ling"),c(t,"牌","pai"),c(t,"界","jie"),c(t,"面","mian"),c(t,"页","ye"),c(t,"面","mian"),c(t,"窗","chuang"),c(t,"口","kou"),c(t,"对","dui"),c(t,"话","hua"),c(t,"框","kuang"),c(t,"按","an"),c(t,"钮","niu"),c(t,"链","lian"),c(t,"接","jie"),c(t,"菜","cai"),c(t,"单","dan"),c(t,"导","dao"),c(t,"航","hang"),c(t,"标","biao"),c(t,"签","qian"),c(t,"选","xuan"),c(t,"项","xiang"),c(t,"卡","ka"),c(t,"片","pian"),c(t,"列","lie"),c(t,"表","biao"),c(t,"表","biao"),c(t,"格","ge"),c(t,"表","biao"),c(t,"单","dan"),c(t,"输","shu"),c(t,"入","ru"),c(t,"框","kuang"),c(t,"下","xia"),c(t,"拉","la"),c(t,"框","kuang"),c(t,"复","fu"),c(t,"选","xuan"),c(t,"框","kuang"),c(t,"单","dan"),c(t,"选","xuan"),c(t,"按","an"),c(t,"钮","niu"),c(t,"滑","hua"),c(t,"块","kuai"),c(t,"进","jin"),c(t,"度","du"),c(t,"条","tiao"),c(t,"加","jia"),c(t,"载","zai"),c(t,"刷","shua"),c(t,"新","xin"),c(t,"更","geng"),c(t,"新","xin"),c(t,"提","ti"),c(t,"示","shi"),c(t,"消","xiao"),c(t,"息","xi"),c(t,"通","tong"),c(t,"知","zhi"),c(t,"警","jing"),c(t,"告","gao"),c(t,"确","que"),c(t,"认","ren"),c(t,"取","qu"),c(t,"消","xiao"),t);return a[e]||e.charCodeAt(0).toString(36)})).replace(/[^a-z0-9]/g,"_").replace(/_+/g,"_").replace(/^_|_$/g,"")},checkUnique:function(){var e=u(n.a.mark((function e(){var t,a=this;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.model.pluginKey){e.next=2;break}return e.abrupt("return");case 2:return this.checkingUnique=!0,this.isDuplicate=!1,this.isUnique=!1,e.prev=5,e.next=8,Object(o["c"])("/plubshop/aigcPlubShop/checkPluginKey",{pluginKey:this.model.pluginKey,excludeId:this.model.id});case 8:t=e.sent,t.success&&(this.isDuplicate=t.result.exists,this.isUnique=!t.result.exists,this.isDuplicate&&(this.$message.warning("标识已存在，正在自动生成新的标识..."),setTimeout((function(){a.regeneratePluginKey()}),1e3))),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](5),this.$message.error("检查标识唯一性失败");case 15:return e.prev=15,this.checkingUnique=!1,e.finish(15);case 18:case"end":return e.stop()}}),e,this,[[5,12,15,18]])})));function t(){return e.apply(this,arguments)}return t}(),debounceCheckUnique:function(){var e=this;clearTimeout(this.checkTimer),this.checkTimer=setTimeout((function(){e.checkUnique()}),500)},onPluginKeyInput:function(e){var t=e.toLowerCase().replace(/[^a-z0-9_]/g,"");t!==e&&(this.model.pluginKey=t),this.isDuplicate=!1,this.isUnique=!1,t&&t.trim()&&this.debounceCheckUnique()},onPluginKeyBlur:function(){this.model.pluginKey&&this.model.pluginKey.trim()&&this.checkUnique()},isAutoGeneratedKey:function(){if(!this.model.pluginKey||!this.model.plubname)return!0;var e=this.createBaseKey(this.model.plubname);return this.model.pluginKey===e||this.model.pluginKey.startsWith(e+"_")},setDefaultSortOrder:function(){var e=u(n.a.mark((function e(){var t;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["c"])("/plubshop/aigcPlubShop/getMaxSortOrder");case 3:t=e.sent,this.model.sortOrder=(t.result||0)+1,e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),this.model.sortOrder=1;case 11:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),handleSortOrderChange:function(){var e=u(n.a.mark((function e(t){var a,r=this;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(this.isAdmin&&t&&t>0)){e.next=12;break}return e.prev=1,e.next=5,Object(o["c"])("/plubshop/aigcPlubShop/checkSortOrderConflict",{sortOrder:parseInt(t),excludeId:this.model.id||""});case 5:a=e.sent,a.success&&a.result.hasConflict&&this.$confirm({title:"权重冲突",content:"权重 ".concat(t," 已存在，是否将现有插件权重后移？"),onOk:function(){r.adjustSortOrder(t)},onCancel:function(){r.setDefaultSortOrder()}}),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1);case 12:case"end":return e.stop()}}),e,this,[[1,9]])})));function t(t){return e.apply(this,arguments)}return t}(),adjustSortOrder:function(){var e=u(n.a.mark((function e(t){return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=4,Object(o["h"])("/plubshop/aigcPlubShop/adjustSortOrder",{targetOrder:parseInt(t),excludeId:this.model.id||""},"post");case 4:this.$message.success("权重调整成功"),e.next=11;break;case 7:e.prev=7,e.t0=e["catch"](0),this.$message.error("权重调整失败");case 11:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(t){return e.apply(this,arguments)}return t}(),debounceQueryCombinedPlugin:function(){var e=this;clearTimeout(this.queryTimer),this.queryTimer=setTimeout((function(){e.queryCombinedPluginInfo()}),500)},queryCombinedPluginInfo:function(){var e=u(n.a.mark((function e(){var t,a,r;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(o["c"])("/plubshop/aigcPlubShop/validateAndQueryCombinedPlugin",{combinedName:this.model.combinedName,excludeId:this.model.id||""});case 3:if(t=e.sent,!t.success||!t.result){e.next=13;break}if(a=t.result,a.hasPermission){e.next=11;break}return this.combinedNameError=a.message,this.foundExistingCombined=!1,this.clearAutoFilledFields(),e.abrupt("return");case 11:this.combinedNameError="",a.foundExisting?(r=[],this.model.combinedDescription&&""!==this.model.combinedDescription.trim()&&!this.isAutoFilledDescription||(this.model.combinedDescription=a.combinedDescription,this.isAutoFilledDescription=!0,r.push("组合插件介绍")),this.model.combinedImage&&""!==this.model.combinedImage.trim()&&!this.isAutoFilledImage||(this.model.combinedImage=a.combinedImage,this.isAutoFilledImage=!0,r.push("组合插件图片")),this.model.plubwrite&&""!==this.model.plubwrite.trim()&&!this.isAutoFilledAuthor||(this.model.plubwrite=a.plubwrite,this.isAutoFilledAuthor=!0,r.push("插件创作者")),this.model.sortOrder&&0!==this.model.sortOrder&&!this.isAutoFilledSortOrder||(this.model.sortOrder=a.sortOrder,this.isAutoFilledSortOrder=!0,r.push("排序权重")),this.foundExistingCombined=!0,r.length>0&&this.$message.info('已自动回填"'.concat(this.model.combinedName,'"的').concat(r.join("、")))):(this.foundExistingCombined=!1,this.clearAutoFilledFlags());case 13:e.next=20;break;case 15:e.prev=15,e.t0=e["catch"](0),this.combinedNameError="查询失败，请重试",this.foundExistingCombined=!1;case 20:case"end":return e.stop()}}),e,this,[[0,15]])})));function t(){return e.apply(this,arguments)}return t}(),clearAutoFilledFields:function(){this.isAutoFilledDescription&&(this.model.combinedDescription=""),this.isAutoFilledImage&&(this.model.combinedImage=""),this.isAutoFilledAuthor&&(this.model.plubwrite=""),this.isAutoFilledSortOrder&&(this.model.sortOrder=null),this.clearAutoFilledFlags()},clearAutoFilledFlags:function(){this.isAutoFilledDescription=!1,this.isAutoFilledImage=!1,this.isAutoFilledAuthor=!1,this.isAutoFilledSortOrder=!1},checkCombinedPluginCount:function(){var e=u(n.a.mark((function e(){var t,a,r,i=this;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,this.model.combinedName&&this.isCombinedPlugin){e.next=3;break}return e.abrupt("return",0);case 3:return e.next=5,Object(o["c"])("/plubshop/aigcPlubShop/list",{combinedName:this.model.combinedName,isCombined:1,pageNo:1,pageSize:1e3});case 5:if(t=e.sent,!(t.success&&t.result&&t.result.records)){e.next=10;break}return a=this.model.id,r=t.result.records.filter((function(e){return e.id!==a&&e.combinedName===i.model.combinedName})),e.abrupt("return",r.length);case 10:return e.abrupt("return",0);case 13:return e.prev=13,e.t0=e["catch"](0),e.abrupt("return",0);case 17:case"end":return e.stop()}}),e,this,[[0,13]])})));function t(){return e.apply(this,arguments)}return t}(),submitForm:function(){var e=u(n.a.mark((function e(){var t,a,r,i=this;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this,1!==this.model.isCombined||!this.model.combinedName||!this.model.combinedDescription){e.next=17;break}return e.prev=2,e.next=5,this.checkCombinedPluginCount();case 5:if(a=e.sent,!(a>0)){e.next=12;break}return e.next=9,new Promise((function(e){i.$confirm({title:"确认保存",content:"检测到还有 ".concat(a,' 个插件使用相同的组合插件名"').concat(i.model.combinedName,'"，保存后将同步更新它们的介绍。是否继续？'),okText:"确认保存",cancelText:"取消",onOk:function(){return e(!0)},onCancel:function(){return e(!1)}})}));case 9:if(r=e.sent,r){e.next=12;break}return e.abrupt("return");case 12:e.next=17;break;case 14:e.prev=14,e.t0=e["catch"](2);case 17:this.$refs.form.validate(function(){var e=u(n.a.mark((function e(a){var r,l,s;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=28;break}return t.confirmLoading=!0,e.prev=2,e.next=5,i.uploadPendingImages();case 5:return r="",l="",i.model.id?(r+=i.url.edit,l="put"):(r+=i.url.add,l="post"),e.next=10,Object(o["h"])(r,i.model,l);case 10:if(s=e.sent,!s.success){e.next=18;break}return e.next=14,i.confirmDeleteOriginalFiles();case 14:t.$message.success(s.message),t.$emit("ok"),e.next=19;break;case 18:t.$message.warning(s.message);case 19:e.next=25;break;case 21:e.prev=21,e.t0=e["catch"](2),t.$message.error("保存失败: "+(e.t0.message||"未知错误"));case 25:return e.prev=25,t.confirmLoading=!1,e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[2,21,25,28]])})));return function(t){return e.apply(this,arguments)}}());case 18:case"end":return e.stop()}}),e,this,[[2,14]])})));function t(){return e.apply(this,arguments)}return t}(),uploadPendingImages:function(){var e=u(n.a.mark((function e(){var t,a,r,i;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this.$refs.pluginImageUpload){e.next=23;break}if(!this.$refs.pluginImageUpload.hasPendingFiles()){e.next=20;break}return e.prev=4,e.next=7,this.$refs.pluginImageUpload.performUpload();case 7:e.sent,t=this.$refs.pluginImageUpload.getCurrentValue(),this.model.plubimg=t,e.next=18;break;case 14:throw e.prev=14,e.t0=e["catch"](4),e.t0;case 18:e.next=23;break;case 20:a=this.$refs.pluginImageUpload.getCurrentValue(),this.model.plubimg=a;case 23:if(!this.$refs.combinedImageUpload){e.next=45;break}if(!this.$refs.combinedImageUpload.hasPendingFiles()){e.next=42;break}return e.prev=26,e.next=29,this.$refs.combinedImageUpload.performUpload();case 29:e.sent,r=this.$refs.combinedImageUpload.getCurrentValue(),this.model.combinedImage=r,e.next=40;break;case 36:throw e.prev=36,e.t1=e["catch"](26),e.t1;case 40:e.next=45;break;case 42:i=this.$refs.combinedImageUpload.getCurrentValue(),this.model.combinedImage=i;case 45:case 46:case"end":return e.stop()}}),e,this,[[4,14],[26,36]])})));function t(){return e.apply(this,arguments)}return t}(),confirmDeleteOriginalFiles:function(){var e=u(n.a.mark((function e(){var t;return n.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=[],this.$refs.pluginImageUpload&&t.push(this.$refs.pluginImageUpload.confirmDeleteOriginalFiles()),this.$refs.combinedImageUpload&&t.push(this.$refs.combinedImageUpload.confirmDeleteOriginalFiles()),!(t.length>0)){e.next=13;break}return e.prev=4,e.next=7,Promise.all(t);case 7:e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](4);case 13:case"end":return e.stop()}}),e,this,[[4,10]])})));function t(){return e.apply(this,arguments)}return t}(),handleClose:function(){this.$refs.pluginImageUpload&&this.$refs.pluginImageUpload.rollbackChanges(),this.$refs.combinedImageUpload&&this.$refs.combinedImageUpload.rollbackChanges(),this.$emit("close")},showVideoUploadTip:function(){this.$message.info("暂不支持上传文件，请使用教程链接字段输入外部视频链接")}}},p=m,h=(a("facd8"),a("2877")),f=Object(h["a"])(p,r,i,!1,null,"0c5419f3",null);t["default"]=f.exports},"663e":function(e,t,a){},6973:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐关系ID",prop:"referralId"}},[a("a-input",{attrs:{placeholder:"请输入推荐关系ID"},model:{value:e.model.referralId,callback:function(t){e.$set(e.model,"referralId",t)},expression:"model.referralId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"推荐人ID",prop:"referrerId"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.model.referrerId,callback:function(t){e.$set(e.model,"referrerId",t)},expression:"model.referrerId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"被推荐人ID",prop:"refereeId"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.model.refereeId,callback:function(t){e.$set(e.model,"refereeId",t)},expression:"model.refereeId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"奖励类型",prop:"rewardType"}},[a("a-select",{attrs:{placeholder:"请选择奖励类型"},model:{value:e.model.rewardType,callback:function(t){e.$set(e.model,"rewardType",t)},expression:"model.rewardType"}},[a("a-select-option",{attrs:{value:1}},[e._v("注册奖励")]),a("a-select-option",{attrs:{value:2}},[e._v("首充奖励")]),a("a-select-option",{attrs:{value:3}},[e._v("升级奖励")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"奖励金额",prop:"rewardAmount"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:0,precision:2,placeholder:"请输入奖励金额"},model:{value:e.model.rewardAmount,callback:function(t){e.$set(e.model,"rewardAmount",t)},expression:"model.rewardAmount"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待发放")]),a("a-select-option",{attrs:{value:2}},[e._v("已发放")]),a("a-select-option",{attrs:{value:3}},[e._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"触发事件描述",prop:"triggerEvent"}},[a("a-input",{attrs:{placeholder:"请输入触发事件描述"},model:{value:e.model.triggerEvent,callback:function(t){e.$set(e.model,"triggerEvent",t)},expression:"model.triggerEvent"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"交易记录ID",prop:"transactionId"}},[a("a-input",{attrs:{placeholder:"请输入交易记录ID"},model:{value:e.model.transactionId,callback:function(t){e.$set(e.model,"transactionId",t)},expression:"model.transactionId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"发放时间",prop:"rewardTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择发放时间"},model:{value:e.model.rewardTime,callback:function(t){e.$set(e.model,"rewardTime",t)},expression:"model.rewardTime"}})],1)],1)],1)],1)],1)],1)},i=[],l=a("0fea"),n=a("c1df"),o=a.n(n),s={name:"AicgUserReferralRewardModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{referralId:[{required:!0,message:"请输入推荐关系ID!"}],referrerId:[{required:!0,message:"请输入推荐人ID!"}],refereeId:[{required:!0,message:"请输入被推荐人ID!"}],rewardType:[{required:!0,message:"请选择奖励类型!"}],rewardAmount:[{required:!0,message:"请输入奖励金额!"}]},url:{add:"/demo/referralreward/add",edit:"/demo/referralreward/edit",queryById:"/demo/referralreward/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={referralId:t.model.referralId,referrerId:t.model.referrerId,refereeId:t.model.refereeId,rewardType:t.model.rewardType,rewardAmount:t.model.rewardAmount,status:t.model.status,triggerEvent:t.model.triggerEvent,transactionId:t.model.transactionId};t.form.setFieldsValue(e),t.model.rewardTime&&(t.model.rewardTime=o()(t.model.rewardTime))})),e.id?this.title="编辑":(this.title="新增",this.model.status=1)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var n=Object.assign(e.model);n.rewardTime&&(n.rewardTime=n.rewardTime.format("YYYY-MM-DD HH:mm:ss")),Object(l["h"])(r,n,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},7290:function(e,t,a){"use strict";var r=a("6198"),i=a.n(r);i.a},7818:function(e,t,a){"use strict";var r=a("8b4b"),i=a.n(r);i.a},"84f8":function(e,t,a){"use strict";var r=a("4638"),i=a.n(r);i.a},8660:function(e,t,a){},8877:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户昵称"}},[a("a-input",{attrs:{placeholder:"请输入用户昵称"},model:{value:e.queryParam.userNickname,callback:function(t){e.$set(e.queryParam,"userNickname",t)},expression:"queryParam.userNickname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"API接口"}},[a("a-input",{attrs:{placeholder:"请输入API接口地址"},model:{value:e.queryParam.apiEndpoint,callback:function(t){e.$set(e.queryParam,"apiEndpoint",t)},expression:"queryParam.apiEndpoint"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"请求方法"}},[a("a-select",{attrs:{placeholder:"请选择请求方法",allowClear:""},model:{value:e.queryParam.apiMethod,callback:function(t){e.$set(e.queryParam,"apiMethod",t)},expression:"queryParam.apiMethod"}},[a("a-select-option",{attrs:{value:"GET"}},[e._v("GET")]),a("a-select-option",{attrs:{value:"POST"}},[e._v("POST")]),a("a-select-option",{attrs:{value:"PUT"}},[e._v("PUT")]),a("a-select-option",{attrs:{value:"DELETE"}},[e._v("DELETE")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"响应状态"}},[a("a-select",{attrs:{placeholder:"请选择响应状态",allowClear:""},model:{value:e.queryParam.responseStatus,callback:function(t){e.$set(e.queryParam,"responseStatus",t)},expression:"queryParam.responseStatus"}},[a("a-select-option",{attrs:{value:200}},[e._v("200 成功")]),a("a-select-option",{attrs:{value:400}},[e._v("400 请求错误")]),a("a-select-option",{attrs:{value:401}},[e._v("401 未授权")]),a("a-select-option",{attrs:{value:403}},[e._v("403 禁止访问")]),a("a-select-option",{attrs:{value:404}},[e._v("404 未找到")]),a("a-select-option",{attrs:{value:500}},[e._v("500 服务器错误")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("API使用记录")}}},[e._v("导出")]),a("a-button",{attrs:{type:"primary",icon:"bar-chart"},on:{click:e.showStats}},[e._v("统计分析")])],1),a("div",[a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"responseStatus",fn:function(t){return[a("a-tag",{attrs:{color:200===t?"green":"red"}},[e._v("\n          "+e._s(200===t?"成功":"失败("+t+")")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)}}])})],1),a("a-modal",{attrs:{title:"API使用记录详情",width:800,visible:e.detailVisible,footer:null},on:{cancel:function(t){e.detailVisible=!1}}},[e.detailData?a("div",[a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.detailData.user_id))]),a("a-descriptions-item",{attrs:{label:"用户昵称"}},[e._v(e._s(e.detailData.userNickname||"-"))]),a("a-descriptions-item",{attrs:{label:"API密钥"}},[e._v(e._s(e.detailData.api_key))]),a("a-descriptions-item",{attrs:{label:"API接口"}},[e._v(e._s(e.detailData.api_endpoint))]),a("a-descriptions-item",{attrs:{label:"请求方法"}},[e._v(e._s(e.detailData.api_method))]),a("a-descriptions-item",{attrs:{label:"响应状态"}},[a("a-tag",{attrs:{color:200===e.detailData.response_status?"green":"red"}},[e._v("\n            "+e._s(e.detailData.response_status)+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"响应时间"}},[e._v(e._s(e.detailData.response_time)+"ms")]),a("a-descriptions-item",{attrs:{label:"消耗Token"}},[e._v(e._s(e.detailData.tokens_used||"-"))]),a("a-descriptions-item",{attrs:{label:"消耗金额"}},[e._v("¥"+e._s(e.detailData.cost_amount||"0.00"))]),a("a-descriptions-item",{attrs:{label:"IP地址"}},[e._v(e._s(e.detailData.ip_address))]),a("a-descriptions-item",{attrs:{label:"调用时间"}},[e._v(e._s(e.formatDateTime(e.detailData.call_time)))]),a("a-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.formatDateTime(e.detailData.create_time)))])],1),a("h4",{staticStyle:{"margin-top":"16px"}},[e._v("请求参数")]),a("pre",{staticStyle:{background:"#f5f5f5",padding:"10px","border-radius":"4px","max-height":"200px","overflow-y":"auto"}},[e._v(e._s(e.detailData.request_params))]),e.detailData.error_message?a("div",[a("h4",[e._v("错误信息")]),a("a-alert",{attrs:{message:e.detailData.error_message,type:"error"}})],1):e._e(),e.detailData.plugin_name?a("div",[a("h4",[e._v("插件信息")]),a("a-descriptions",{attrs:{column:2,bordered:""}},[a("a-descriptions-item",{attrs:{label:"插件名称"}},[e._v(e._s(e.detailData.plugin_name))]),a("a-descriptions-item",{attrs:{label:"插件标识"}},[e._v(e._s(e.detailData.plugin_key))])],1)],1):e._e()],1):e._e()]),a("a-modal",{attrs:{title:"API使用统计分析",width:1e3,visible:e.statsVisible,footer:null},on:{cancel:function(t){e.statsVisible=!1}}},[e.statsData?a("div",[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总调用次数",value:e.statsData.total_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功调用",value:e.statsData.success_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"错误调用",value:e.statsData.error_calls}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"平均响应时间",value:e.statsData.avg_response_time,suffix:"ms"}})],1)],1),a("a-divider"),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:12}},[a("a-statistic",{attrs:{title:"总消耗Token",value:e.statsData.total_tokens}})],1),a("a-col",{attrs:{span:12}},[a("a-statistic",{attrs:{title:"总消耗金额",value:e.statsData.total_cost,prefix:"¥",precision:2}})],1)],1)],1):e._e()])],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o={name:"AicgUserApiUsageList",mixins:[n["a"],l["b"]],components:{},data:function(){return{description:"API使用记录管理页面",statsVisible:!1,statsData:null,detailVisible:!1,detailData:null,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"user_id",width:120},{title:"用户昵称",align:"center",dataIndex:"userNickname",width:120,ellipsis:!0},{title:"API接口",align:"center",dataIndex:"api_endpoint",width:200,ellipsis:!0},{title:"请求方法",align:"center",dataIndex:"api_method",width:80},{title:"响应状态",align:"center",dataIndex:"response_status",width:100,scopedSlots:{customRender:"responseStatus"}},{title:"响应时间(ms)",align:"center",dataIndex:"response_time",width:120,customRender:function(e){return e?"".concat(e,"ms"):"-"}},{title:"消耗Token",align:"center",dataIndex:"tokens_used",width:100,customRender:function(e){return e||"-"}},{title:"消耗金额",align:"center",dataIndex:"cost_amount",width:100,customRender:function(e){return e?"¥".concat(e):"-"}},{title:"调用时间",align:"center",dataIndex:"call_time",width:150,customRender:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:120,scopedSlots:{customRender:"action"}}],url:{list:"/demo/apiusage/list",delete:"/demo/apiusage/delete",deleteBatch:"/demo/apiusage/deleteBatch",exportXlsUrl:"/demo/apiusage/exportXls",importExcelUrl:"demo/apiusage/importExcel",edit:"/demo/apiusage/queryById"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"user_id",text:"用户ID"}),e.push({type:"string",value:"userNickname",text:"用户昵称"}),e.push({type:"string",value:"api_endpoint",text:"API接口地址"}),e.push({type:"string",value:"api_method",text:"请求方法"}),e.push({type:"int",value:"response_status",text:"响应状态码"}),e.push({type:"int",value:"response_time",text:"响应时间"}),e.push({type:"int",value:"tokens_used",text:"消耗Token数量"}),e.push({type:"BigDecimal",value:"cost_amount",text:"消耗金额"}),e.push({type:"Date",value:"call_time",text:"调用时间"}),this.superFieldList=e},showStats:function(){var e=this;this.$http.get("/demo/apiusage/getStats?userId=&timeRange=month").then((function(t){t.success?(e.statsData=t.result,e.statsVisible=!0):e.$message.error(t.message)}))},handleDetail:function(e){this.detailData=e,this.detailVisible=!0},formatDateTime:function(e){return e?new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"-"}}},s=o,c=(a("c310"),a("2877")),d=Object(c["a"])(s,r,i,!1,null,"05aabdbb",null);t["default"]=d.exports},"899a":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入推荐人ID"},model:{value:e.queryParam.referrerId,callback:function(t){e.$set(e.queryParam,"referrerId",t)},expression:"queryParam.referrerId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"被推荐人ID"}},[a("a-input",{attrs:{placeholder:"请输入被推荐人ID"},model:{value:e.queryParam.refereeId,callback:function(t){e.$set(e.queryParam,"refereeId",t)},expression:"queryParam.refereeId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"推荐码"}},[a("a-input",{attrs:{placeholder:"请输入推荐码"},model:{value:e.queryParam.referralCode,callback:function(t){e.$set(e.queryParam,"referralCode",t)},expression:"queryParam.referralCode"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态",allowClear:""},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:1}},[e._v("待确认")]),a("a-select-option",{attrs:{value:2}},[e._v("已确认")]),a("a-select-option",{attrs:{value:3}},[e._v("已奖励")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("推荐关系")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),1===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleConfirm(r)}}},[e._v("确认推荐")])]):e._e(),2===r.status?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleMarkRewarded(r)}}},[e._v("标记已奖励")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-referral-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("1de8"),s={name:"AicgUserReferralList",mixins:[n["a"],l["b"]],components:{AicgUserReferralModal:o["default"]},data:function(){return{description:"用户推荐关系管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"推荐人ID",align:"center",dataIndex:"referrerId"},{title:"被推荐人ID",align:"center",dataIndex:"refereeId"},{title:"推荐码",align:"center",dataIndex:"referralCode"},{title:"注册时间",align:"center",dataIndex:"registerTime"},{title:"首次充值时间",align:"center",dataIndex:"firstRechargeTime"},{title:"首次充值金额",align:"center",dataIndex:"firstRechargeAmount",customRender:function(e){return e?"¥".concat(e):"-"}},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){var t={1:"待确认",2:"已确认",3:"已奖励"},a={1:"orange",2:"blue",3:"green"};return'<span style="color: '.concat(a[e],'">').concat(t[e]||e,"</span>")},scopedSlots:{customRender:"htmlSlot"}},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/referral/list",delete:"/demo/referral/delete",deleteBatch:"/demo/referral/deleteBatch",exportXlsUrl:"/demo/referral/exportXls",importExcelUrl:"demo/referral/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"referrerId",text:"推荐人ID"}),e.push({type:"string",value:"refereeId",text:"被推荐人ID"}),e.push({type:"string",value:"referralCode",text:"推荐码"}),e.push({type:"Date",value:"registerTime",text:"注册时间"}),e.push({type:"Date",value:"firstRechargeTime",text:"首次充值时间"}),e.push({type:"BigDecimal",value:"firstRechargeAmount",text:"首次充值金额"}),e.push({type:"int",value:"status",text:"状态"}),this.superFieldList=e},handleConfirm:function(e){var t=this;this.$confirm({title:"确认推荐",content:"确定要确认该推荐关系吗？",onOk:function(){t.$http.post("/demo/referral/confirm?refereeId=".concat(e.refereeId,"&rechargeAmount=").concat(e.firstRechargeAmount||0)).then((function(e){e.success?(t.$message.success("确认成功"),t.loadData()):t.$message.error(e.message)}))}})},handleMarkRewarded:function(e){var t=this;this.$confirm({title:"标记已奖励",content:"确定要标记该推荐关系为已奖励吗？",onOk:function(){t.$http.post("/demo/referral/markRewarded?id=".concat(e.id)).then((function(e){e.success?(t.$message.success("标记成功"),t.loadData()):t.$message.error(e.message)}))}})}}},c=s,d=(a("f7f1"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"63d19bce",null);t["default"]=u.exports},"8b4b":function(e,t,a){},9521:function(e,t,a){},"9caa":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("aigc-plub-shop-form",{ref:"realForm",attrs:{disabled:e.disableSubmit},on:{ok:e.submitCallback}})],1)},i=[],l=a("63be"),n={name:"AigcPlubShopModal",components:{AigcPlubShopForm:l["default"]},data:function(){return{title:"",width:1200,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.$refs.realForm&&this.$refs.realForm.handleClose&&this.$refs.realForm.handleClose(),this.close()}}},o=n,s=a("2877"),c=Object(s["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},a3f8:function(e,t,a){},bc94:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"用户ID",prop:"userId"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.model.userId,callback:function(t){e.$set(e.model,"userId",t)},expression:"model.userId"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"通知类型",prop:"type"}},[a("a-select",{attrs:{placeholder:"请选择通知类型"},model:{value:e.model.type,callback:function(t){e.$set(e.model,"type",t)},expression:"model.type"}},[a("a-select-option",{attrs:{value:1}},[e._v("系统通知")]),a("a-select-option",{attrs:{value:2}},[e._v("交易通知")]),a("a-select-option",{attrs:{value:3}},[e._v("安全提醒")]),a("a-select-option",{attrs:{value:4}},[e._v("营销推送")])],1)],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"通知标题",prop:"title"}},[a("a-input",{attrs:{placeholder:"请输入通知标题"},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"通知内容",prop:"content"}},[a("a-textarea",{attrs:{placeholder:"请输入通知内容",rows:4},model:{value:e.model.content,callback:function(t){e.$set(e.model,"content",t)},expression:"model.content"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"优先级",prop:"priority"}},[a("a-select",{attrs:{placeholder:"请选择优先级"},model:{value:e.model.priority,callback:function(t){e.$set(e.model,"priority",t)},expression:"model.priority"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通")]),a("a-select-option",{attrs:{value:2}},[e._v("重要")]),a("a-select-option",{attrs:{value:3}},[e._v("紧急")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"是否已读",prop:"isRead"}},[a("a-select",{attrs:{placeholder:"请选择是否已读"},model:{value:e.model.isRead,callback:function(t){e.$set(e.model,"isRead",t)},expression:"model.isRead"}},[a("a-select-option",{attrs:{value:0}},[e._v("未读")]),a("a-select-option",{attrs:{value:1}},[e._v("已读")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"跳转链接",prop:"actionUrl"}},[a("a-input",{attrs:{placeholder:"请输入跳转链接"},model:{value:e.model.actionUrl,callback:function(t){e.$set(e.model,"actionUrl",t)},expression:"model.actionUrl"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"阅读时间",prop:"readTime"}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{"show-time":"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"请选择阅读时间"},model:{value:e.model.readTime,callback:function(t){e.$set(e.model,"readTime",t)},expression:"model.readTime"}})],1)],1)],1)],1)],1)],1)},i=[],l=a("0fea"),n=a("c1df"),o=a.n(n),s={name:"AicgUserNotificationModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{userId:[{required:!0,message:"请输入用户ID!"}],type:[{required:!0,message:"请选择通知类型!"}],title:[{required:!0,message:"请输入通知标题!"}],content:[{required:!0,message:"请输入通知内容!"}],priority:[{required:!0,message:"请选择优先级!"}]},url:{add:"/demo/notification/add",edit:"/demo/notification/edit",queryById:"/demo/notification/queryById"}}},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){var e={userId:t.model.userId,type:t.model.type,title:t.model.title,content:t.model.content,priority:t.model.priority,isRead:t.model.isRead,actionUrl:t.model.actionUrl};t.form.setFieldsValue(e),t.model.readTime&&(t.model.readTime=o()(t.model.readTime))})),e.id?this.title="编辑":(this.title="新增",this.model.priority=1,this.model.isRead=0)},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post");var n=Object.assign(e.model);n.readTime&&(n.readTime=n.readTime.format("YYYY-MM-DD HH:mm:ss")),Object(l["h"])(r,n,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},c=s,d=a("2877"),u=Object(d["a"])(c,r,i,!1,null,null,null);t["default"]=u.exports},c1ca:function(e,t,a){"use strict";var r=a("8660"),i=a.n(r);i.a},c310:function(e,t,a){"use strict";var r=a("484b"),i=a.n(r);i.a},c3c7:function(e,t,a){"use strict";var r=a("9521"),i=a.n(r);i.a},d403:function(e,t,a){"use strict";var r=a("24e6"),i=a.n(r);i.a},d846:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-plub-shop-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],l=a("63be"),n={name:"AigcPlubShopModal",components:{AigcPlubShopForm:l["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=n,s=(a("7290"),a("2877")),c=Object(s["a"])(o,r,i,!1,null,"57b2909c",null);t["default"]=c.exports},d901:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"创作者名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"authorname"}},[a("a-input",{attrs:{placeholder:"请输入创作者名称"},model:{value:e.model.authorname,callback:function(t){e.$set(e.model,"authorname",t)},expression:"model.authorname"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"作者职位",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"title"}},[a("j-dict-select-tag",{attrs:{dictCode:"author_title",placeholder:"请选择作者职位",triggerChange:!0},model:{value:e.model.title,callback:function(t){e.$set(e.model,"title",t)},expression:"model.title"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"专业领域",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"expertise"}},[a("j-multi-select-tag",{attrs:{dictCode:"author_expertise",placeholder:"请选择专业领域（可多选）"},model:{value:e.model.expertise,callback:function(t){e.$set(e.model,"expertise",t)},expression:"model.expertise"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubnum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.plubnum,callback:function(t){e.$set(e.model,"plubnum",t)},expression:"model.plubnum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              此字段由系统自动统计，无需手动输入\n            ")])],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"插件使用总数",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"plubusenum"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{placeholder:"系统自动统计",disabled:!0},model:{value:e.model.plubusenum,callback:function(t){e.$set(e.model,"plubusenum",t)},expression:"model.plubusenum"}}),a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"4px"}},[e._v("\n              此字段由系统自动统计，无需手动输入\n            ")])],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"创作者简介",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"createinfo"}},[a("a-textarea",{attrs:{rows:"4",placeholder:"请输入创作者简介"},model:{value:e.model.createinfo,callback:function(t){e.$set(e.model,"createinfo",t)},expression:"model.createinfo"}})],1)],1)],1)],1)],1)],1)},i=[],l=a("0fea"),n=(a("ca00"),{name:"AigcPlubAuthorForm",components:{},props:{disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{plubnum:0,plubusenum:0},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{},url:{add:"/plubauthor/aigcPlubAuthor/add",edit:"/plubauthor/aigcPlubAuthor/edit",queryById:"/plubauthor/aigcPlubAuthor/queryById"}}},computed:{formDisabled:function(){return this.disabled}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){var e=Object.assign({},this.modelDefault,{plubnum:0,plubusenum:0});this.edit(e)},edit:function(e){this.model=Object.assign({},e),this.visible=!0},submitForm:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(l["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))}}}),o=n,s=a("2877"),c=Object(s["a"])(o,r,i,!1,null,null,null);t["default"]=c.exports},e3ec:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.width,placement:"right",closable:!1,destroyOnClose:"",visible:e.visible},on:{close:e.close}},[a("aigc-plub-author-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}}),a("div",{staticClass:"drawer-footer"},[a("a-button",{staticStyle:{"margin-bottom":"0"},on:{click:e.handleCancel}},[e._v("关闭")]),e.disableSubmit?e._e():a("a-button",{staticStyle:{"margin-bottom":"0"},attrs:{type:"primary"},on:{click:e.handleOk}},[e._v("提交")])],1)],1)},i=[],l=a("d901"),n={name:"AigcPlubAuthorModal",components:{AigcPlubAuthorForm:l["default"]},data:function(){return{title:"操作",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.add()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.edit(e)}))},close:function(){this.$emit("close"),this.visible=!1},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},handleCancel:function(){this.close()}}},o=n,s=(a("3450"),a("2877")),c=Object(s["a"])(o,r,i,!1,null,"43dfa3dc",null);t["default"]=c.exports},ecbf:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"作者类型"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择作者类型",dictCode:"author_type"},model:{value:e.queryParam.authorType,callback:function(t){e.$set(e.queryParam,"authorType",t)},expression:"queryParam.authorType"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"智能体名称"}},[a("a-input",{attrs:{placeholder:"请输入智能体名称"},model:{value:e.queryParam.agentName,callback:function(t){e.$set(e.queryParam,"agentName",t)},expression:"queryParam.agentName"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:10,lg:11,md:12,sm:24}},[a("a-form-item",{attrs:{label:"价格（元）"}},[a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最小值"},model:{value:e.queryParam.price_begin,callback:function(t){e.$set(e.queryParam,"price_begin",t)},expression:"queryParam.price_begin"}}),a("span",{staticClass:"query-group-split-cust"}),a("a-input",{staticClass:"query-group-cust",attrs:{placeholder:"请输入最大值"},model:{value:e.queryParam.price_end,callback:function(t){e.$set(e.queryParam,"price_end",t)},expression:"queryParam.price_end"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"审核状态"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择审核状态",dictCode:"audit_status"},model:{value:e.queryParam.auditStatus,callback:function(t){e.$set(e.queryParam,"auditStatus",t)},expression:"queryParam.auditStatus"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("智能体表")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",bordered:"",rowKey:"id",scroll:{x:!0},columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aigc-agent-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=a("b65a"),n=a("42e3"),o=(a("89f2"),a("6eb7"),{name:"AigcAgentList",mixins:[l["a"]],components:{AigcAgentModal:n["default"]},data:function(){return{description:"智能体表管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"作者类型",align:"center",dataIndex:"authorType_dictText"},{title:"智能体名称",align:"center",dataIndex:"agentName"},{title:"智能体描述",align:"center",dataIndex:"agentDescription"},{title:"智能体头像",align:"center",dataIndex:"agentAvatar",scopedSlots:{customRender:"imgSlot"}},{title:"展示视频",align:"center",dataIndex:"demoVideo",scopedSlots:{customRender:"fileSlot"}},{title:"体验链接",align:"center",dataIndex:"experienceLink"},{title:"价格（元）",align:"center",dataIndex:"price"},{title:"审核状态",align:"center",dataIndex:"auditStatus_dictText"},{title:"审核备注",align:"center",dataIndex:"auditRemark"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/aigc_agent/aigcAgent/list",delete:"/aigc_agent/aigcAgent/delete",deleteBatch:"/aigc_agent/aigcAgent/deleteBatch",exportXlsUrl:"/aigc_agent/aigcAgent/exportXls",importExcelUrl:"aigc_agent/aigcAgent/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"authorType",text:"作者类型",dictCode:"author_type"}),e.push({type:"string",value:"agentName",text:"智能体名称",dictCode:""}),e.push({type:"string",value:"agentDescription",text:"智能体描述",dictCode:""}),e.push({type:"string",value:"agentAvatar",text:"智能体头像",dictCode:""}),e.push({type:"string",value:"demoVideo",text:"展示视频",dictCode:""}),e.push({type:"string",value:"experienceLink",text:"体验链接",dictCode:""}),e.push({type:"BigDecimal",value:"price",text:"价格（元）",dictCode:""}),e.push({type:"string",value:"auditStatus",text:"审核状态",dictCode:"audit_status"}),e.push({type:"string",value:"auditRemark",text:"审核备注",dictCode:""}),this.superFieldList=e}}}),s=o,c=(a("49df"),a("2877")),d=Object(c["a"])(s,r,i,!1,null,"6d83eb9a",null);t["default"]=d.exports},f4b7:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"用户ID"}},[a("a-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.queryParam.userId,callback:function(t){e.$set(e.queryParam,"userId",t)},expression:"queryParam.userId"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"通知类型"}},[a("a-select",{attrs:{placeholder:"请选择通知类型",allowClear:""},model:{value:e.queryParam.type,callback:function(t){e.$set(e.queryParam,"type",t)},expression:"queryParam.type"}},[a("a-select-option",{attrs:{value:1}},[e._v("系统通知")]),a("a-select-option",{attrs:{value:2}},[e._v("交易通知")]),a("a-select-option",{attrs:{value:3}},[e._v("安全提醒")]),a("a-select-option",{attrs:{value:4}},[e._v("营销推送")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"优先级"}},[a("a-select",{attrs:{placeholder:"请选择优先级",allowClear:""},model:{value:e.queryParam.priority,callback:function(t){e.$set(e.queryParam,"priority",t)},expression:"queryParam.priority"}},[a("a-select-option",{attrs:{value:1}},[e._v("普通")]),a("a-select-option",{attrs:{value:2}},[e._v("重要")]),a("a-select-option",{attrs:{value:3}},[e._v("紧急")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"是否已读"}},[a("a-select",{attrs:{placeholder:"请选择是否已读",allowClear:""},model:{value:e.queryParam.isRead,callback:function(t){e.$set(e.queryParam,"isRead",t)},expression:"queryParam.isRead"}},[a("a-select-option",{attrs:{value:0}},[e._v("未读")]),a("a-select-option",{attrs:{value:1}},[e._v("已读")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",{staticClass:"table-operator"},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新增")]),a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("用户通知消息")}}},[e._v("导出")]),e.selectedRowKeys.length>0?a("a-button",{attrs:{type:"primary",icon:"check"},on:{click:e.batchMarkRead}},[e._v("批量标记已读")]):e._e(),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"priority",fn:function(t){return[a("a-tag",{attrs:{color:1===t?"blue":2===t?"orange":"red"}},[e._v("\n          "+e._s(1===t?"普通":2===t?"重要":3===t?"紧急":t)+"\n        ")])]}},{key:"isRead",fn:function(t){return[a("a-tag",{attrs:{color:0===t?"red":"green"}},[e._v("\n          "+e._s(0===t?"未读":"已读")+"\n        ")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),0===r.isRead?a("a-menu-item",[a("a",{on:{click:function(t){return e.handleMarkRead(r)}}},[e._v("标记已读")])]):e._e(),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("aicg-user-notification-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("bc94"),s={name:"AicgUserNotificationList",mixins:[n["a"],l["b"]],components:{AicgUserNotificationModal:o["default"]},data:function(){return{description:"用户通知消息管理页面",columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户ID",align:"center",dataIndex:"userId",width:120},{title:"通知标题",align:"center",dataIndex:"title",width:200,ellipsis:!0},{title:"通知类型",align:"center",dataIndex:"type",width:100,customRender:function(e){var t={1:"系统通知",2:"交易通知",3:"安全提醒",4:"营销推送"};return t[e]||e}},{title:"优先级",align:"center",dataIndex:"priority",width:80,scopedSlots:{customRender:"priority"}},{title:"是否已读",align:"center",dataIndex:"isRead",width:100,scopedSlots:{customRender:"isRead"}},{title:"阅读时间",align:"center",dataIndex:"readTime",width:150},{title:"创建时间",align:"center",dataIndex:"createTime",width:150},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/demo/notification/list",delete:"/demo/notification/delete",deleteBatch:"/demo/notification/deleteBatch",exportXlsUrl:"/demo/notification/exportXls",importExcelUrl:"demo/notification/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)}},methods:{initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"userId",text:"用户ID"}),e.push({type:"string",value:"title",text:"通知标题"}),e.push({type:"int",value:"type",text:"通知类型"}),e.push({type:"int",value:"priority",text:"优先级"}),e.push({type:"int",value:"isRead",text:"是否已读"}),e.push({type:"Date",value:"readTime",text:"阅读时间"}),e.push({type:"Date",value:"createTime",text:"创建时间"}),this.superFieldList=e},handleMarkRead:function(e){var t=this;this.$confirm({title:"标记已读",content:"确定要标记该通知为已读吗？",onOk:function(){t.$http.post("/demo/notification/markRead?id=".concat(e.id,"&userId=").concat(e.userId)).then((function(e){e.success?(t.$message.success("标记成功"),t.loadData()):t.$message.error(e.message)}))}})},batchMarkRead:function(){var e=this;0!==this.selectedRowKeys.length?this.$confirm({title:"批量标记已读",content:"确定要标记选中的 ".concat(this.selectedRowKeys.length," 条通知为已读吗？"),onOk:function(){var t=e.dataSource.find((function(t){return t.id===e.selectedRowKeys[0]}));t?e.$http.post("/demo/notification/batchMarkRead?userId=".concat(t.userId,"&ids=").concat(e.selectedRowKeys.join(","))).then((function(t){t.success?(e.$message.success(t.message),e.loadData(),e.onClearSelected()):e.$message.error(t.message)})):e.$message.error("获取用户ID失败")}}):this.$message.warning("请选择要标记的通知")}}},c=s,d=(a("7818"),a("2877")),u=Object(d["a"])(c,r,i,!1,null,"31be6548",null);t["default"]=u.exports},f7f1:function(e,t,a){"use strict";var r=a("663e"),i=a.n(r);i.a},facd8:function(e,t,a){"use strict";var r=a("31bf"),i=a.n(r);i.a},fe20:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"创作者名称"}},[a("a-input",{attrs:{placeholder:"请输入创作者名称"},model:{value:e.queryParam.authorname,callback:function(t){e.$set(e.queryParam,"authorname",t)},expression:"queryParam.authorname"}})],1)],1),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件数"}},[a("a-input",{attrs:{placeholder:"请输入插件数"},model:{value:e.queryParam.plubnum,callback:function(t){e.$set(e.queryParam,"plubnum",t)},expression:"queryParam.plubnum"}})],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("a-form-item",{attrs:{label:"插件使用总数"}},[a("a-input",{attrs:{placeholder:"请输入插件使用总数"},model:{value:e.queryParam.plubusenum,callback:function(t){e.$set(e.queryParam,"plubusenum",t)},expression:"queryParam.plubusenum"}})],1)],1)]:e._e(),a("a-col",{attrs:{xl:6,lg:7,md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n              "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n              "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("div",{staticClass:"table-operator"},[e.isAdmin||!e.hasAuthorRecord?a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("\n      新增\n    ")]):e._e(),e.isAdmin?[a("a-button",{attrs:{type:"primary",icon:"download"},on:{click:function(t){return e.handleExportXls("插件创作者")}}},[e._v("导出")]),a("a-upload",{attrs:{name:"file",showUploadList:!1,multiple:!1,headers:e.tokenHeader,action:e.importExcelUrl},on:{change:e.handleImportExcel}},[a("a-button",{attrs:{type:"primary",icon:"import"}},[e._v("导入")])],1),a("a-button",{attrs:{type:"default",icon:"sync",loading:e.updateAllLoading},on:{click:e.handleUpdateAllPluginCounts}},[e._v("更新所有插件数")]),a("j-super-query",{ref:"superQueryModal",attrs:{fieldList:e.superFieldList},on:{handleSuperQuery:e.handleSuperQuery}}),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()]:e._e(),!e.isAdmin&&e.hasAuthorRecord?a("div",{staticStyle:{color:"#999","font-size":"12px","margin-top":"8px"}},[e._v("\n      您已创建作者信息，如需修改请点击编辑\n    ")]):e._e()],2),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",staticClass:"j-table-force-nowrap",attrs:{size:"middle",scroll:{x:!0},bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"htmlSlot",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(t)}})]}},{key:"imgSlot",fn:function(t){return[t?a("img",{staticStyle:{"max-width":"80px","font-size":"12px","font-style":"italic"},attrs:{src:e.getImgView(t),height:"25px",alt:""}}):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无图片")])]}},{key:"fileSlot",fn:function(t){return[t?a("a-button",{attrs:{ghost:!0,type:"primary",icon:"download",size:"small"},on:{click:function(a){return e.downloadFile(t)}}},[e._v("\n          下载\n        ")]):a("span",{staticStyle:{"font-size":"12px","font-style":"italic"}},[e._v("无文件")])]}},{key:"action",fn:function(t,r){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(r)}}},[e._v("编辑")]),e.isAdmin?[a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handleDetail(r)}}},[e._v("详情")])]),a("a-menu-item",[a("a",{attrs:{disabled:r.updateLoading},on:{click:function(t){return e.handleUpdatePluginCount(r.id)}}},[a("a-icon",{attrs:{type:"sync",spin:r.updateLoading}}),e._v("\n                  更新插件数\n                ")],1)]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(r.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)]:e._e()],2)}}])})],1),a("aigc-plub-author-modal",{ref:"modalForm",on:{ok:e.modalFormOk}})],1)},i=[],l=(a("6eb7"),a("ac0d")),n=a("b65a"),o=a("266d"),s=a("ca00"),c={name:"AigcPlubAuthorList",mixins:[n["a"],l["b"]],components:{AigcPlubAuthorModal:o["default"]},data:function(){return{description:"插件创作者管理页面",updateAllLoading:!1,columns:[{title:"#",dataIndex:"",key:"rowIndex",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"创作者名称",align:"center",dataIndex:"authorname"},{title:"作者职位",align:"center",dataIndex:"title_dictText"},{title:"专业领域",align:"center",dataIndex:"expertise_dictText",customRender:function(e){if(!e)return"-";var t=e.split(",");return t.length>2?t.slice(0,2).join(", ")+"...":e}},{title:"插件数",align:"center",dataIndex:"plubnum"},{title:"插件使用总数",align:"center",dataIndex:"plubusenum"},{title:"累计收益",align:"center",dataIndex:"totalIncome",customRender:function(e){return e?"¥"+parseFloat(e).toFixed(2):"¥0.00"}},{title:"创作者简介",align:"center",dataIndex:"createinfo"},{title:"操作",dataIndex:"action",align:"center",fixed:"right",width:147,scopedSlots:{customRender:"action"}}],url:{list:"/plubauthor/aigcPlubAuthor/list",delete:"/plubauthor/aigcPlubAuthor/delete",deleteBatch:"/plubauthor/aigcPlubAuthor/deleteBatch",exportXlsUrl:"/plubauthor/aigcPlubAuthor/exportXls",importExcelUrl:"plubauthor/aigcPlubAuthor/importExcel"},dictOptions:{},superFieldList:[]}},created:function(){this.getSuperFieldList()},computed:{importExcelUrl:function(){return"".concat(window._CONFIG["domianURL"],"/").concat(this.url.importExcelUrl)},isAdmin:function(){var e=localStorage.getItem("userRole");return e&&e.toLowerCase().includes("admin")},hasAuthorRecord:function(){return this.dataSource&&this.dataSource.length>0}},methods:{getQueryParams:function(){var e=Object.assign({},this.queryParam,this.isorter,this.filters);return this.isAdmin||(e.create_by=this.$store.getters.userInfo.username),e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,Object(s["d"])(e)},initDictConfig:function(){},getSuperFieldList:function(){var e=[];e.push({type:"string",value:"authorname",text:"创作者名称",dictCode:""}),e.push({type:"int",value:"plubnum",text:"插件数",dictCode:""}),e.push({type:"int",value:"plubusenum",text:"插件使用总数",dictCode:""}),e.push({type:"decimal",value:"totalIncome",text:"累计收益",dictCode:""}),e.push({type:"string",value:"createinfo",text:"创作者简介",dictCode:""}),this.superFieldList=e},handleUpdatePluginCount:function(e){var t=this,a=this,r=this.dataSource.find((function(t){return t.id===e}));r&&this.$set(r,"updateLoading",!0),this.$http.post("/plubauthor/aigcPlubAuthor/updatePluginCount",null,{params:{id:e}}).then((function(e){e.success?(a.$message.success("更新插件数成功！"),a.loadData()):a.$message.error(e.message||"更新插件数失败！")})).catch((function(e){a.$message.error("更新插件数异常！")})).finally((function(){r&&t.$set(r,"updateLoading",!1)}))},handleUpdateAllPluginCounts:function(){var e=this,t=this;this.updateAllLoading=!0,this.$http.post("/plubauthor/aigcPlubAuthor/updateAllPluginCounts").then((function(e){e.success?(t.$message.success(e.message||"批量更新插件数成功！"),t.loadData()):t.$message.error(e.message||"批量更新插件数失败！")})).catch((function(e){t.$message.error("批量更新插件数异常！")})).finally((function(){e.updateAllLoading=!1}))}}},d=c,u=(a("c1ca"),a("2877")),m=Object(u["a"])(d,r,i,!1,null,"7b4bf869",null);t["default"]=m.exports}}]);