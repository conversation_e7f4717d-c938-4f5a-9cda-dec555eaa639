(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~b90aa0de"],{"0088":function(t,e,a){},"02fe":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"affiliate-container"},[t.loading?a("div",{staticClass:"affiliate-loading-overlay"},[a("div",{staticClass:"loading-content"},[a("div",{staticClass:"loading-spinner"},[a("a-spin",{attrs:{size:"large"}})],1),a("div",{staticClass:"loading-text"},[a("h3",[t._v("正在加载邀请奖励")]),a("p",[t._v("正在为您准备推荐数据，请稍候...")])])])]):t._e(),a("transition",{attrs:{name:"content-fade",appear:""}},[t.loading?t._e():a("div",{staticClass:"affiliate-main"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("邀请奖励")]),a("p",{staticClass:"simple-subtitle"},[t._v("邀请好友注册智界AIGC，获得丰厚奖励")]),a("div",{staticClass:"commission-badge"},[a("span",{staticClass:"badge-text"},[t._v("当前奖励比例："+t._s(t.currentCommissionRate)+"%")]),a("span",{staticClass:"badge-level"},[t._v(t._s(t.commissionLevelText))])])]),a("section",{staticClass:"affiliate-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"promotion-link-section"},[a("h2",{staticClass:"section-title"},[t._v("您的专属邀请链接")]),a("div",{staticClass:"link-main-container"},[a("div",{staticClass:"link-input-large"},[a("a-input",{attrs:{value:t.affiliateLink||"正在生成邀请链接...",readonly:"",loading:t.loading,size:"large",placeholder:"邀请链接生成中..."}})],1),a("div",{staticClass:"link-actions"},[a("a-button",{staticClass:"copy-btn",attrs:{type:"primary",size:"large",disabled:!t.affiliateLink||t.loading},on:{click:t.copyLink}},[a("a-icon",{attrs:{type:"copy"}}),t._v("\n                复制链接\n              ")],1),a("a-button",{staticClass:"qr-btn",attrs:{size:"large",loading:t.qrLoading},on:{click:t.generateQRCode}},[a("a-icon",{attrs:{type:"qrcode"}}),t._v("\n                邀请二维码\n              ")],1)],1)]),a("div",{staticClass:"link-tips"},[a("a-icon",{attrs:{type:"info-circle"}}),t._v("\n            分享此链接，您将获得好友付费的 "),a("strong",[t._v(t._s(t.currentCommissionRate)+"%")]),t._v(" 奖励\n          ")],1)]),a("div",{staticClass:"earnings-dashboard"},[a("h2",{staticClass:"section-title"},[t._v("收益概览")]),a("div",{staticClass:"earnings-grid"},[a("div",{staticClass:"earning-card primary"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"dollar"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v("¥"+t._s(t.formatNumber(t.totalEarnings)))]),a("div",{staticClass:"earning-label"},[t._v("累计收益")])])],1)]),a("div",{staticClass:"earning-card success"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"wallet"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v("¥"+t._s(t.formatNumber(t.availableEarnings)))]),a("div",{staticClass:"earning-label"},[t._v("可提现金额")])]),a("div",{staticClass:"card-action"},[a("a-button",{attrs:{type:"primary",size:"small",disabled:t.availableEarnings<=0||t.loading},on:{click:t.openWithdrawModal}},[t._v("\n                    立即提现\n                  ")])],1)],1)]),a("div",{staticClass:"earning-card info"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"team"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v(t._s(Math.floor(t.totalReferrals)))]),a("div",{staticClass:"earning-label"},[t._v("邀请注册人数")])])],1)]),a("div",{staticClass:"earning-card warning"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"crown"}})],1),a("div",{staticClass:"card-content"},[a("a-spin",{attrs:{spinning:t.loading,size:"small"}},[a("div",{staticClass:"earning-number"},[t._v(t._s(Math.floor(t.memberReferrals)))]),a("div",{staticClass:"earning-label"},[t._v("转化人数")])])],1)])])]),a("div",{staticClass:"commission-progress"},[a("h2",{staticClass:"section-title"},[t._v("奖励等级进度")]),a("div",{staticClass:"progress-card"},[a("div",{staticClass:"level-timeline-horizontal"},t._l(t.commissionLevels,(function(e,i){return a("div",{key:i,staticClass:"level-step-horizontal",class:{current:e.isCurrent,completed:e.isCompleted,upcoming:e.isUpcoming}},[a("div",{staticClass:"step-circle-horizontal"},[e.isCompleted?a("a-icon",{attrs:{type:"check"}}):e.isCurrent?a("span",{staticClass:"current-dot"}):a("span",{staticClass:"step-number"},[t._v(t._s(i+1))])],1),a("div",{staticClass:"step-content-horizontal"},[a("div",{staticClass:"step-title"},[t._v(t._s(e.name))]),a("div",{staticClass:"step-rate"},[t._v(t._s(e.rate)+"%")]),a("div",{staticClass:"step-requirement"},[t._v(t._s(e.requirement)+"人")]),e.remaining>0?a("div",{staticClass:"step-remaining"},[t._v("\n                    还需"+t._s(e.remaining)+"个\n                  ")]):e.isCompleted?a("div",{staticClass:"step-completed"},[t._v("\n                    已达成\n                  ")]):t._e()]),i<t.commissionLevels.length-1?a("div",{staticClass:"step-line-horizontal"}):t._e()])})),0)])]),a("div",{staticClass:"commission-rules"},[a("h2",{staticClass:"section-title"},[t._v("奖励规则")]),a("div",{staticClass:"rules-table"},[a("div",{staticClass:"rule-row header"},[a("div",{staticClass:"rule-cell"},[t._v("用户等级")]),a("div",{staticClass:"rule-cell"},[t._v("邀请人数要求")]),a("div",{staticClass:"rule-cell"},[t._v("奖励比例")]),a("div",{staticClass:"rule-cell"},[t._v("说明")])]),a("a-spin",{attrs:{spinning:t.loading,size:"small"}},t._l(t.allLevelConfigs,(function(e){return a("div",{key:e.id,staticClass:"rule-row",class:{vip:"VIP"===e.role_code,svip:"SVIP"===e.role_code}},[a("div",{staticClass:"rule-cell"},[t._v(t._s(t.getRoleDisplayName(e.role_code)))]),a("div",{staticClass:"rule-cell"},[t._v(t._s(t.getRequirementText(e)))]),a("div",{staticClass:"rule-cell highlight"},[t._v(t._s(e.commission_rate)+"%")]),a("div",{staticClass:"rule-cell"},[t._v(t._s(e.level_name))])])})),0)],1)]),a("div",{staticClass:"referral-users"},[a("h2",{staticClass:"section-title"},[t._v("我的邀请用户")]),a("div",{staticClass:"users-table-container"},[a("a-table",{attrs:{columns:t.userColumns,"data-source":t.referralUsers,loading:t.usersLoading,pagination:t.usersPagination,size:"middle"},on:{change:t.handleUsersTableChange},scopedSlots:t._u([{key:"avatar",fn:function(e,i){return[a("a-avatar",{style:{backgroundColor:"#87d068"},attrs:{src:t.getAvatarUrl(i.avatar)}},[t._v("\n                  "+t._s(i.nickname?i.nickname.charAt(0):"U")+"\n                ")])]}},{key:"reward",fn:function(e){return[a("span",{staticClass:"reward-amount"},[t._v("¥"+t._s(e||"0.00"))])]}}],null,!1,3010831496)})],1)]),a("div",{staticClass:"withdraw-records"},[a("h2",{staticClass:"section-title"},[t._v("提现记录")]),a("div",{staticClass:"filter-section",staticStyle:{"margin-bottom":"16px",padding:"16px",background:"#fafafa","border-radius":"6px"}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"提现金额"}},[a("a-input-group",{attrs:{compact:""}},[a("a-input-number",{staticStyle:{width:"50%"},attrs:{placeholder:"最小金额",min:0,precision:2},model:{value:t.withdrawFilter.minAmount,callback:function(e){t.$set(t.withdrawFilter,"minAmount",e)},expression:"withdrawFilter.minAmount"}}),a("a-input-number",{staticStyle:{width:"50%"},attrs:{placeholder:"最大金额",min:0,precision:2},model:{value:t.withdrawFilter.maxAmount,callback:function(e){t.$set(t.withdrawFilter,"maxAmount",e)},expression:"withdrawFilter.maxAmount"}})],1)],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"申请时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始日期', '结束日期']"},model:{value:t.withdrawFilter.dateRange,callback:function(e){t.$set(t.withdrawFilter,"dateRange",e)},expression:"withdrawFilter.dateRange"}})],1)],1),a("a-col",{attrs:{span:4}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择状态"},model:{value:t.withdrawFilter.status,callback:function(e){t.$set(t.withdrawFilter,"status",e)},expression:"withdrawFilter.status"}},[a("a-select-option",{attrs:{value:null}},[t._v("全部")]),a("a-select-option",{attrs:{value:1}},[t._v("待审核")]),a("a-select-option",{attrs:{value:2}},[t._v("已发放")]),a("a-select-option",{attrs:{value:3}},[t._v("审核拒绝")]),a("a-select-option",{attrs:{value:4}},[t._v("已取消")])],1)],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:"完成时间"}},[a("a-range-picker",{staticStyle:{width:"100%"},attrs:{format:"YYYY-MM-DD",placeholder:"['开始日期', '结束日期']"},model:{value:t.withdrawFilter.completeDateRange,callback:function(e){t.$set(t.withdrawFilter,"completeDateRange",e)},expression:"withdrawFilter.completeDateRange"}})],1)],1),a("a-col",{attrs:{span:5}},[a("a-form-item",{attrs:{label:" "}},[a("a-button",{staticStyle:{"margin-right":"8px"},attrs:{type:"primary",loading:t.recordsLoading},on:{click:t.handleWithdrawFilter}},[t._v("\n                    搜索\n                  ")]),a("a-button",{on:{click:t.handleWithdrawReset}},[t._v("重置")])],1)],1)],1)],1),a("div",{staticClass:"records-table-container"},[a("a-table",{attrs:{columns:t.withdrawColumns,"data-source":t.withdrawRecords,loading:t.recordsLoading,pagination:t.withdrawPagination,size:"middle"},on:{change:t.handleWithdrawTableChange},scopedSlots:t._u([{key:"status",fn:function(e){return[a("a-tag",{attrs:{color:t.getStatusColor(e)}},[t._v("\n                  "+t._s(e)+"\n                ")])]}},{key:"amount",fn:function(e){return[a("span",{staticClass:"withdraw-amount"},[t._v("¥"+t._s(e))])]}},{key:"action",fn:function(e,i){return[1===i.rawStatus?a("a-button",{attrs:{type:"danger",size:"small",loading:t.cancelLoading},on:{click:function(e){return t.handleCancelWithdraw(i)}}},[t._v("\n                  取消提现\n                ")]):a("span",[t._v("-")])]}}],null,!1,2565111685)})],1)])])]),a("a-modal",{attrs:{title:"邀请二维码",footer:null,width:"400px",centered:""},model:{value:t.showQRModal,callback:function(e){t.showQRModal=e},expression:"showQRModal"}},[a("div",{staticClass:"qr-modal-content"},[t.qrCodeUrl?a("div",{staticClass:"qr-code-container"},[a("img",{staticClass:"qr-code-image",attrs:{src:t.qrCodeUrl,alt:"邀请二维码"}})]):t._e(),a("div",{staticClass:"qr-actions"},[t.qrCodeUrl?a("a-button",{attrs:{type:"primary",block:""},on:{click:t.downloadQRCode}},[a("a-icon",{attrs:{type:"download"}}),t._v("\n            下载二维码\n          ")],1):t._e()],1)])]),a("a-modal",{attrs:{title:"申请提现",footer:null,width:"500px",centered:""},model:{value:t.showWithdrawModal,callback:function(e){t.showWithdrawModal=e},expression:"showWithdrawModal"}},[a("div",{staticClass:"withdraw-modal-content"},[a("div",{staticClass:"withdraw-info"},[a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("可提现金额：")]),a("span",{staticClass:"info-value"},[t._v("¥"+t._s(t.formatNumber(t.availableEarnings)))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"info-label"},[t._v("最低提现金额：")]),a("span",{staticClass:"info-value"},[t._v("¥50.00")])])]),a("a-form",{attrs:{form:t.withdrawForm},on:{submit:t.handleWithdraw}},[a("a-form-item",{attrs:{label:"提现金额"}},[a("a-input-number",{directives:[{name:"decorator",rawName:"v-decorator",value:["amount",{rules:[{required:!0,message:"请输入提现金额"},{type:"number",min:50,message:"最低提现金额为50元"},{type:"number",max:t.availableEarnings,message:"提现金额不能超过可提现金额"}]}],expression:"['amount', {\n                rules: [\n                  { required: true, message: '请输入提现金额' },\n                  { type: 'number', min: 50, message: '最低提现金额为50元' },\n                  { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }\n                ]\n              }]"}],staticStyle:{width:"100%"},attrs:{min:50,max:t.availableEarnings,precision:2,placeholder:"请输入提现金额"}},[a("template",{slot:"addonAfter"},[t._v("元")])],2)],1),a("a-form-item",{attrs:{label:"提现方式"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["method",{rules:[{required:!0,message:"请选择提现方式"}],initialValue:"alipay"}],expression:"['method', {\n                rules: [{ required: true, message: '请选择提现方式' }],\n                initialValue: 'alipay'\n              }]"}],attrs:{placeholder:"请选择提现方式",disabled:""}},[a("a-select-option",{attrs:{value:"alipay"}},[t._v("支付宝")])],1)],1),a("a-form-item",{attrs:{label:"支付宝手机号"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["alipayAccount",{rules:[{required:!0,message:"请输入支付宝手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}]}],expression:"['alipayAccount', {\n                rules: [\n                  { required: true, message: '请输入支付宝手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号格式' }\n                ]\n              }]"}],attrs:{placeholder:"请输入支付宝手机号"}})],1),a("a-form-item",{attrs:{label:"收款人真实姓名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["realName",{rules:[{required:!0,message:"请输入收款人真实姓名"},{pattern:/^[\u4e00-\u9fa5]{2,4}$/,message:"请输入正确的中文姓名（2-4个汉字）"}]}],expression:"['realName', {\n                rules: [\n                  { required: true, message: '请输入收款人真实姓名' },\n                  { pattern: /^[\\u4e00-\\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }\n                ]\n              }]"}],attrs:{placeholder:"请输入收款人真实姓名"}})],1)],1),a("div",{staticClass:"withdraw-actions"},[a("a-button",{staticStyle:{"margin-right":"8px"},on:{click:function(e){t.showWithdrawModal=!1}}},[t._v("\n            取消\n          ")]),a("a-button",{attrs:{type:"primary",loading:t.withdrawLoading},on:{click:t.handleWithdraw}},[t._v("\n            申请提现\n          ")])],1)],1)])],1)])],1)])},r=[],s=a("a34a"),n=a.n(s),o=a("df7c"),c=a("77ea"),l=a("9fb0"),d=a("2b0e");function u(t,e,a,i,r,s,n){try{var o=t[s](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,r)}function h(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var s=t.apply(e,a);function n(t){u(s,i,r,n,o,"next",t)}function o(t){u(s,i,r,n,o,"throw",t)}n(void 0)}))}}var p={name:"Affiliate",components:{WebsitePage:o["default"]},data:function(){return{loading:!0,qrLoading:!1,totalEarnings:0,availableEarnings:0,totalReferrals:0,memberReferrals:0,affiliateLink:"",userRole:"user",currentCommissionRate:30,commissionLevelText:"新手邀请员",levelProgress:0,nextLevelRequirement:10,nextLevelText:"高级邀请员",nextLevelRate:40,progressColor:"#1890ff",commissionLevels:[],allLevelConfigs:[],showQRModal:!1,qrCodeUrl:"",qrPreGenerated:!1,showWithdrawModal:!1,withdrawLoading:!1,withdrawForm:this.$form.createForm(this),referralUsers:[],usersLoading:!1,usersPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")}},usersSort:{orderBy:"total_reward",order:"desc"},defaultAvatar:"/default-avatar.png",withdrawRecords:[],recordsLoading:!1,cancelLoading:!1,withdrawPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"第 ".concat(e[0],"-").concat(e[1]," 条，共 ").concat(t," 条")}},withdrawSort:{orderBy:"apply_time",order:"desc"},withdrawFilter:{minAmount:null,maxAmount:null,status:null,dateRange:[],completeDateRange:[]},userInfo:null}},computed:{userColumns:function(){return[{title:"头像",dataIndex:"avatar",key:"avatar",scopedSlots:{customRender:"avatar"},width:80},{title:"用户昵称",dataIndex:"nickname",key:"nickname",sorter:!0},{title:"注册时间",dataIndex:"registerTime",key:"registerTime",sorter:!0},{title:"获得奖励",dataIndex:"reward",key:"reward",scopedSlots:{customRender:"reward"},sorter:!0}]},withdrawColumns:function(){return[{title:"提现金额",dataIndex:"amount",key:"amount",scopedSlots:{customRender:"amount"},sorter:!0},{title:"提现方式",dataIndex:"method",key:"method"},{title:"申请时间",dataIndex:"applyTime",key:"applyTime",sorter:!0},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"},sorter:!0},{title:"完成时间",dataIndex:"completeTime",key:"completeTime",sorter:!0},{title:"操作",key:"action",width:100,scopedSlots:{customRender:"action"}}]}},mounted:function(){var t=h(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.checkLoginAndLoadData();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{getAvatarUrl:function(t){return t?t.startsWith("http://")||t.startsWith("https://")?t:this.getFileAccessHttpUrl(t)||this.defaultAvatar:this.defaultAvatar},getFileAccessHttpUrl:function(t){if(!t)return this.defaultAvatar;if(t.startsWith("http://")||t.startsWith("https://"))return t;if(t.startsWith("uploads/"))return window.getFileAccessHttpUrl?window.getFileAccessHttpUrl(t):t;var e=this.$store.state.app.staticDomainURL;return e?"".concat(e,"/").concat(t):t},loadDefaultAvatar:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/sys/common/default-avatar-url");case 3:e=t.sent,e&&e.success&&e.result&&(this.defaultAvatar=e.result),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),checkLoginAndLoadData:function(){var t=h(n.a.mark((function t(){var e,a,i,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=d["default"].ls.get(l["a"]),e){t.next=5;break}return this.loading=!1,this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),t.abrupt("return");case 5:return t.prev=5,this.loading=!0,a=Date.now(),t.next=11,Promise.all([this.loadReferralData(),this.loadReferralLink(),this.loadUserRole(),this.loadLevelConfig(),this.loadReferralUsers(),this.loadWithdrawRecords(),this.loadDefaultAvatar()]);case 11:if(this.calculateCommissionLevel(),this.preGenerateQRCode(),i=Date.now()-a,r=1e3,!(i<r)){t.next=18;break}return t.next=18,new Promise((function(t){return setTimeout(t,r-i)}));case 18:t.next=25;break;case 21:t.prev=21,t.t0=t["catch"](5),this.$notification.error({message:"加载失败",description:"获取分销数据失败，请稍后重试",placement:"topRight"});case 25:return t.prev=25,this.loading=!1,t.finish(25);case 29:case"end":return t.stop()}}),t,this,[[5,21,25,29]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralData:function(){var t=h(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["o"])();case 3:e=t.sent,e.success&&(a=e.result,this.totalEarnings=a.total_reward_amount||0,this.availableEarnings=a.available_rewards||0,this.totalReferrals=a.total_referrals||0,this.memberReferrals=a.member_referrals||0),t.next=11;break;case 7:throw t.prev=7,t.t0=t["catch"](0),t.t0;case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadReferralLink:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["g"])({});case 3:e=t.sent,e.success&&(this.affiliateLink=e.result||""),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.affiliateLink="".concat(window.location.origin,"?ref=loading...");case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadUserRole:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["w"])();case 3:e=t.sent,e.success&&(this.userRole=e.result.role_code||"user"),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.userRole="user";case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadLevelConfig:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(c["k"])();case 3:e=t.sent,e.success&&(this.allLevelConfigs=e.result||[]),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.allLevelConfigs=[];case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),calculateCommissionLevel:function(){var t=this,e=this.memberReferrals,a=this.allLevelConfigs.filter((function(e){return e.role_code===t.userRole}));if(0!==a.length){for(var i=null,r=a.length-1;r>=0;r--)if(e>=a[r].min_referrals){i=a[r];break}i||(i=a[0]),this.currentCommissionRate=parseFloat(i.commission_rate),this.commissionLevelText=i.level_name;var s=a.find((function(t){return t.min_referrals>e}));s?(this.nextLevelRequirement=s.min_referrals,this.nextLevelText=s.level_name,this.nextLevelRate=parseFloat(s.commission_rate),this.levelProgress=e/s.min_referrals*100,this.progressColor="#1890ff"):(this.nextLevelRequirement=0,this.nextLevelText="已达最高等级",this.nextLevelRate=this.currentCommissionRate,this.levelProgress=100,this.progressColor="#722ed1"),this.commissionLevels=a.map((function(t,i){var r=e>=t.min_referrals,s=!1;if(!r)if(0===i)s=!0;else{var n=a[i-1].min_referrals;s=e>=n}var o=!r&&!s,c=0;return r||(c=t.min_referrals-e),{name:t.level_name,rate:parseFloat(t.commission_rate),requirement:t.min_referrals,isCompleted:r,isCurrent:s,isUpcoming:o,remaining:c>0?c:0}}))}},copyLink:function(){var t=this;this.affiliateLink?navigator.clipboard.writeText(this.affiliateLink).then((function(){t.$notification.success({message:"邀请链接已复制",description:"邀请链接已成功复制到剪贴板，快去分享给好友吧！",placement:"topRight",duration:3,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}})})).catch((function(){t.$notification.error({message:"复制失败",description:"复制邀请链接失败，请手动复制",placement:"topRight"})})):this.$notification.warning({message:"邀请链接未生成",description:"邀请链接正在生成中，请稍后再试",placement:"topRight"})},preGenerateQRCode:function(){var t=h(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.affiliateLink&&!this.qrPreGenerated){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=6,this.$http.post("/api/usercenter/generateReferralQRCode",null,{params:{url:this.affiliateLink}});case 6:e=t.sent,e&&e.success&&(this.qrCodeUrl=e.result,this.qrPreGenerated=!0),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2);case 13:case"end":return t.stop()}}),t,this,[[2,10]])})));function e(){return t.apply(this,arguments)}return e}(),generateQRCode:function(){var t=h(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.affiliateLink){t.next=3;break}return this.$notification.warning({message:"邀请链接未生成",description:"请等待邀请链接生成完成后再生成二维码",placement:"topRight"}),t.abrupt("return");case 3:if(!this.qrPreGenerated||!this.qrCodeUrl){t.next=6;break}return this.showQRModal=!0,t.abrupt("return");case 6:return t.prev=6,this.qrLoading=!0,t.next=10,this.$http.post("/api/usercenter/generateReferralQRCode",null,{params:{url:this.affiliateLink}});case 10:if(e=t.sent,!e||!e.success){t.next=19;break}this.qrCodeUrl=e.result,this.qrPreGenerated=!0,this.showQRModal=!0,this.$notification.success({message:"二维码生成成功",description:"邀请二维码已生成并存储到CDN，可以下载保存",placement:"topRight"}),t.next=21;break;case 19:throw a=e&&e.message||"生成失败",new Error(a);case 21:t.next=27;break;case 23:t.prev=23,t.t0=t["catch"](6),this.$notification.error({message:"生成失败",description:t.t0.message||"二维码生成失败，请稍后重试",placement:"topRight"});case 27:return t.prev=27,this.qrLoading=!1,t.finish(27);case 30:case"end":return t.stop()}}),t,this,[[6,23,27,30]])})));function e(){return t.apply(this,arguments)}return e}(),downloadQRCode:function(){if(this.qrCodeUrl)try{var t=this.extractReferralCode(this.affiliateLink),e="https://www.aigcview.cn/jeecg-boot",a="".concat(e,"/api/usercenter/downloadReferralQRCode?url=").concat(encodeURIComponent(this.qrCodeUrl),"&code=").concat(t,"&t=").concat(Date.now()),i=document.createElement("iframe");i.style.display="none",i.style.position="absolute",i.style.left="-9999px",i.src=a,document.body.appendChild(i),setTimeout((function(){i.parentNode&&document.body.removeChild(i)}),3e3),this.$notification.success({message:"下载开始",description:"邀请二维码_".concat(t,".png 正在下载"),placement:"topRight"})}catch(r){this.$notification.error({message:"下载失败",description:"二维码下载失败，请稍后重试",placement:"topRight"})}},extractReferralCode:function(t){if(!t)return"UNKNOWN";try{var e=new URL(t),a=e.searchParams.get("ref");return a||"UNKNOWN"}catch(i){return"UNKNOWN"}},openWithdrawModal:function(){this.availableEarnings<50?this.$notification.warning({message:"提现金额不足",description:"最低提现金额为50元，请继续邀请获得更多收益",placement:"topRight"}):this.showWithdrawModal=!0},handleWithdraw:function(){var t=h(n.a.mark((function t(){var e=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.withdrawForm.validateFields(function(){var t=h(n.a.mark((function t(a,i){var r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=2;break}return t.abrupt("return");case 2:r=e.$createElement,e.$confirm({title:"确认提现申请",content:r("div",{style:{margin:"16px 0"}},[r("p",{style:{marginBottom:"8px"}},[r("strong","提现金额："),"¥".concat(i.amount)]),r("p",{style:{marginBottom:"8px"}},[r("strong","支付宝账号："),i.alipayAccount]),r("p",{style:{marginBottom:"8px"}},[r("strong","收款人姓名："),i.realName]),r("p",{style:{color:"#ff4d4f",marginTop:"12px",marginBottom:"0"}},[r("strong","注意："),"请再次核实一遍提现信息，请确认信息无误！"])]),okText:"确认提现",cancelText:"取消",centered:!0,width:400,onOk:function(){var t=h(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.submitWithdrawRequest(i);case 2:case"end":return t.stop()}}),t)})));function a(){return t.apply(this,arguments)}return a}()});case 4:case"end":return t.stop()}}),t)})));return function(e,a){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),submitWithdrawRequest:function(){var t=h(n.a.mark((function t(e){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.withdrawLoading=!0,t.prev=1,a={withdrawalAmount:e.amount,realName:e.realName,alipayAccount:e.alipayAccount},t.next=5,this.$http.post("/api/usercenter/applyWithdrawal",a);case 5:if(i=t.sent,!i.success){t.next=15;break}return this.withdrawLoading=!1,this.showWithdrawModal=!1,this.withdrawForm.resetFields(),this.$notification.success({message:"提现申请成功",description:"您的提现申请已提交，预计1-3个工作日到账",placement:"topRight"}),t.next=13,Promise.all([this.loadReferralData(),this.loadWithdrawRecords()]);case 13:t.next=17;break;case 15:this.withdrawLoading=!1,this.$notification.error({message:"提现申请失败",description:i.message||"申请失败，请重试",placement:"topRight"});case 17:t.next=24;break;case 19:t.prev=19,t.t0=t["catch"](1),this.withdrawLoading=!1,t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$notification.error({message:"提现申请失败",description:t.t0.response.data.message,placement:"topRight"}):t.t0.message?this.$notification.error({message:"提现申请失败",description:t.t0.message,placement:"topRight"}):this.$notification.error({message:"提现申请失败",description:"网络错误，请稍后重试",placement:"topRight"});case 24:case"end":return t.stop()}}),t,this,[[1,19]])})));function e(e){return t.apply(this,arguments)}return e}(),loadReferralUsers:function(){var t=h(n.a.mark((function t(){var e,a,i,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.usersLoading=!0,e={current:this.usersPagination&&this.usersPagination.current||1,size:this.usersPagination&&this.usersPagination.pageSize||10,orderBy:this.usersSort&&this.usersSort.orderBy||"total_reward",order:this.usersSort&&this.usersSort.order||"desc"},t.next=6,this.$http.get("/api/usercenter/referralList",{params:e});case 6:a=t.sent,a&&a.success?(i=a.result||{},r=i.records||[],this.usersPagination&&(this.usersPagination.total=i.total||0),this.referralUsers=r.map((function(t,e){return{key:t.id||e,nickname:t.referee_nickname||"用户***".concat(e+1),avatar:t.referee_avatar||"",registerTime:t.register_time||"",reward:t.total_reward||"0.00"}}))):(this.referralUsers=[],this.usersPagination&&(this.usersPagination.total=0)),t.next=15;break;case 10:t.prev=10,t.t0=t["catch"](0),this.referralUsers=[],t.t0.response&&401===t.t0.response.status&&this.$message.warning("登录已过期，请重新登录");case 15:return t.prev=15,this.usersLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[0,10,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),loadWithdrawRecords:function(){var t=h(n.a.mark((function t(){var e,a,i,r,s=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.recordsLoading=!0,e={current:this.withdrawPagination&&this.withdrawPagination.current||1,size:this.withdrawPagination&&this.withdrawPagination.pageSize||10,orderBy:this.withdrawSort&&this.withdrawSort.orderBy||"apply_time",order:this.withdrawSort&&this.withdrawSort.order||"desc"},this.withdrawFilter&&(null!==this.withdrawFilter.minAmount&&""!==this.withdrawFilter.minAmount&&(e.minAmount=this.withdrawFilter.minAmount),null!==this.withdrawFilter.maxAmount&&""!==this.withdrawFilter.maxAmount&&(e.maxAmount=this.withdrawFilter.maxAmount),null!==this.withdrawFilter.status&&(e.status=this.withdrawFilter.status),this.withdrawFilter.dateRange&&2===this.withdrawFilter.dateRange.length&&(e.startDate=this.withdrawFilter.dateRange[0].format?this.withdrawFilter.dateRange[0].format("YYYY-MM-DD"):this.withdrawFilter.dateRange[0],e.endDate=this.withdrawFilter.dateRange[1].format?this.withdrawFilter.dateRange[1].format("YYYY-MM-DD"):this.withdrawFilter.dateRange[1]),this.withdrawFilter.completeDateRange&&2===this.withdrawFilter.completeDateRange.length&&(e.completeStartDate=this.withdrawFilter.completeDateRange[0].format?this.withdrawFilter.completeDateRange[0].format("YYYY-MM-DD"):this.withdrawFilter.completeDateRange[0],e.completeEndDate=this.withdrawFilter.completeDateRange[1].format?this.withdrawFilter.completeDateRange[1].format("YYYY-MM-DD"):this.withdrawFilter.completeDateRange[1])),t.next=8,this.$http.get("/api/usercenter/withdrawalHistory",{params:e});case 8:a=t.sent,a&&a.success?(i=a.result||{},r=i.records||[],this.withdrawPagination&&(this.withdrawPagination.total=i.total||0),this.withdrawRecords=r.map((function(t,e){return{key:t.id||e,id:t.id,amount:t.withdrawal_amount||"0.00",method:t.withdrawalMethod||"支付宝",applyTime:t.apply_time||"",status:s.getWithdrawStatusText(t.status,t.review_remark),rawStatus:t.status,completeTime:t.review_time||"-"}}))):(this.withdrawRecords=[],this.withdrawPagination&&(this.withdrawPagination.total=0)),t.next=17;break;case 12:t.prev=12,t.t0=t["catch"](0),this.withdrawRecords=[],t.t0.response&&401===t.t0.response.status&&this.$message.warning("登录已过期，请重新登录");case 17:return t.prev=17,this.recordsLoading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[0,12,17,20]])})));function e(){return t.apply(this,arguments)}return e}(),getWithdrawStatusText:function(t,e){var a={0:"待审核",1:"待审核",2:"已完成",3:"已拒绝",4:"已取消"},i=a[t]||"未知状态";return 3===t&&e&&(i+="（".concat(e,"）")),i},getStatusColor:function(t){if(t.includes("已拒绝"))return"red";var e={"已完成":"green","处理中":"blue","待审核":"orange","已取消":"gray"};return e[t]||"default"},getSortOrder:function(t){return this.usersSort&&this.usersSort.orderBy===t?"asc"===this.usersSort.order?"ascend":"descend":null},handleUsersTableChange:function(t,e,a){if(this.usersPagination&&t&&(this.usersPagination.current=t.current||1,this.usersPagination.pageSize=t.pageSize||10),a&&a.field&&this.usersSort){var i={nickname:"nickname",registerTime:"register_time",reward:"total_reward"},r=i[a.field]||"total_reward";this.usersSort.orderBy===r?this.usersSort.order="asc"===this.usersSort.order?"desc":"asc":(this.usersSort.orderBy=r,this.usersSort.order="total_reward"===r?"desc":"asc"),this.usersPagination&&(this.usersPagination.current=1)}this.loadReferralUsers()},handleWithdrawTableChange:function(t,e,a){if(this.withdrawPagination&&t&&(this.withdrawPagination.current=t.current||1,this.withdrawPagination.pageSize=t.pageSize||10),a&&a.field&&this.withdrawSort){var i={amount:"withdrawal_amount",applyTime:"apply_time",status:"status",completeTime:"review_time"},r=i[a.field]||"apply_time";this.withdrawSort.orderBy===r?this.withdrawSort.order="asc"===this.withdrawSort.order?"desc":"asc":(this.withdrawSort.orderBy=r,this.withdrawSort.order="apply_time"===r||"withdrawal_amount"===r?"desc":"asc"),this.withdrawPagination&&(this.withdrawPagination.current=1)}this.loadWithdrawRecords()},handleWithdrawFilter:function(){this.withdrawPagination&&(this.withdrawPagination.current=1),this.loadWithdrawRecords()},handleWithdrawReset:function(){this.withdrawFilter={minAmount:null,maxAmount:null,status:null,dateRange:[],completeDateRange:[]},this.withdrawPagination&&(this.withdrawPagination.current=1),this.loadWithdrawRecords()},handleCancelWithdraw:function(t){var e=this,a=this.$createElement;this.$confirm({title:"确认取消提现",content:a("div",{style:{margin:"16px 0"}},[a("p",{style:{marginBottom:"8px"}},[a("strong","提现金额："),"¥".concat(t.amount)]),a("p",{style:{marginBottom:"8px"}},[a("strong","申请时间："),t.applyTime]),a("p",{style:{color:"#ff4d4f",marginTop:"12px",marginBottom:"0"}},[a("strong","注意："),"取消后金额将返还到可提现余额，此操作不可撤销！"])]),okText:"确认取消",okType:"danger",cancelText:"返回",centered:!0,width:400,onOk:function(){var a=h(n.a.mark((function a(){return n.a.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.confirmCancelWithdraw(t);case 2:case"end":return a.stop()}}),a)})));function i(){return a.apply(this,arguments)}return i}()})},confirmCancelWithdraw:function(){var t=h(n.a.mark((function t(e){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.cancelLoading=!0,t.prev=1,a={withdrawalId:e.id},t.next=5,this.$http.post("/api/usercenter/cancelWithdrawal",a);case 5:if(i=t.sent,!i.success){t.next=12;break}return this.$notification.success({message:"取消成功",description:"提现申请已取消，金额已返还到可提现余额",placement:"topRight"}),t.next=10,Promise.all([this.loadReferralData(),this.loadWithdrawRecords()]);case 10:t.next=13;break;case 12:this.$notification.error({message:"取消失败",description:i.message||"取消提现失败，请重试",placement:"topRight"});case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](1),t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$notification.error({message:"取消失败",description:t.t0.response.data.message,placement:"topRight"}):t.t0.message?this.$notification.error({message:"取消失败",description:t.t0.message,placement:"topRight"}):this.$notification.error({message:"取消失败",description:"网络错误，请稍后重试",placement:"topRight"});case 19:return t.prev=19,this.cancelLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[1,15,19,22]])})));function e(e){return t.apply(this,arguments)}return e}(),formatNumber:function(t){if(null===t||void 0===t)return"0";var e=parseFloat(t);return isNaN(e)?"0":t===this.totalEarnings?e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}):e.toLocaleString("zh-CN")},getRoleDisplayName:function(t){switch(t){case"VIP":return"VIP用户";case"SVIP":return"SVIP用户";case"user":default:return"普通用户"}},getRequirementText:function(t){var e=t.min_referrals,a=t.role_code,i=this.allLevelConfigs.filter((function(t){return t.role_code===a})),r=i.findIndex((function(e){return e.id===t.id})),s=i[r+1];return"SVIP"===a?"无要求":s?0===e?"0-".concat(s.min_referrals-1,"人"):"".concat(e,"-").concat(s.min_referrals-1,"人"):"".concat(e,"人以上")}}},m=p,v=(a("6491"),a("2877")),g=Object(v["a"])(m,i,r,!1,null,"794dfa0a",null);e["default"]=g.exports},6491:function(t,e,a){"use strict";var i=a("a2b0"),r=a.n(i);r.a},"7be7":function(t,e,a){"use strict";var i=a("0088"),r=a.n(i);r.a},"9d91":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"agent-detail-page"},[a("div",{staticClass:"timeline-nav"},[a("div",{staticClass:"timeline-line"}),t._l(t.navItems,(function(e,i){return a("div",{key:e.id,staticClass:"timeline-node",class:{active:t.activeNavItem===e.id},style:{top:120*i+100+"px"},on:{click:function(a){return t.scrollToSection(e.id)}}},[a("div",{staticClass:"node-dot"}),a("div",{staticClass:"node-label"},[t._v(t._s(e.title))])])}))],2),t.isCheckingPermission?a("div",{staticClass:"permission-loading"},[a("a-spin",{attrs:{size:"large"}},[a("div",{staticClass:"loading-text"},[t._v("正在验证访问权限...")])])],1):t.isLoggedIn?t.isPurchased?a("div",{staticClass:"main-content"},[a("div",{staticClass:"page-header"},[a("a-button",{staticClass:"back-button",on:{click:t.goBack}},[a("a-icon",{attrs:{type:"arrow-left"}}),t._v("\n        返回\n      ")],1),a("div",{staticClass:"breadcrumb"},[a("a-breadcrumb",[a("a-breadcrumb-item",[a("router-link",{attrs:{to:"/workflow-center"}},[t._v("工作流中心")])],1),a("a-breadcrumb-item",[t._v("智能体详情")])],1)],1)],1),a("div",{staticClass:"agent-info-section",attrs:{id:"agent-info"}},[a("div",{staticClass:"agent-basic-info"},[a("div",{staticClass:"agent-avatar"},[a("img",{attrs:{src:t.agentDetail.agentAvatar||t.defaultAvatar,alt:t.agentDetail.agentName},on:{error:t.handleAvatarError}})]),a("div",{staticClass:"agent-details"},[a("h1",{staticClass:"agent-name"},[t._v(t._s(t.agentDetail.agentName||"智能体名称"))]),a("div",{staticClass:"agent-description"},[a("p",[t._v(t._s(t.agentDetail.agentDescription||"暂无描述"))])]),a("div",{staticClass:"creator-info"},[a("img",{staticClass:"creator-avatar",attrs:{src:t.agentDetail.creatorInfo&&t.agentDetail.creatorInfo.avatar||t.agentDetail.creatorAvatar||t.defaultCreatorAvatar,alt:t.agentDetail.creatorInfo&&t.agentDetail.creatorInfo.nickname||t.agentDetail.creatorNickname||"创作者"},on:{error:t.handleCreatorAvatarError}}),a("span",{staticClass:"creator-name"},[t._v(t._s(t.agentDetail.creatorInfo&&t.agentDetail.creatorInfo.nickname||t.agentDetail.creatorNickname||"创作者"))]),a("span",{staticClass:"author-type-badge",class:t.authorTypeClass},[t._v("\n              "+t._s(t.authorTypeText)+"\n            ")])])])]),a("div",{staticClass:"reading-tip",on:{click:t.scrollToUsageGuide}},[a("a-icon",{staticClass:"tip-icon",attrs:{type:"info-circle"}}),a("span",{staticClass:"tip-text"},[t._v("\n          请往下滑或点此快速下滑，仔细阅读工作流导入使用说明\n        ")]),a("a-icon",{staticClass:"arrow-icon",attrs:{type:"arrow-down"}})],1)]),t.agentDetail.demoVideo?a("div",{staticClass:"video-section",attrs:{id:"demo-video"}},[a("div",{staticClass:"video-header",on:{click:t.toggleVideoCollapse}},[a("h3",[t._v("演示视频")]),a("a-icon",{staticClass:"collapse-icon",attrs:{type:t.videoCollapsed?"down":"up"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.videoCollapsed,expression:"!videoCollapsed"}],staticClass:"video-container"},[a("video",{ref:"videoPlayer",attrs:{src:t.agentDetail.demoVideo,controls:"",autoplay:"",muted:"",preload:"metadata"},domProps:{muted:!0},on:{loadstart:t.onVideoLoadStart,loadeddata:t.onVideoLoaded,error:t.onVideoError,play:t.onVideoPlay,pause:t.onVideoPause}},[t._v("\n          您的浏览器不支持视频播放\n        ")]),t.videoError?a("div",{staticClass:"video-error"},[a("a-icon",{attrs:{type:"exclamation-circle"}}),a("span",[t._v("视频加载失败，请稍后重试")])],1):t._e()])]):t._e(),a("div",{staticClass:"usage-guide-section",attrs:{id:"usage-guide"}},[a("div",{staticClass:"guide-header",on:{click:t.toggleGuideCollapse}},[a("h3",[t._v("工作流导入使用说明")]),a("a-icon",{staticClass:"collapse-icon",attrs:{type:t.guideCollapsed?"down":"up"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.guideCollapsed,expression:"!guideCollapsed"}],staticClass:"guide-content"},[a("div",{staticClass:"guide-step"},[a("div",{staticClass:"step-number"},[t._v("1")]),a("div",{staticClass:"step-content"},[a("p",{staticClass:"step-text"},[t._v("点击下载工作流，下载完成是一个压缩包。")]),a("div",{staticClass:"step-image",on:{click:function(e){return t.previewImage("https://cdn.aigcview.cn/defaults/export.png")}}},[a("img",{attrs:{src:"https://cdn.aigcview.cn/defaults/export.png",alt:"下载工作流示例图"},on:{error:t.handleGuideImageError}}),a("div",{staticClass:"image-overlay"},[a("a-icon",{attrs:{type:"zoom-in"}}),a("span",[t._v("点击查看大图")])],1)])])]),a("div",{staticClass:"guide-step"},[a("div",{staticClass:"step-number"},[t._v("2")]),a("div",{staticClass:"step-content"},[a("p",{staticClass:"step-text"},[a("a",{staticClass:"coze-link",attrs:{href:"https://www.coze.cn/space",target:"_blank"}},[t._v("\n                点此快速跳转Coze工作空间\n                "),a("a-icon",{attrs:{type:"external-link"}})],1),t._v("\n              ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。"),a("span",{staticClass:"highlight-text"},[t._v("（压缩包无需解压）")])]),a("div",{staticClass:"step-image",on:{click:function(e){return t.previewImage("https://cdn.aigcview.cn/defaults/import.png")}}},[a("img",{attrs:{src:"https://cdn.aigcview.cn/defaults/import.png",alt:"导入工作流示例图"},on:{error:t.handleGuideImageError}}),a("div",{staticClass:"image-overlay"},[a("a-icon",{attrs:{type:"zoom-in"}}),a("span",[t._v("点击查看大图")])],1)])])])])]),a("div",{staticClass:"workflow-section",attrs:{id:"workflow-list"}},[a("h3",[t._v("关联工作流 ("+t._s(t.workflowList.length)+"个)")]),t.workflowLoading?a("div",{staticClass:"workflow-loading"},[a("a-spin",[t._v("加载工作流列表...")])],1):0===t.workflowList.length?a("div",{staticClass:"workflow-empty"},[a("a-empty",{attrs:{description:"暂无关联工作流"}})],1):a("div",{staticClass:"workflow-list"},t._l(t.workflowList,(function(e){return a("div",{key:e.id,staticClass:"workflow-card"},[a("div",{staticClass:"workflow-avatar"},[a("img",{staticClass:"workflow-image",attrs:{src:t.agentDetail.agentAvatar||t.defaultAgentAvatar,alt:e.workflowName},on:{error:t.handleWorkflowImageError}})]),a("div",{staticClass:"workflow-info"},[a("h4",{staticClass:"workflow-name"},[t._v(t._s(e.workflowName))]),a("p",{staticClass:"workflow-description"},[t._v(t._s(e.workflowDescription||"暂无描述"))]),a("div",{staticClass:"workflow-params"},[a("div",{staticClass:"params-label"},[a("a-icon",{attrs:{type:"setting"}}),a("span",[t._v("输入参数说明")])],1),a("div",{staticClass:"params-content"},[t._v("\n                "+t._s(e.inputParamsDesc||"暂无输入参数说明")+"\n              ")])])]),a("div",{staticClass:"workflow-actions"},[a("a-button",{staticClass:"download-btn",attrs:{type:"primary",size:"default",loading:e.downloading,disabled:!e.workflowPackage},on:{click:function(a){return t.handleWorkflowDownload(e)}}},[a("a-icon",{attrs:{type:"cloud-download"}}),a("span",[t._v("下载工作流")])],1)],1)])})),0),t.workflowList.length>0?a("div",{staticClass:"workflow-usage-tip"},[a("a-icon",{staticClass:"tip-icon",attrs:{type:"info-circle"}}),t._m(0)],1):t._e()])]):a("div",{staticClass:"permission-denied"},[a("div",{staticClass:"permission-content"},[a("a-icon",{staticClass:"permission-icon",attrs:{type:"shopping-cart"}}),a("h2",[t._v("需要购买访问")]),a("p",[t._v("您还未购买此智能体，请先购买后再查看详细信息")]),a("div",{staticClass:"action-buttons"},[a("a-button",{attrs:{size:"large"},on:{click:t.goBack}},[t._v("\n          返回列表\n        ")]),a("a-button",{attrs:{type:"primary",size:"large"},on:{click:t.goToPurchase}},[t._v("\n          立即购买\n        ")])],1)],1)]):a("div",{staticClass:"permission-denied"},[a("div",{staticClass:"permission-content"},[a("a-icon",{staticClass:"permission-icon",attrs:{type:"lock"}}),a("h2",[t._v("需要登录访问")]),a("p",[t._v("请先登录您的账户以查看智能体详细信息")]),a("a-button",{attrs:{type:"primary",size:"large"},on:{click:t.goToLogin}},[t._v("\n        立即登录\n      ")])],1)])])},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"tip-text"},[t._v("\n          点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。"),a("span",{staticClass:"highlight-text"},[t._v("（压缩包无需解压）")])])}],s=a("a34a"),n=a.n(s),o=a("cfda");function c(t,e,a,i,r,s,n){try{var o=t[s](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,r)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(i,r){var s=t.apply(e,a);function n(t){c(s,i,r,n,o,"next",t)}function o(t){c(s,i,r,n,o,"throw",t)}n(void 0)}))}}var d={name:"AgentDetailPage",data:function(){return{isCheckingPermission:!0,isLoggedIn:!1,isPurchased:!1,agentDetail:{},workflowList:[],workflowLoading:!1,videoError:!1,videoCollapsed:!1,guideCollapsed:!1,activeNavItem:"agent-info",navItems:[{id:"agent-info",title:"智能体信息"},{id:"demo-video",title:"演示视频"},{id:"usage-guide",title:"使用说明"},{id:"workflow-list",title:"工作流列表"}],defaultAvatar:"/default-avatar.png",defaultCreatorAvatar:"/default-avatar.png"}},computed:{agentId:function(){return this.$route.query.agentId},authorTypeClass:function(){return"1"===this.agentDetail.authorType||1===this.agentDetail.authorType?"official":"creator"},authorTypeText:function(){return"1"===this.agentDetail.authorType||1===this.agentDetail.authorType?"官方":"创作者"}},created:function(){var t=l(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.checkPermissions();case 2:if(!this.isLoggedIn||!this.isPurchased){t.next=5;break}return t.next=5,this.loadAgentData();case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),mounted:function(){var t=this;window.addEventListener("scroll",this.handleScroll),this.$nextTick((function(){t.activeNavItem="agent-info",setTimeout((function(){t.handleScroll()}),500)}))},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll)},methods:{checkPermissions:function(){var t=l(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=5,Object(o["b"])();case 5:if(e=t.sent,this.isLoggedIn=!!e,this.isLoggedIn){t.next=12;break}return this.isCheckingPermission=!1,t.abrupt("return");case 12:return t.next=15,this.checkPurchaseStatus();case 15:this.isPurchased=t.sent,this.isCheckingPermission=!1,t.next=25;break;case 21:t.prev=21,t.t0=t["catch"](0),this.isCheckingPermission=!1;case 25:case"end":return t.stop()}}),t,this,[[0,21]])})));function e(){return t.apply(this,arguments)}return e}(),checkPurchaseStatus:function(){var t=l(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,this.$http.get("/api/agent/market/purchase/check/".concat(this.agentId));case 4:if(e=t.sent,!e||!e.success){t.next=12;break}return a=e.result.isPurchased,t.abrupt("return",a);case 12:return t.abrupt("return",!1);case 14:t.next=20;break;case 16:return t.prev=16,t.t0=t["catch"](0),t.abrupt("return",!1);case 20:case"end":return t.stop()}}),t,this,[[0,16]])})));function e(){return t.apply(this,arguments)}return e}(),loadAgentData:function(){var t=l(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.loadAgentDetail();case 3:t.next=9;break;case 5:t.prev=5,t.t0=t["catch"](0),this.$message.error("加载数据失败，请稍后重试");case 9:case"end":return t.stop()}}),t,this,[[0,5]])})));function e(){return t.apply(this,arguments)}return e}(),loadAgentDetail:function(){var t=l(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,this.$http.get("/api/agent/market/detail/".concat(this.agentId));case 4:if(e=t.sent,!e.success){t.next=13;break}this.agentDetail=e.result,document.title="".concat(this.agentDetail.agentName||"智能体详情"," - 智界AIGC"),this.agentDetail.workflowList?this.workflowList=this.agentDetail.workflowList:this.workflowList=[],t.next=14;break;case 13:throw new Error(e.message||"获取智能体详情失败");case 14:t.next=21;break;case 16:throw t.prev=16,t.t0=t["catch"](0),this.$message.error("加载智能体详情失败: "+t.t0.message),t.t0;case 21:case"end":return t.stop()}}),t,this,[[0,16]])})));function e(){return t.apply(this,arguments)}return e}(),goBack:function(){this.$router.go(-1)},goToLogin:function(){this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}})},goToPurchase:function(){this.$router.push("/workflow-center")},handleAvatarError:function(t){t.target.src=this.defaultAvatar},handleCreatorAvatarError:function(t){t.target.src=this.defaultCreatorAvatar},handleWorkflowImageError:function(t){t.target.src=this.defaultAgentAvatar},onVideoLoadStart:function(){this.videoError=!1},onVideoLoaded:function(){},onVideoError:function(){this.videoError=!0},onVideoPlay:function(){},onVideoPause:function(){},toggleVideoCollapse:function(){this.videoCollapsed=!this.videoCollapsed},toggleGuideCollapse:function(){this.guideCollapsed=!this.guideCollapsed},handleGuideImageError:function(t){t.target.style.display="none"},previewImage:function(t){document.body.style.overflow="hidden";var e=1,a=0,i=0,r=!1,s=0,n=0,o=null,c=document.createElement("div");c.style.cssText="\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 9999;\n        cursor: default;\n      ";var l=document.createElement("img");l.src=t,l.style.cssText="\n        max-width: 90%;\n        max-height: 90%;\n        object-fit: contain;\n        border-radius: 8px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);\n        user-select: none;\n        cursor: grab;\n      ";var d=document.createElement("div");d.style.cssText="\n        position: absolute;\n        top: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.7);\n        color: white;\n        padding: 8px 16px;\n        border-radius: 20px;\n        font-size: 14px;\n        z-index: 10000;\n        pointer-events: none;\n        opacity: 0.8;\n      ",d.textContent="滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭";var u=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];l.style.transition=t?"transform 0.3s ease":"none",l.style.transform="translate(".concat(a,"px, ").concat(i,"px) scale(").concat(e,")")};c.appendChild(l),c.appendChild(d),document.body.appendChild(c);var h=function(t){t.preventDefault();var a=t.deltaY>0?-.1:.1,i=Math.max(.5,Math.min(5,e+a));i!==e&&(e=i,u(!0),c.style.cursor="grab")},p=function(t){t.target===l&&(t.preventDefault(),t.stopPropagation(),r=!0,s=t.clientX,n=t.clientY,c.style.cursor="grabbing")},m=function(t){r&&(t.preventDefault(),o&&cancelAnimationFrame(o),o=requestAnimationFrame((function(){var e=t.clientX-s,r=t.clientY-n;a+=e,i+=r,s=t.clientX,n=t.clientY,u(!1)})))},v=function(t){r&&(t.preventDefault(),t.stopPropagation(),r=!1,c.style.cursor="grab")},g=function(){e=1,a=0,i=0,u(!0),c.style.cursor="grab"},f=function(){document.body.style.overflow="",o&&cancelAnimationFrame(o),c.removeEventListener("wheel",h),c.removeEventListener("mousedown",p),document.removeEventListener("mousemove",m),document.removeEventListener("mouseup",v),c.removeEventListener("dblclick",g),document.removeEventListener("keydown",w),document.body.removeChild(c)},w=function(t){"Escape"===t.key&&f()},C=function(t){t.target!==c||r||f()};c.addEventListener("wheel",h),c.addEventListener("mousedown",p),document.addEventListener("mousemove",m),document.addEventListener("mouseup",v),c.addEventListener("dblclick",g),c.addEventListener("click",C),document.addEventListener("keydown",w)},scrollToSection:function(t){var e=document.getElementById(t);e&&(e.scrollIntoView({behavior:"smooth",block:"start"}),this.activeNavItem=t)},scrollToUsageGuide:function(){this.scrollToSection("usage-guide")},handleScroll:function(){var t=window.pageYOffset||document.documentElement.scrollTop,e=window.innerHeight,a=document.documentElement.scrollHeight;if(t<100)"agent-info"!==this.activeNavItem&&(this.activeNavItem="agent-info");else{var i=this.navItems.map((function(t){var e=document.getElementById(t.id);return{id:t.id,offsetTop:e?e.offsetTop-150:0,element:e}})),r=t+e>=a-50,s=i[0].id;if(r)s=i[i.length-1].id;else for(var n=i.length-1;n>=0;n--)if(t>=i[n].offsetTop){s=i[n].id;break}this.activeNavItem!==s&&(this.activeNavItem=s)}},handleWorkflowDownload:function(){var t=l(n.a.mark((function t(e){var a,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.workflowPackage||e.packagePath){t.next=5;break}return this.$message.error("工作流压缩包地址不存在"),t.abrupt("return");case 5:this.$set(e,"downloading",!0),a=e.workflowPackage||e.packagePath,i=document.createElement("a"),i.href=a,i.download="".concat(e.workflowName||"工作流",".zip"),i.target="_blank",document.body.appendChild(i),i.click(),document.body.removeChild(i),this.$message.success('工作流 "'.concat(e.workflowName,'" 下载已开始')),t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](0),this.$message.error("下载失败: "+t.t0.message);case 22:return t.prev=22,this.$set(e,"downloading",!1),t.finish(22);case 25:case"end":return t.stop()}}),t,this,[[0,18,22,25]])})));function e(e){return t.apply(this,arguments)}return e}()}},u=d,h=(a("7be7"),a("2877")),p=Object(h["a"])(u,i,r,!1,null,"55488129",null);e["default"]=p.exports},a2b0:function(t,e,a){}}]);