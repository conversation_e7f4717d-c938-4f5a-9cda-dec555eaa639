(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~af8298de"],{"013f":function(t,e,a){},"04ff":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"jianying-draft-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("剪映小助手")]),a("p",{staticClass:"simple-subtitle"},[t._v("专业的剪映草稿管理工具")])]),a("div",{staticClass:"main-functions"},[a("div",{staticClass:"functions-container"},[a("div",{staticClass:"function-card download-card"},[a("div",{staticClass:"card-header"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"download"}})],1),a("h3",{staticClass:"card-title"},[t._v("下载小助手")]),a("p",{staticClass:"card-description"},[t._v("选择适合您操作系统的版本进行下载")])]),a("div",{staticClass:"download-options"},[a("div",{staticClass:"download-option",class:{downloading:t.isPlatformDownloading("windows"),recommended:"windows"===t.currentOS,disabled:t.isDownloadDisabled("windows")},on:{click:function(e){return t.handleDownload("windows")}}},[a("div",{staticClass:"option-icon"},[a("a-icon",{attrs:{type:"windows"}})],1),a("div",{staticClass:"option-content"},[a("h4",{staticClass:"option-title"},[t._v("\n                  Windows 版本\n                  "),"windows"===t.currentOS?a("span",{staticClass:"recommended-badge"},[t._v("推荐")]):t._e()]),a("p",{staticClass:"option-desc"},[t._v("适用于 Windows 8/10/11")]),t.isPlatformDownloading("windows")?a("p",{staticClass:"download-status"},[t._v("正在获取下载链接...")]):t._e(),t.getCooldownStatus("windows").inCooldown?a("p",{staticClass:"cooldown-status"},[t._v("\n                  请稍后再试 ("+t._s(t.getCooldownStatus("windows").remainingTime)+"s)\n                ")]):t._e()]),a("div",{staticClass:"option-arrow"},[a("a-icon",{attrs:{type:t.isPlatformDownloading("windows")?"loading":"download",spin:t.isPlatformDownloading("windows")}})],1)]),a("div",{staticClass:"download-option",class:{downloading:t.isPlatformDownloading("mac"),recommended:"mac"===t.currentOS,disabled:t.isDownloadDisabled("mac")},on:{click:function(e){return t.handleDownload("mac")}}},[a("div",{staticClass:"option-icon"},[a("a-icon",{attrs:{type:"apple"}})],1),a("div",{staticClass:"option-content"},[a("h4",{staticClass:"option-title"},[t._v("\n                  Mac 版本\n                  "),"mac"===t.currentOS?a("span",{staticClass:"recommended-badge"},[t._v("推荐")]):t._e()]),a("p",{staticClass:"option-desc"},[t._v("适用于 macOS 系统")]),t.isPlatformDownloading("mac")?a("p",{staticClass:"download-status"},[t._v("正在获取下载链接...")]):t._e(),t.getCooldownStatus("mac").inCooldown?a("p",{staticClass:"cooldown-status"},[t._v("\n                  请稍后再试 ("+t._s(t.getCooldownStatus("mac").remainingTime)+"s)\n                ")]):t._e()]),a("div",{staticClass:"option-arrow"},[a("a-icon",{attrs:{type:t.isPlatformDownloading("mac")?"loading":"download",spin:t.isPlatformDownloading("mac")}})],1)])]),a("div",{staticClass:"download-notice"},[a("div",{staticClass:"notice-item"},[a("a-icon",{attrs:{type:"info-circle"}}),a("span",[t._v("下载完成后，请按照安装向导完成安装")])],1)])]),a("div",{staticClass:"function-card draft-card"},[a("div",{staticClass:"card-header"},[a("div",{staticClass:"card-icon"},[a("a-icon",{attrs:{type:"file-text"}})],1),a("h3",{staticClass:"card-title"},[t._v("剪映草稿导出")]),a("p",{staticClass:"card-description"},[t._v("复制草稿链接到剪映小助手，一键下载并创建本地草稿文件"),a("br"),t._v("支持链接换行，批量处理多个草稿")])]),a("div",{staticClass:"draft-input-area"},[a("a-textarea",{staticClass:"draft-textarea",attrs:{placeholder:"请粘贴剪映草稿链接...",rows:6},model:{value:t.draftUrl,callback:function(e){t.draftUrl=e},expression:"draftUrl"}}),a("a-button",{staticClass:"copy-btn",attrs:{type:"primary",size:"large",disabled:!t.draftUrl},on:{click:t.handleCopyUrl}},[a("a-icon",{attrs:{type:"copy"}}),t._v("\n              复制草稿链接\n            ")],1)],1)])])]),a("div",{staticClass:"product-intro"},[a("div",{staticClass:"intro-container"},[a("div",{staticClass:"intro-header"},[a("h2",{staticClass:"intro-title"},[t._v("产品介绍")])]),a("div",{staticClass:"intro-content"},[a("div",{staticClass:"intro-card"},[a("div",{staticClass:"intro-icon"},[a("a-icon",{attrs:{type:"tool"}})],1),a("div",{staticClass:"intro-text"},[a("h3",[t._v("剪映小助手")]),a("p",[t._v("专为剪映用户打造的桌面工具，支持草稿文件的快速导入和管理。通过简单的链接分享，让您的创作更加便捷高效。支持Windows和Mac系统，本地处理保障数据安全。")])])])]),a("div",{staticClass:"features-grid"},t._l(t.features,(function(e){return a("div",{key:e.id,staticClass:"feature-item"},[a("div",{staticClass:"feature-icon"},[a("a-icon",{attrs:{type:e.icon}})],1),a("div",{staticClass:"feature-content"},[a("h4",{staticClass:"feature-title"},[t._v(t._s(e.title))]),a("p",{staticClass:"feature-description"},[t._v(t._s(e.description))])])])})),0)])])]),a("a-modal",{attrs:{title:"",footer:null,width:480,centered:!0,maskClosable:!1},model:{value:t.confirmModalVisible,callback:function(e){t.confirmModalVisible=e},expression:"confirmModalVisible"}},[a("div",{staticClass:"confirm-modal-content"},[a("div",{staticClass:"modal-icon"},[a("a-icon",{attrs:{type:"exclamation-circle"}})],1),a("div",{staticClass:"modal-title"},[t._v("系统不匹配提醒")]),a("div",{staticClass:"modal-message"},[t._v("\n        检测到您当前使用的是 "),a("strong",[t._v(t._s(t.confirmModalData.currentOS))]),t._v(" 系统，\n        但您选择下载 "),a("strong",[t._v(t._s(t.confirmModalData.targetOS))]),t._v(" 版本。\n        "),a("br"),a("br"),t._v("\n        确定要继续下载吗？\n      ")]),a("div",{staticClass:"modal-actions"},[a("a-button",{staticClass:"cancel-btn",on:{click:t.cancelDownload}},[t._v("\n          取消\n        ")]),a("a-button",{staticClass:"confirm-btn",attrs:{type:"primary"},on:{click:t.confirmDownload}},[t._v("\n          继续下载\n        ")])],1)])])],1)},s=[],n=a("a34a"),i=a.n(n),r=a("df7c");function c(t,e,a,o,s,n,i){try{var r=t[n](i),c=r.value}catch(l){return void a(l)}r.done?e(c):Promise.resolve(c).then(o,s)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(o,s){var n=t.apply(e,a);function i(t){c(n,o,s,i,r,"next",t)}function r(t){c(n,o,s,i,r,"throw",t)}i(void 0)}))}}var d={name:"JianYingDraft",components:{WebsitePage:r["default"]},data:function(){return{draftUrl:"",downloadingPlatforms:{},currentOS:null,confirmModalVisible:!1,confirmModalData:{},downloadCooldown:{},downloadHistory:{},cooldownTimer:null,features:[{id:1,icon:"cloud-download",title:"草稿文件导入",description:"支持从云端下载剪映草稿文件到本地，快速同步您的创作项目"},{id:2,icon:"folder",title:"文件管理",description:"智能管理下载的草稿文件，自动分类整理，方便查找和使用"},{id:3,icon:"setting",title:"路径配置",description:"灵活设置剪映软件安装路径，确保草稿文件正确导入到剪映编辑器"},{id:4,icon:"safety",title:"本地安全",description:"所有操作在本地进行，保护您的创作内容安全，不会泄露到第三方"},{id:5,icon:"thunderbolt",title:"批量处理",description:"支持批量下载和导入多个草稿文件，大幅提升工作效率"},{id:6,icon:"global",title:"跨平台支持",description:"同时支持Windows和Mac系统，满足不同用户的使用需求"}]}},computed:{isDownloadDisabled:function(){var t=this;return function(e){if(t.downloadingPlatforms[e])return!0;var a=t.checkDownloadCooldown(e);return!!a.inCooldown}},getCooldownStatus:function(){var t=this;return function(e){return t.checkDownloadCooldown(e)}}},mounted:function(){this.checkDraftParam(),this.detectCurrentOS(),this.initializeFromLocalStorage(),this.startCooldownTimer()},beforeDestroy:function(){this.cooldownTimer&&clearInterval(this.cooldownTimer),this.cleanupExpiredData()},methods:{detectCurrentOS:function(){var t=navigator.userAgent.toLowerCase();t.includes("mac")?this.currentOS="mac":t.includes("win")?this.currentOS="windows":t.includes("linux")?this.currentOS="linux":this.currentOS="unknown"},checkDownloadCooldown:function(t){var e=Date.now(),a=45e3,o=this.downloadCooldown[t];if(o&&e-o<a){var s=Math.ceil((a-(e-o))/1e3);return{inCooldown:!0,remainingTime:s}}return{inCooldown:!1,remainingTime:0}},checkDownloadHistory:function(t){var e=this.downloadHistory[t];if(e){var a=Date.now()-e.timestamp,o=Math.floor(a/6e4);if(o<10)return{hasRecent:!0,minutesAgo:o,fileName:e.fileName}}return{hasRecent:!1,minutesAgo:0,fileName:""}},setDownloadCooldown:function(t){var e=Date.now();this.$set(this.downloadCooldown,t,e),this.saveToLocalStorage("download_cooldown",this.downloadCooldown)},recordDownloadHistory:function(t,e){var a={timestamp:Date.now(),fileName:e};this.$set(this.downloadHistory,t,a),this.saveToLocalStorage("download_history",this.downloadHistory)},startCooldownTimer:function(){var t=this;this.cooldownTimer=setInterval((function(){t.$forceUpdate(),Date.now()%1e4<1e3&&t.cleanupExpiredData()}),1e3)},cleanupExpiredData:function(){var t=this,e=Date.now(),a=45e3,o=864e5,s=!1;Object.keys(this.downloadCooldown).forEach((function(o){var n=t.downloadCooldown[o];n&&e-n>=a&&(t.$delete(t.downloadCooldown,o),s=!0)}));var n=!1;Object.keys(this.downloadHistory).forEach((function(a){var s=t.downloadHistory[a];s&&s.timestamp&&e-s.timestamp>=o&&(t.$delete(t.downloadHistory,a),n=!0)})),s&&this.saveToLocalStorage("download_cooldown",this.downloadCooldown),n&&this.saveToLocalStorage("download_history",this.downloadHistory)},saveToLocalStorage:function(t,e){try{localStorage.setItem("jianying_assistant_".concat(t),JSON.stringify(e))}catch(a){}},loadFromLocalStorage:function(t){try{var e=localStorage.getItem("jianying_assistant_".concat(t));return e?JSON.parse(e):null}catch(a){return null}},clearLocalStorage:function(t){try{localStorage.removeItem("jianying_assistant_".concat(t))}catch(e){}},clearAllDownloadData:function(){this.downloadCooldown={},this.downloadHistory={},this.downloadingPlatforms={},this.clearLocalStorage("download_cooldown"),this.clearLocalStorage("download_history"),this.$message.success("已清理所有下载数据")},debugDownloadStatus:function(){var t={downloadingPlatforms:this.downloadingPlatforms,downloadCooldown:this.downloadCooldown,downloadHistory:this.downloadHistory,localStorageCooldown:this.loadFromLocalStorage("download_cooldown"),localStorageHistory:this.loadFromLocalStorage("download_history")};return t},setPlatformDownloading:function(t,e){this.$set(this.downloadingPlatforms,t,e)},isPlatformDownloading:function(t){return this.downloadingPlatforms[t]||!1},initializeFromLocalStorage:function(){var t=this,e=this.loadFromLocalStorage("download_cooldown");if(e){var a=Date.now(),o=45e3;Object.keys(e).forEach((function(s){var n=e[s];n&&a-n<o&&t.$set(t.downloadCooldown,s,n)}))}var s=this.loadFromLocalStorage("download_history");if(s){var n=Date.now(),i=864e5;Object.keys(s).forEach((function(e){var a=s[e];a&&a.timestamp&&n-a.timestamp<i&&t.$set(t.downloadHistory,e,a)}))}},checkDraftParam:function(){var t=this.$route.query.draft;t&&(this.draftUrl=t)},handleDownload:function(){var t=l(i.a.mark((function t(e){var a,o,s,n,r,c;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.isPlatformDownloading(e)){t.next=2;break}return t.abrupt("return");case 2:if(a=this.checkDownloadCooldown(e),!a.inCooldown){t.next=7;break}return o="windows"===e?"Windows":"Mac",this.$message.warning("".concat(o," 版本请稍后再试，还需等待 ").concat(a.remainingTime," 秒")),t.abrupt("return");case 7:if(s=this.checkDownloadHistory(e),s.hasRecent&&(n="windows"===e?"Windows":"Mac",0===s.minutesAgo?this.$message.info("检测到您刚刚下载过 ".concat(n," 版本")):this.$message.info("检测到您 ".concat(s.minutesAgo," 分钟前下载过 ").concat(n," 版本"))),!this.currentOS||this.currentOS===e||"unknown"===this.currentOS){t.next=15;break}return r="mac"===this.currentOS?"Mac":"Windows",c="mac"===e?"Mac":"Windows",this.confirmModalVisible=!0,this.confirmModalData={currentOS:r,targetOS:c,platform:e},t.abrupt("return");case 15:return t.next=17,this.executeDownload(e);case 17:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),confirmDownload:function(){var t=l(i.a.mark((function t(){var e;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.confirmModalVisible=!1,e=this.confirmModalData.platform,t.next=4,this.executeDownload(e);case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),cancelDownload:function(){this.confirmModalVisible=!1,this.confirmModalData={}},executeDownload:function(){var t=l(i.a.mark((function t(e){var a,o,s,n,r,c;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.setPlatformDownloading(e,!0),this.setDownloadCooldown(e),a={windows:"剪映小助手-智界-Windows.zip",mac:"剪映小助手-智界-Mac.zip"},o=a[e],o){t.next=8;break}return this.$message.error("不支持的平台类型"),t.abrupt("return");case 8:return t.next=10,this.axios.get("".concat(this.API_BASE_URL,"/sys/common/desktop-app-download"),{params:{platform:e}});case 10:s=t.sent,s.success&&s.result?(n=document.createElement("a"),n.href=s.result,n.download=o,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),this.recordDownloadHistory(e,o),this.$message.success("开始下载 ".concat("windows"===e?"Windows":"Mac"," 版本"))):this.$message.error(s.message||"获取下载链接失败"),t.next=33;break;case 14:if(t.prev=14,t.t0=t["catch"](0),!t.t0.response){t.next=32;break}r=t.t0.response.status,c=t.t0.response.data&&t.t0.response.data.message||t.t0.response.statusText,t.t1=r,t.next=404===t.t1?23:403===t.t1?25:500===t.t1?27:29;break;case 23:return this.$message.error("安装包文件不存在，请联系管理员"),t.abrupt("break",30);case 25:return this.$message.error("没有权限访问该文件"),t.abrupt("break",30);case 27:return this.$message.error("服务器内部错误，请稍后重试"),t.abrupt("break",30);case 29:this.$message.error("下载失败: ".concat(c));case 30:t.next=33;break;case 32:"NETWORK_ERROR"===t.t0.code||t.t0.message.includes("Network Error")?this.$message.error("网络连接失败，请检查网络后重试"):"TIMEOUT"===t.t0.code?this.$message.error("请求超时，请稍后重试"):this.$message.error("下载失败，请稍后重试");case 33:return t.prev=33,this.setPlatformDownloading(e,!1),t.finish(33);case 36:case"end":return t.stop()}}),t,this,[[0,14,33,36]])})));function e(e){return t.apply(this,arguments)}return e}(),handleCopyUrl:function(){var t=this;this.draftUrl&&(navigator.clipboard?navigator.clipboard.writeText(this.draftUrl).then((function(){t.$message.success("草稿链接已复制，请粘贴到剪映小助手中")})).catch((function(){t.fallbackCopyText(t.draftUrl)})):this.fallbackCopyText(this.draftUrl))},fallbackCopyText:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select();try{document.execCommand("copy"),this.$message.success("链接已复制到剪贴板")}catch(a){this.$message.error("复制失败，请手动复制")}document.body.removeChild(e)}}},u=d,h=(a("843e"),a("2877")),m=Object(h["a"])(u,o,s,!1,null,"66be1460",null);e["default"]=m.exports},"0fb8":function(t,e,a){},1885:function(t,e,a){},"1d6a":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"main"},[o("a-form",{staticStyle:{"max-width":"500px",margin:"40px auto 0"},attrs:{form:t.form},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.nextStep(e)}}},[o("a-form-item",[o("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["username",t.validatorRules.username],expression:"['username',validatorRules.username]"}],attrs:{size:"large",type:"text",autocomplete:"false",placeholder:"请输入用户账号或手机号"}},[o("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"lock"},slot:"prefix"})],1)],1),o("a-row",{attrs:{gutter:0}},[o("a-col",{attrs:{span:14}},[o("a-form-item",[o("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["inputCode",t.validatorRules.inputCode],expression:"['inputCode',validatorRules.inputCode]"}],attrs:{size:"large",type:"text",placeholder:"请输入验证码"},on:{change:t.inputCodeChange}},[t.inputCodeContent==t.verifiedCode?o("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"smile"},slot:"prefix"}):o("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"frown"},slot:"prefix"})],1)],1)],1),o("a-col",{staticStyle:{"text-align":"right"},attrs:{span:10}},[t.requestCodeSuccess?o("img",{staticStyle:{"margin-top":"2px"},attrs:{src:t.randCodeImage},on:{click:t.handleChangeCheckCode}}):o("img",{staticStyle:{"margin-top":"2px"},attrs:{src:a("d5ac")},on:{click:t.handleChangeCheckCode}})])],1),o("a-form-item",{attrs:{wrapperCol:{span:19,offset:5}}},[o("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")]),o("a-button",{attrs:{type:"primary"},on:{click:t.nextStep}},[t._v("下一步")])],1)],1)],1)},s=[],n=a("0fea"),i=a("4ec3"),r={name:"Step1",data:function(){return{form:this.$form.createForm(this),inputCodeContent:"",inputCodeNull:!0,verifiedCode:"",validatorRules:{username:{rules:[{required:!1},{validator:this.validateInputUsername}]},inputCode:{rules:[{required:!0,message:"请输入验证码!"}]}},randCodeImage:"",requestCodeSuccess:!0,currdatetime:""}},created:function(){this.handleChangeCheckCode()},methods:{handleChangeCheckCode:function(){var t=this;this.currdatetime=(new Date).getTime(),Object(n["c"])("/sys/randomImage/".concat(this.currdatetime)).then((function(e){e.success?(t.randCodeImage=e.result,t.requestCodeSuccess=!0):(t.$message.error(e.message),t.requestCodeSuccess=!1)})).catch((function(){t.requestCodeSuccess=!1}))},nextStep:function(){var t=this;this.form.validateFields((function(e,a){if(!e){var o=!1,s={},i=/^[1-9]\d*$|^0$/,r=a.username;1==i.test(r)?(s.phone=r,o=!0):s.username=r,t.validateInputCode().then((function(){Object(n["c"])("/sys/user/querySysUser",s).then((function(e){if(e.success){var a={username:e.result.username,phone:e.result.phone,isPhone:o};setTimeout((function(){t.$emit("nextStep",a)}))}}))}))}}))},validateInputCode:function(){var t=this;return new Promise((function(e,a){Object(n["i"])("/sys/checkCaptcha",{captcha:t.inputCodeContent,checkKey:t.currdatetime}).then((function(o){o.success?e():(t.$message.error(o.message),a())}))}))},inputCodeChange:function(t){this.inputCodeContent=t.target.value,t.target.value&&0!=t.target.value?(this.inputCodeContent=this.inputCodeContent.toLowerCase(),this.inputCodeNull=!1):this.inputCodeNull=!0},generateCode:function(t){this.verifiedCode=t.toLowerCase()},validateInputUsername:function(t,e,a){var o=/^[0-9]+.?[0-9]*/;if(e||a("请输入用户名和手机号！"),o.test(e)){var s={phone:e};Object(i["h"])(s).then((function(t){t.success?a("用户名不存在!"):a()}))}else{s={username:e};Object(i["h"])(s).then((function(t){t.success?a("用户名不存在!"):a()}))}}}},c=r,l=a("2877"),d=Object(l["a"])(c,o,s,!1,null,"5a355e54",null);e["default"]=d.exports},"1dae":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"main user-layout-register"},[t._m(0),a("a-form-model",{ref:"form",attrs:{model:t.model,rules:t.validatorRules}},[a("a-form-model-item",{attrs:{prop:"username"}},[a("a-input",{attrs:{size:"large",type:"text",autocomplete:"false",placeholder:"请输入用户名"},model:{value:t.model.username,callback:function(e){t.$set(t.model,"username",e)},expression:"model.username"}})],1),a("a-popover",{attrs:{placement:"rightTop",trigger:"click",visible:t.state.passwordLevelChecked}},[a("template",{slot:"content"},[a("div",{style:{width:"240px"}},[a("div",{class:["user-register",t.passwordLevelClass]},[t._v("强度："),a("span",[t._v(t._s(t.passwordLevelName))])]),a("a-progress",{attrs:{percent:t.state.percent,showInfo:!1,strokeColor:t.passwordLevelColor}}),a("div",{staticStyle:{"margin-top":"10px"}},[a("span",[t._v("请至少输入 8 个字符。请不要使用容易被猜到的密码。")])])],1)]),a("a-form-model-item",{attrs:{prop:"password"}},[a("a-input",{attrs:{size:"large",type:"password",autocomplete:"false",placeholder:"至少8位密码，区分大小写"},on:{click:t.handlePasswordInputClick},model:{value:t.model.password,callback:function(e){t.$set(t.model,"password",e)},expression:"model.password"}})],1)],2),a("a-form-model-item",{attrs:{prop:"password2"}},[a("a-input",{attrs:{size:"large",type:"password",autocomplete:"false",placeholder:"确认密码"},model:{value:t.model.password2,callback:function(e){t.$set(t.model,"password2",e)},expression:"model.password2"}})],1),a("a-form-model-item",{attrs:{prop:"mobile"}},[a("a-input",{attrs:{size:"large",placeholder:"11 位手机号"},model:{value:t.model.mobile,callback:function(e){t.$set(t.model,"mobile",e)},expression:"model.mobile"}},[a("a-select",{attrs:{slot:"addonBefore",size:"large",defaultValue:"+86"},slot:"addonBefore"},[a("a-select-option",{attrs:{value:"+86"}},[t._v("+86")]),a("a-select-option",{attrs:{value:"+87"}},[t._v("+87")])],1)],1)],1),a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:16}},[a("a-form-model-item",{attrs:{prop:"captcha"}},[a("a-input",{attrs:{size:"large",type:"text",placeholder:"验证码"},model:{value:t.model.captcha,callback:function(e){t.$set(t.model,"captcha",e)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{staticClass:"getCaptcha",attrs:{size:"large",disabled:t.state.smsSendBtn},domProps:{textContent:t._s(t.state.smsSendBtn?t.state.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getCaptcha(e)}}})],1)],1),a("a-form-model-item",[a("a-button",{staticClass:"register-button",attrs:{size:"large",type:"primary",htmlType:"submit",loading:t.registerBtn,disabled:t.registerBtn},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[t._v("注册\n      ")]),a("router-link",{staticClass:"login",attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")])],1)],1)],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("h3",[a("span",[t._v("注册")])])}],n=a("ac0d"),i=(a("7ded"),a("0fea")),r=a("4ec3"),c=a("c4f2");function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,o)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={0:"低",1:"低",2:"中",3:"强"},m={0:"error",1:"error",2:"warning",3:"success"},p={0:"#ff0000",1:"#ff0000",2:"#ff7e05",3:"#52c41a"},f={name:"Register",components:{},mixins:[n["b"]],data:function(){return{model:{},validatorRules:{username:[{required:!1},{validator:this.checkUsername}],password:[{required:!1},{validator:this.handlePasswordLevel}],password2:[{required:!1},{validator:this.handlePasswordCheck}],mobile:[{required:!1},{validator:this.handlePhoneCheck}],captcha:[{required:!1},{validator:this.handleCaptchaCheck}]},state:{time:60,smsSendBtn:!1,passwordLevel:0,passwordLevelChecked:!1,percent:10,progressColor:"#FF0000"},registerBtn:!1}},computed:{passwordLevelClass:function(){return m[this.state.passwordLevel]},passwordLevelName:function(){return h[this.state.passwordLevel]},passwordLevelColor:function(){return p[this.state.passwordLevel]}},methods:{checkUsername:function(t,e,a){if(e){var o={username:e};Object(r["h"])(o).then((function(t){t.success?a():a("用户名已存在!")}))}else a(new Error("请输入用户名"))},handleEmailCheck:function(t,e,a){var o={email:e};Object(r["h"])(o).then((function(t){t.success?a():a("邮箱已存在!")}))},handlePasswordLevel:function(t,e,a){var o=0,s=/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/;s.test(e)||a(new Error("密码由8位数字、大小写字母和特殊符号组成!")),/[0-9]/.test(e)&&o++,/[a-zA-Z]/.test(e)&&o++,/[^0-9a-zA-Z_]/.test(e)&&o++,this.state.passwordLevel=o,this.state.percent=30*o,o>=2?(o>=3&&(this.state.percent=100),a()):(0===o&&(this.state.percent=10),a(new Error("密码强度不够")))},handlePasswordCheck:function(t,e,a){var o=this.model["password"];void 0===e&&a(new Error("请输入密码")),e&&o&&e.trim()!==o.trim()&&a(new Error("两次密码不一致")),a()},handleCaptchaCheck:function(t,e,a){e?a():a(new Error("请输入验证码"))},handlePhoneCheck:function(t,e,a){var o=/^1[3456789]\d{9}$/;if(o.test(e)){var s={phone:e};Object(r["h"])(s).then((function(t){t.success?a():a("手机号已存在!")}))}else a(new Error("请输入正确手机号"))},handlePasswordInputClick:function(){this.isMobile()?this.state.passwordLevelChecked=!1:this.state.passwordLevelChecked=!0},handleSubmit:function(){var t=this;this.$refs["form"].validate((function(e){if(1==e){var a=t.model,o=Object(c["a"])(),s={username:a.username,password:a.password,phone:a.mobile,smscode:a.captcha,inviteCode:o};Object(i["i"])("/sys/user/register",s).then((function(e){e.success?t.$router.push({name:"registerResult",params:d({},a)}):t.registerFailed(e.message)}))}}))},getCaptcha:function(t){var e=this;t.preventDefault();var a=this;this.$refs["form"].validateField(["mobile"],(function(t){if(!t){e.state.smsSendBtn=!0;var o=window.setInterval((function(){a.state.time--<=0&&(a.state.time=60,a.state.smsSendBtn=!1,window.clearInterval(o))}),1e3),s=e.$message.loading("验证码发送中..",3),n={mobile:e.model.mobile,smsmode:"1"};Object(i["i"])("/sys/sms",n).then((function(t){t.success||(e.registerFailed(t.message),setTimeout(s,0)),setTimeout(s,500)})).catch((function(t){setTimeout(s,1),clearInterval(o),a.state.time=60,a.state.smsSendBtn=!1,e.requestFailed(t)}))}}))},registerFailed:function(t){this.$notification["error"]({message:"注册失败",description:t,duration:2})},requestFailed:function(t){this.$notification["error"]({message:"错误",description:((t.response||{}).data||{}).message||"请求出现错误，请稍后再试",duration:4}),this.registerBtn=!1}},watch:{"state.passwordLevel":function(t){}}},v=f,w=(a("7afd"),a("bdfc"),a("2877")),g=Object(w["a"])(v,o,s,!1,null,"18861612",null);e["default"]=g.exports},2914:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"user-login-other"},[a("span",[t._v("其他登录方式")]),a("a",{attrs:{title:"github"},on:{click:function(e){return t.onThirdLogin("github")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"github"}})],1),a("a",{attrs:{title:"企业微信"},on:{click:function(e){return t.onThirdLogin("wechat_enterprise")}}},[a("icon-font",{staticClass:"item-icon",attrs:{type:"icon-qiyeweixin3"}})],1),a("a",{attrs:{title:"钉钉"},on:{click:function(e){return t.onThirdLogin("dingtalk")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"dingding"}})],1),a("a",{attrs:{title:"微信"},on:{click:function(e){return t.onThirdLogin("wechat_open")}}},[a("a-icon",{staticClass:"item-icon",attrs:{type:"wechat"}})],1)]),a("a-modal",{attrs:{title:"请输入密码",visible:t.thirdPasswordShow},on:{ok:t.thirdLoginCheckPassword,cancel:t.thirdLoginNoPassword}},[a("a-input-password",{attrs:{placeholder:"请输入密码"},model:{value:t.thirdLoginPassword,callback:function(e){t.thirdLoginPassword=e},expression:"thirdLoginPassword"}})],1),a("a-modal",{class:"ant-modal-confirm",attrs:{footer:null,closable:!1,visible:t.thirdConfirmShow}},[a("div",{staticClass:"ant-modal-confirm-body-wrapper"},[a("div",{staticClass:"ant-modal-confirm-body"},[a("a-icon",{staticStyle:{color:"#faad14"},attrs:{type:"question-circle"}}),a("span",{staticClass:"ant-modal-confirm-title"},[t._v("提示")]),a("div",{staticClass:"ant-modal-confirm-content"},[t._v("\n          已有同名账号存在,请确认是否绑定该账号？\n        ")])],1),a("div",{staticClass:"ant-modal-confirm-btns"},[a("a-button",{attrs:{loading:t.thirdCreateUserLoding},on:{click:t.thirdLoginUserCreate}},[t._v("创建新账号")]),a("a-button",{attrs:{type:"primary"},on:{click:t.thirdLoginUserBind}},[t._v("确认绑定")])],1)])]),a("a-modal",{class:"ant-modal-confirm",attrs:{visible:t.bindingPhoneModal}},[a("template",{slot:"footer"},[a("a-button",{key:"submit",attrs:{type:"primary"},on:{click:t.thirdHandleOk}},[t._v("\n        确定\n      ")])],1),a("div",{staticClass:"ant-modal-confirm-body-wrapper"},[a("a-form-model-item",[a("span",[t._v("绑定手机号")])]),a("a-form-model-item",[a("a-input",{attrs:{size:"large",type:"text",placeholder:"手机号"},model:{value:t.thirdPhone,callback:function(e){t.thirdPhone=e},expression:"thirdPhone"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mobile"},slot:"prefix"})],1)],1),a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:16}},[a("a-form-model-item",[a("a-input",{attrs:{size:"large",type:"text",placeholder:"请输入验证码"},model:{value:t.thirdCaptcha,callback:function(e){t.thirdCaptcha=e},expression:"thirdCaptcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"mail"},slot:"prefix"})],1)],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{staticClass:"getCaptcha",attrs:{tabindex:"-1",disabled:t.thirdState.smsSendBtn},domProps:{textContent:t._s(t.thirdState.smsSendBtn?t.thirdState.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getThirdCaptcha(e)}}})],1)],1)],1)],2)],1)},s=[],n=a("2f62"),i=a("0fea"),r=a("ca00");function c(t){return c="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function l(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,o)}return a}function d(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?l(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var h={data:function(){return{thirdLoginInfo:"",thirdPasswordShow:!1,thirdLoginPassword:"",thirdLoginUser:"",thirdConfirmShow:!1,thirdCreateUserLoding:!1,thirdLoginState:!1,bindingPhoneModal:!1,thirdPhone:"",thirdCaptcha:"",thirdState:{time:30,smsSendBtn:!1},thirdUserUuid:"",thirdType:"",url:{bindingThirdPhone:"/sys/thirdLogin/bindingThirdPhone"}}},created:function(){},methods:d(d({},Object(n["b"])(["ThirdLogin"])),{},{onThirdLogin:function(t){var e=window._CONFIG["domianURL"]+"/sys/thirdLogin/render/".concat(t);window.open(e,"login ".concat(t),"height=500, width=500, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no");var a=this;a.thirdType=t,a.thirdLoginInfo="",a.thirdLoginState=!1;var o=function(t){var e=t.data;if("string"===typeof e)if("登录失败"===e)a.$message.warning(e);else if(e.includes("绑定手机号")){a.bindingPhoneModal=!0;var o=e.split(",");a.thirdUserUuid=o[1]}else a.doThirdLogin(e);else"object"===c(e)?!0===e["isObj"]&&(a.thirdConfirmShow=!0,a.thirdLoginInfo=d({},e)):a.$message.warning("不识别的信息传递")};window.addEventListener("message",o,!1)},doThirdLogin:function(t){var e=this;if(!1===this.thirdLoginState){this.thirdLoginState=!0;var a={};a.thirdType=this.thirdType,a.token=t,this.ThirdLogin(a).then((function(t){t.success?e.loginSuccess():e.requestFailed(t)}))}},thirdLoginUserBind:function(){this.thirdLoginPassword="",this.thirdLoginUser=this.thirdLoginInfo.uuid,this.thirdConfirmShow=!1,this.thirdPasswordShow=!0},thirdLoginUserCreate:function(){var t=this;this.thirdCreateUserLoding=!0,this.thirdLoginInfo["suffix"]=parseInt(98*Math.random()+1),Object(i["i"])("/sys/third/user/create",this.thirdLoginInfo).then((function(e){if(e.success){var a=e.result;t.doThirdLogin(a),t.thirdConfirmShow=!1}else t.$message.warning(e.message)})).finally((function(){t.thirdCreateUserLoding=!1}))},thirdLoginCheckPassword:function(){var t=this,e=Object.assign({},this.thirdLoginInfo,{password:this.thirdLoginPassword});Object(i["i"])("/sys/third/user/checkPassword",e).then((function(e){e.success?(t.thirdLoginNoPassword(),t.doThirdLogin(e.result)):t.$message.warning(e.message)}))},thirdLoginNoPassword:function(){this.thirdPasswordShow=!1,this.thirdLoginPassword="",this.thirdLoginUser=""},getThirdCaptcha:function(){var t=this,e=this;if(this.thirdPhone){this.thirdState.smsSendBtn=!0;var a=window.setInterval((function(){e.thirdState.time--<=0&&(e.thirdState.time=30,e.thirdState.smsSendBtn=!1,window.clearInterval(a))}),1e3),o=this.$message.loading("验证码发送中..",0),s={};s.mobile=this.thirdPhone,s.smsmode="0",Object(i["i"])("/sys/sms",s).then((function(e){e.success||(setTimeout(o,0),t.cmsFailed(e.message)),setTimeout(o,500)})).catch((function(s){setTimeout(o,1),clearInterval(a),e.thirdState.time=30,e.thirdState.smsSendBtn=!1,t.requestFailed(s)}))}else e.cmsFailed("请输入手机号")},thirdHandleOk:function(){var t=this,e={};e.mobile=this.thirdPhone,e.captcha=this.thirdCaptcha,e.thirdUserUuid=this.thirdUserUuid,Object(i["i"])(this.url.bindingThirdPhone,e).then((function(e){e.success?(t.bindingPhoneModal=!1,t.doThirdLogin(e.result)):t.$message.warning(e.message)}))},loginSuccess:function(){this.$router.push({path:"/dashboard/analysis"}).catch((function(){})),this.$notification.success({message:"欢迎",description:"".concat(Object(r["o"])(),"，欢迎回来")})},cmsFailed:function(t){this.$notification["error"]({message:"登录失败",description:t,duration:4})},requestFailed:function(t){this.$notification["error"]({message:"登录失败",description:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",duration:4}),this.loginBtn=!1}})},m=a("0c63"),p=m["a"].createFromIconfontCN({scriptUrl:"/cdn/font-icon/font_2316098_umqusozousr.js"}),f={name:"thirdLogin",mixins:[h],components:{IconFont:p}},v=f,w=(a("8cea"),a("2877")),g=Object(w["a"])(v,o,s,!1,null,"0d925ab8",null);e["default"]=g.exports},"2ade":function(t,e,a){},"350c":function(t,e,a){},5193:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{attrs:{id:"loader-wrapper"}},[a("div",{attrs:{id:"loader"}}),a("div",{staticClass:"loader-section section-left"}),a("div",{staticClass:"loader-section section-right"}),a("div",{staticClass:"load_title"},[t._v("正在登录 智界AIGC，请耐心等待")])])])}],n=a("2f62"),i=a("ca00"),r=a("9fb0");function c(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,o)}return a}function l(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?c(Object(a),!0).forEach((function(e){d(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):c(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function d(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var u={name:"OAuth2Login",data:function(){return{env:{thirdApp:!1,wxWork:!1,dingtalk:!1}}},beforeCreate:function(){Object(i["h"])()||this.$router.replace({path:"/user/login"})},created:function(){this.checkEnv(),this.doOAuth2Login()},methods:l(l({},Object(n["b"])(["ThirdLogin"])),{},{checkEnv:function(){/wxwork/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.wxWork=!0),/dingtalk/i.test(navigator.userAgent)&&(this.env.thirdApp=!0,this.env.dingtalk=!0)},doOAuth2Login:function(){if(this.env.thirdApp)if(this.$route.query.oauth2LoginToken){this.thirdType=this.$route.query.thirdType;var t=this.$route.query.oauth2LoginToken;this.doThirdLogin(t)}else this.env.wxWork?this.doWechatEnterpriseOAuth2Login():this.env.dingtalk&&this.doDingTalkOAuth2Login()},doThirdLogin:function(t){var e=this,a={};a.thirdType=this.thirdType,a.token=t,this.ThirdLogin(a).then((function(t){t.success?e.loginSuccess():e.requestFailed(t)}))},loginSuccess:function(){this.$router.replace({path:r["n"]}),this.$notification.success({message:"欢迎",description:"".concat(Object(i["o"])(),"，欢迎回来")})},requestFailed:function(t){this.$error({title:"登录失败",content:((t.response||{}).data||{}).message||t.message||"请求出现错误，请稍后再试",okText:"重新登陆",onOk:function(){window.location.reload()},onCancel:function(){window.location.reload()}})},doWechatEnterpriseOAuth2Login:function(){this.sysOAuth2Login("wechat_enterprise")},doDingTalkOAuth2Login:function(){this.sysOAuth2Login("dingtalk")},sysOAuth2Login:function(t){var e="".concat(window._CONFIG["domianURL"],"/sys/thirdLogin/oauth2/").concat(t,"/login");e+="?state=".concat(encodeURIComponent(window.location.origin)),window.location.href=e}})},h=u,m=a("2877"),p=Object(m["a"])(h,o,s,!1,null,"00fca87c",null);e["default"]=p.exports},"5cfa":function(t,e,a){},"7afd":function(t,e,a){"use strict";var o=a("1885"),s=a.n(o);s.a},"843e":function(t,e,a){"use strict";var o=a("0fb8"),s=a.n(o);s.a},8859:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{staticStyle:{width:"130%","text-align":"center","margin-left":"-10%"},attrs:{bordered:!1}},[a("a-steps",{staticClass:"steps",attrs:{current:t.currentTab}},[a("a-step",{attrs:{title:"手机验证"}}),a("a-step",{attrs:{title:"更改密码"}}),a("a-step",{attrs:{title:"完成"}})],1),a("div",{staticClass:"content"},[0===t.currentTab?a("step2",{on:{nextStep:t.nextStep}}):t._e(),1===t.currentTab?a("step3",{attrs:{userList:t.userList},on:{nextStep:t.nextStep,prevStep:t.prevStep}}):t._e(),2===t.currentTab?a("step4",{attrs:{userList:t.userList},on:{prevStep:t.prevStep,finish:t.finish}}):t._e()],1)],1)},s=[],n=a("a73d"),i=a("977f"),r=a("b23d"),c={name:"Alteration",components:{Step2:n["default"],Step3:i["default"],Step4:r["default"]},data:function(){return{description:"将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",currentTab:0,userList:{},form:null}},methods:{nextStep:function(t){this.userList=t,this.currentTab<4&&(this.currentTab+=1)},prevStep:function(t){this.userList=t,this.currentTab>0&&(this.currentTab-=1)},finish:function(){this.currentTab=0}}},l=c,d=(a("c4f8"),a("2877")),u=Object(d["a"])(l,o,s,!1,null,"8733cc9e",null);e["default"]=u.exports},"8cea":function(t,e,a){"use strict";var o=a("ea4e"),s=a.n(o);s.a},"8ef0":function(t,e,a){"use strict";var o=a("013f"),s=a.n(o);s.a},"94f4":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("result",{attrs:{isSuccess:!0,content:!1,title:t.email}},[a("template",{slot:"action"},[a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{size:"large"},on:{click:t.goHomeHandle}},[t._v("返回首页")])],1)],2)},s=[],n=a("9a3d"),i={name:"RegisterResult",components:{Result:n["default"]},data:function(){return{model:{}}},computed:{email:function(){var t=this.model?this.model.username||this.model.mobile:" XXX ",e="你的账户：".concat(t," 注册成功");return this.username=t,e}},created:function(){this.model=this.$route.params},methods:{goHomeHandle:function(){var t={};t.username=this.model.username,t.password=this.model.password,this.$router.push({name:"login",params:t})}}},r=i,c=a("2877"),l=Object(c["a"])(r,o,s,!1,null,"1a9021fb",null);e["default"]=l.exports},"977f":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:t.model,rules:t.validatorRules}},[a("a-form-model-item",t._b({attrs:{label:"账号名"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"text",value:t.accountName,disabled:""}})],1),a("a-form-model-item",t._b({staticClass:"stepFormText",attrs:{prop:"password",label:"新密码"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:t.model.password,callback:function(e){t.$set(t.model,"password",e)},expression:"model.password"}})],1),a("a-form-model-item",t._b({staticClass:"stepFormText",attrs:{prop:"confirmPassword",label:"确认密码"}},"a-form-model-item",t.layout,!1),[a("a-input",{attrs:{type:"password",autocomplete:"false"},model:{value:t.model.confirmPassword,callback:function(e){t.$set(t.model,"confirmPassword",e)},expression:"model.confirmPassword"}})],1),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:t.prevStep}},[t._v("上一步")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{loading:t.loading,type:"primary"},on:{click:t.nextStep}},[t._v("提交")])],1)],1)],1)},s=[],n=a("0fea"),i={name:"Step3",props:["userList"],data:function(){return{model:{},layout:{labelCol:{span:5},wrapperCol:{span:19}},loading:!1,accountName:this.userList.username,validatorRules:{password:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!!"}],confirmPassword:[{required:!0,message:"密码不能为空!"},{validator:this.handlePasswordCheck}]}}},methods:{nextStep:function(){var t=this;t.loading=!0,t.$refs["form"].validate((function(e){if(!0===e){var a={username:t.userList.username,password:t.model.password,smscode:t.userList.smscode,phone:t.userList.phone};Object(n["c"])("/sys/user/passwordChange",a).then((function(e){if(e.success){var a={username:t.userList.username};setTimeout((function(){t.$emit("nextStep",a)}),1500)}else t.passwordFailed(e.message),t.loading=!1}))}else t.loading=!1}))},prevStep:function(){this.$emit("prevStep",this.userList)},handlePasswordCheck:function(t,e,a){var o=this.model["password"];e&&o&&e.trim()!==o.trim()&&a(new Error("两次密码不一致")),a()},passwordFailed:function(t){this.$notification["error"]({message:"更改密码失败",description:t,duration:4})}}},r=i,c=(a("f55e"),a("2877")),l=Object(c["a"])(r,o,s,!1,null,"3c0160d7",null);e["default"]=l.exports},a2cc:function(t,e,a){},a73d:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form-model",{ref:"form",staticClass:"password-retrieval-form",attrs:{model:t.model,rules:t.validatorRules},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.nextStep(e)}}},[a("a-form-model-item",{attrs:{label:"手机",required:"",prop:"phone",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:20}},[a("a-input",{attrs:{type:"text",autocomplete:"false",placeholder:"请输入手机号"},model:{value:t.model.phone,callback:function(e){t.$set(t.model,"phone",e)},expression:"model.phone"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"phone"},slot:"prefix"})],1)],1)],1)],1),t.show?a("a-form-model-item",{attrs:{required:"",prop:"captcha",label:"验证码",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{staticClass:"gutter-row",attrs:{span:12}},[a("a-input",{attrs:{type:"text",placeholder:"手机短信验证码"},model:{value:t.model.captcha,callback:function(e){t.$set(t.model,"captcha",e)},expression:"model.captcha"}},[a("a-icon",{style:{color:"rgba(0,0,0,.25)"},attrs:{slot:"prefix",type:"code"},slot:"prefix"})],1)],1),a("a-col",{staticClass:"gutter-row",attrs:{span:8}},[a("a-button",{attrs:{tabindex:"-1",size:"default",disabled:t.state.smsSendBtn},domProps:{textContent:t._s(t.state.smsSendBtn?t.state.time+" s":"获取验证码")},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.getCaptcha(e)}}})],1)],1)],1):t._e(),a("a-form-model-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("router-link",{staticStyle:{float:"left","line-height":"40px"},attrs:{to:{name:"login"}}},[t._v("使用已有账户登录")]),a("a-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:t.nextStep}},[t._v("下一步")])],1)],1)],1)},s=[],n=a("0fea"),i={name:"Step2",props:["userList"],data:function(){return{model:{},loading:!1,dropList:"0",captcha:"",show:!0,state:{time:60,smsSendBtn:!1},formLogin:{captcha:"",mobile:""},validatorRules:{phone:[{required:!0,message:"请输入手机号码!"},{validator:this.validatePhone}],captcha:[{required:!0,message:"请输入短信验证码!"}]}}},computed:{},methods:{nextStep:function(){var t=this,e=this;e.loading=!0,this.$refs["form"].validate((function(a){if(1==a){var o={phone:t.model.phone,smscode:t.model.captcha};Object(n["i"])("/sys/user/phoneVerification",o).then((function(a){if(a.success){var s={username:a.result.username,phone:o.phone,smscode:a.result.smscode};setTimeout((function(){e.$emit("nextStep",s)}),0)}else t.cmsFailed(a.message)}))}}))},getCaptcha:function(t){t.preventDefault();var e=this;e.$refs["form"].validateField("phone",(function(t){if(t)e.cmsFailed(t);else{e.state.smsSendBtn=!0;var a=window.setInterval((function(){e.state.time--<=0&&(e.state.time=60,e.state.smsSendBtn=!1,window.clearInterval(a))}),1e3),o=e.$message.loading("验证码发送中..",0),s={mobile:e.model.phone,smsmode:"2"};Object(n["i"])("/sys/sms",s).then((function(t){t.success||(setTimeout(o,1),e.cmsFailed(t.message)),setTimeout(o,500)}))}}))},cmsFailed:function(t){this.$notification["error"]({message:"验证错误",description:t,duration:4})},handleChangeSelect:function(t){var e=this;0==t?(e.dropList="0",e.show=!0):(e.dropList="1",e.show=!1)},validatePhone:function(t,e,a){if(e){var o=/^[1][3,4,5,7,8][0-9]{9}$/;o.test(e)?a():a("请输入正确的手机号")}else a()}}},r=i,c=(a("8ef0"),a("2877")),l=Object(c["a"])(r,o,s,!1,null,"14343278",null);e["default"]=l.exports},aa79:function(t,e,a){},ad9c:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"about-page"},[a("WebsiteHeader",{attrs:{transparent:!1}}),a("div",{staticClass:"about-container"},[a("div",{staticClass:"about-content"},[t._m(0),t._m(1),t._m(2),t._m(3),t._m(4),t._m(5),a("div",{staticClass:"legal-footer"},[a("a-button",{staticClass:"back-btn",attrs:{type:"primary",size:"large"},on:{click:t.goBack}},[a("a-icon",{attrs:{type:"arrow-left"}}),t._v("\n          返回\n        ")],1)],1)])])],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("header",{staticClass:"about-header"},[a("h1",{staticClass:"about-title"},[t._v("平台介绍")]),a("p",{staticClass:"about-subtitle"},[t._v("了解智界AIGC的使命、愿景与核心能力")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"about-section"},[a("h2",[t._v("关于智界AIGC")]),a("p",[t._v("\n          智界AIGC是一站式 AI 内容生产平台，面向创作者和企业提供从图文到视频的全流程创作能力。\n          平台以“数据驱动 + 工作流编排”为核心，通过插件生态、工作流中心与开放 API，帮助用户快速构建高质量的内容生产线。\n        ")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"about-section"},[a("h2",[t._v("使命与愿景")]),a("ul",[a("li",[t._v("使命：用 AI 让内容创作更简单高效")]),a("li",[t._v("愿景：成为新一代创作基础设施，赋能千行百业的内容生产")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"about-section"},[a("h2",[t._v("核心能力")]),a("ul",[a("li",[t._v("插件中心：标准化能力封装，开箱即用")]),a("li",[t._v("工作流中心：低代码编排数据流与任务流")]),a("li",[t._v("剪映小助手：草稿生成、素材加工、时间线合成")]),a("li",[t._v("开放 API：音视频、字幕、特效、关键帧与数据处理")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"about-section"},[a("h2",[t._v("团队与文化")]),a("p",[t._v("\n          我们是一支重实践、重体验、重质量的工程型团队，倡导「简单、稳定、可信」的产品理念，持续构建可复用、可度量、可扩展的能力底座。\n        ")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"about-section"},[a("h2",[t._v("联系我们")]),a("p",[t._v("商务合作 / 媒体沟通：<EMAIL>")]),a("p",[t._v("隐私与合规：<EMAIL>")])])}],n=a("ccb3"),i={name:"WebsiteAbout",components:{WebsiteHeader:n["default"]},metaInfo:{title:"平台介绍 - 智界AIGC",meta:[{name:"description",content:"了解智界AIGC的使命、愿景与核心能力"}]},methods:{goBack:function(){window.history&&window.history.length>1?this.$router.go(-1):this.$router.push("/home")}}},r=i,c=(a("bcf9"),a("2877")),l=Object(c["a"])(r,o,s,!1,null,"4dd288b1",null);e["default"]=l.exports},b23d:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form",{staticStyle:{margin:"40px auto 0"}},[a("result",{attrs:{title:"更改密码成功","is-success":!0}},[a("div",{staticClass:"toLogin"},[a("h3",[t._v("将在"),a("span",[t._v(t._s(t.time))]),t._v("秒后返回登录页面.")])])])],1)],1)},s=[],n=a("9a3d"),i={name:"Step4",props:["userList"],components:{Result:n["default"]},data:function(){return{loading:!1,time:0}},methods:{countDown:function(){var t=this;t.time--}},mounted:function(){var t=this;t.time=5,setInterval(t.countDown,1e3)},watch:{time:function(t,e){if(0==t){var a={username:this.userList.username};this.$router.push({name:"login",params:a})}}}},r=i,c=(a("d8b8"),a("2877")),l=Object(c["a"])(r,o,s,!1,null,"0ac9a29e",null);e["default"]=l.exports},bcf9:function(t,e,a){"use strict";var o=a("350c"),s=a.n(o);s.a},bdfc:function(t,e,a){"use strict";var o=a("2ade"),s=a.n(o);s.a},c4f8:function(t,e,a){"use strict";var o=a("aa79"),s=a.n(o);s.a},d8b8:function(t,e,a){"use strict";var o=a("a2cc"),s=a.n(o);s.a},ea4e:function(t,e,a){},f55e:function(t,e,a){"use strict";var o=a("5cfa"),s=a.n(o);s.a}}]);