(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~f6f6ed3b"],{"077e":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,okText:"保存并安排任务",cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"任务类名",prop:"jobClassName",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入任务类名"},model:{value:e.model.jobClassName,callback:function(t){e.$set(e.model,"jobClassName",t)},expression:"model.jobClassName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"Cron表达式",prop:"cronExpression"}},[a("j-easy-cron",{model:{value:e.model.cronExpression,callback:function(t){e.$set(e.model,"cronExpression",t)},expression:"model.cronExpression"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"参数",prop:"parameter"}},[a("a-textarea",{attrs:{placeholder:"请输入参数",rows:5},model:{value:e.model.parameter,callback:function(t){e.$set(e.model,"parameter",t)},expression:"model.parameter"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述",prop:"description"}},[a("a-textarea",{attrs:{placeholder:"请输入描述",rows:3},model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态",prop:"status"}},[a("j-dict-select-tag",{attrs:{type:"radioButton",dictCode:"quartz_status"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("4ba5"),o={name:"QuartzJobModal",components:{},data:function(){return{title:"操作",buttonStyle:"solid",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},cron:{label:"",value:""},confirmLoading:!1,validatorRules:{cronExpression:[{required:!0,message:"请输入cron表达式!"},{validator:i["a"]}],jobClassName:[{required:!0,message:"请输入任务类名!"}]},url:{add:"/sys/quartzJob/add",edit:"/sys/quartzJob/edit"}}},created:function(){},methods:{add:function(){this.edit({cronExpression:"* * * * * ? *",status:0})},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.form.resetFields(),t.model=Object.assign({},e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a,s){if(a){t.confirmLoading=!0;var r="",i="";e.model.id?(r+=e.url.edit,i="put"):(r+=e.url.add,i="post"),Object(l["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.close()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"0cc59faa",null);t["default"]=d.exports},"11cb":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{width:900,keyboard:!1,closable:!1,centered:!0,maskClosable:!1,mask:!1,okText:"确认",cancelText:"取消"},on:{ok:e.ok,cancel:e.cancel},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[a("a-tabs",[a("a-tab-pane",{key:"1",attrs:{tab:"方向性图标"}},[a("ul",e._l(e.icons.directionIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)]),a("a-tab-pane",{key:"2",attrs:{tab:"指示性图标"}},[a("ul",e._l(e.icons.suggestionIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)]),a("a-tab-pane",{key:"3",attrs:{tab:"编辑类图标"}},[a("ul",e._l(e.icons.editIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)]),a("a-tab-pane",{key:"4",attrs:{tab:"数据类图标"}},[a("ul",e._l(e.icons.dataIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)]),a("a-tab-pane",{key:"5",attrs:{tab:"网站通用图标"}},[a("ul",e._l(e.icons.webIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)]),a("a-tab-pane",{key:"6",attrs:{tab:"品牌和标识"}},[a("ul",e._l(e.icons.logoIcons,(function(t){return a("li",{key:t},[a("a-icon",{class:{active:e.activeIndex===t},attrs:{type:t,title:t},on:{click:function(a){return e.chooseIcon(t)}}})],1)})),0)])],1)],1)},r=[],l=["step-backward","step-forward","fast-backward","fast-forward","shrink","arrows-alt","down","up","left","right","caret-up","caret-down","caret-left","caret-right","up-circle","down-circle","left-circle","right-circle","up-circle-o","down-circle-o","right-circle-o","left-circle-o","double-right","double-left","vertical-left","vertical-right","forward","backward","rollback","enter","retweet","swap","swap-left","swap-right","arrow-up","arrow-down","arrow-left","arrow-right","play-circle","play-circle-o","up-square","down-square","left-square","right-square","up-square-o","down-square-o","left-square-o","right-square-o","login","logout","menu-fold","menu-unfold","border-bottom","border-horizontal","border-inner","border-left","border-right","border-top","border-verticle","pic-center","pic-left","pic-right","radius-bottomleft","radius-bottomright","radius-upleft","radius-upright","fullscreen","fullscreen-exit"],i=["question","question-circle","plus","plus-circle","pause","pause-circle","minus","minus-circle","plus-square","minus-square","info","info-circle","exclamation","exclamation-circle","close","close-circle","close-square","check","check-circle","check-square","clock-circle","warning","issues-close","stop"],o=["edit","form","copy","scissor","delete","snippets","diff","highlight","align-center","align-left","align-right","bg-colors","bold","italic","underline","strikethrough","redo","undo","zoom-in","zoom-out","font-colors","font-size","line-height","colum-height","dash","small-dash","sort-ascending","sort-descending","drag","ordered-list","radius-setting"],n=["area-chart","pie-chart","bar-chart","dot-chart","line-chart","radar-chart","heat-map","fall","rise","stock","box-plot","fund","sliders"],c=["lock","unlock","bars","book","calendar","cloud","cloud-download","code","copy","credit-card","delete","desktop","download","ellipsis","file","file-text","file-unknown","file-pdf","file-word","file-excel","file-jpg","file-ppt","file-markdown","file-add","folder","folder-open","folder-add","hdd","frown","meh","smile","inbox","laptop","appstore","link","mail","mobile","notification","paper-clip","picture","poweroff","reload","search","setting","share-alt","shopping-cart","tablet","tag","tags","to-top","upload","user","video-camera","home","loading","loading-3-quarters","cloud-upload","star","heart","environment","eye","camera","save","team","solution","phone","filter","exception","export","customer-service","qrcode","scan","like","dislike","message","pay-circle","calculator","pushpin","bulb","select","switcher","rocket","bell","disconnect","database","compass","barcode","hourglass","key","flag","layout","printer","sound","usb","skin","tool","sync","wifi","car","schedule","user-add","user-delete","usergroup-add","usergroup-delete","man","woman","shop","gift","idcard","medicine-box","red-envelope","coffee","copyright","trademark","safety","wallet","bank","trophy","contacts","global","shake","api","fork","dashboard","table","profile","alert","audit","branches","build","border","crown","experiment","fire","money-collect","property-safety","read","reconciliation","rest","security-scan","insurance","interation","safety-certificate","project","thunderbolt","block","cluster","deployment-unit","dollar","euro","pound","file-done","file-exclamation","file-protect","file-search","file-sync","gateway","gold","robot","shopping"],d=["android","apple","windows","ie","chrome","github","aliwangwang","dingding","weibo-square","weibo-circle","taobao-circle","html5","weibo","twitter","wechat","youtube","alipay-circle","taobao","skype","qq","medium-workmark","gitlab","medium","linkedin","google-plus","dropbox","facebook","codepen","amazon","google","codepen-circle","alipay","ant-design","aliyun","zhihu","slack","slack-square","behance","behance-square","dribbble","dribbble-square","instagram","yuque","alibaba","yahoo"],u={name:"Icons",props:{iconChooseVisible:{default:!1}},data:function(){return{icons:{directionIcons:l,suggestionIcons:i,editIcons:o,dataIcons:n,webIcons:c,logoIcons:d},choosedIcon:"",activeIndex:""}},computed:{show:{get:function(){return this.iconChooseVisible},set:function(){}}},methods:{reset:function(){this.activeIndex=""},chooseIcon:function(e){this.activeIndex=e,this.choosedIcon=e,this.$message.success("选中 ".concat(e))},ok:function(){""!==this.choosedIcon?(this.reset(),this.$emit("choose",this.choosedIcon)):this.$message.warning("尚未选择任何图标")},cancel:function(){this.reset(),this.$emit("close")}}},m=u,p=(a("3ab1"),a("2877")),h=Object(p["a"])(m,s,r,!1,null,"ea6fbdbc",null);t["default"]=h.exports},"17d0":function(e,t,a){},"1b2f":function(e,t,a){},"1be76":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[this.departId?[a("a-spin",{attrs:{spinning:e.loading}},[a("a-form",[a("a-form-item",{attrs:{label:"所拥有的权限"}},[a("a-tree",{staticStyle:{height:"500px",overflow:"auto"},attrs:{checkable:"",checkedKeys:e.checkedKeys,treeData:e.treeData,selectedKeys:e.selectedKeys,expandedKeys:e.expandedKeysss,checkStrictly:e.checkStrictly},on:{check:e.onCheck,expand:e.onExpand,select:e.onTreeNodeSelect},scopedSlots:e._u([{key:"hasDatarule",fn:function(t){var s=t.slotTitle,r=t.ruleFlag;return a("span",{},[e._v("\n              "+e._s(s)+"\n              "),r?a("a-icon",{staticStyle:{"margin-left":"5px",color:"red"},attrs:{type:"align-left"}}):e._e()],1)}}],null,!1,3403517078)})],1)],1)],1),a("div",{staticClass:"anty-form-btn"},[a("a-dropdown",{staticStyle:{float:"left"},attrs:{trigger:["click"],placement:"topCenter"}},[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"3",on:{click:e.checkALL}},[e._v("全部勾选")]),a("a-menu-item",{key:"4",on:{click:e.cancelCheckALL}},[e._v("取消全选")]),a("a-menu-item",{key:"5",on:{click:e.expandAll}},[e._v("展开所有")]),a("a-menu-item",{key:"6",on:{click:e.closeAll}},[e._v("合并所有")])],1),a("a-button",[e._v("\n          树操作 "),a("a-icon",{attrs:{type:"up"}})],1)],1),a("a-button",{staticStyle:{float:"right"},attrs:{type:"primary",htmlType:"button",icon:"form"},on:{click:e.handleSubmit}},[e._v("保存")])],1)]:a("a-card",{staticStyle:{height:"200px"},attrs:{bordered:!1}},[a("a-empty",[a("span",{attrs:{slot:"description"},slot:"description"},[e._v(" 请先选择一个部门! ")])])],1),a("depart-datarule-modal",{ref:"datarule"})],2)},r=[],l=a("4ec3"),i=a("6675");function o(e,t){var a;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(a=d(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var s=0,r=function(){};return{s:r,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,i=!0,o=!1;return{s:function(){a=e[Symbol.iterator]()},n:function(){var e=a.next();return i=e.done,e},e:function(e){o=!0,l=e},f:function(){try{i||null==a.return||a.return()}finally{if(o)throw l}}}}function n(e){return m(e)||u(e)||d(e)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function m(e){if(Array.isArray(e))return p(e)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}var h={name:"DepartAuthModal",components:{DepartDataruleModal:i["default"]},data:function(){return{departId:"",treeData:[],defaultCheckedKeys:[],checkedKeys:[],halfCheckedKeys:[],expandedKeysss:[],allTreeKeys:[],autoExpandParent:!0,checkStrictly:!1,title:"部门权限配置",visible:!1,loading:!1,selectedKeys:[]}},methods:{onTreeNodeSelect:function(e){e&&e.length>0&&(this.selectedKeys=e),this.$refs.datarule.show(this.selectedKeys[0],this.departId)},onCheck:function(e,t){var a=t.halfCheckedKeys;this.checkedKeys=e,this.halfCheckedKeys=a},show:function(e){this.departId=e,this.loadData()},close:function(){this.reset(),this.$emit("close"),this.visible=!1},onExpand:function(e){this.expandedKeysss=e,this.autoExpandParent=!1},reset:function(){this.expandedKeysss=[],this.checkedKeys=[],this.defaultCheckedKeys=[],this.loading=!1},expandAll:function(){this.expandedKeysss=this.allTreeKeys},closeAll:function(){this.expandedKeysss=[]},checkALL:function(){this.checkedKeys=this.allTreeKeys},cancelCheckALL:function(){this.checkedKeys=[]},handleCancel:function(){this.close()},handleSubmit:function(){var e=this;e.departId||this.$message.warning("请点击选择一个部门!");var t=[].concat(n(e.checkedKeys),n(e.halfCheckedKeys)),a=t.join(","),s={departId:e.departId,permissionIds:a,lastpermissionIds:e.defaultCheckedKeys.join(",")};e.loading=!0,Object(l["R"])(s).then((function(t){t.success?(e.$message.success(t.message),e.loading=!1,e.loadData()):(e.$message.error(t.message),e.loading=!1)}))},convertTreeListToKeyLeafPairs:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=o(e);try{for(s.s();!(t=s.n()).done;){var r=t.value,l=r.key,i=r.isLeaf,n=r.children;a.push({key:l,isLeaf:i}),n&&n.length>0&&this.convertTreeListToKeyLeafPairs(n,a)}}catch(c){s.e(c)}finally{s.f()}return a},emptyCurrForm:function(){this.form.resetFields()},loadData:function(){var e=this;this.loading=!0,Object(l["N"])().then((function(t){e.treeData=t.result.treeList,e.allTreeKeys=t.result.ids;var a=e.convertTreeListToKeyLeafPairs(e.treeData);Object(l["C"])({departId:e.departId}).then((function(t){var s=n(t.result).filter((function(e){var t=a.filter((function(t){return t.key===e}))[0];return t&&t.isLeaf})),r=n(t.result).filter((function(e){var t=a.filter((function(t){return t.key===e}))[0];return t&&!t.isLeaf}));e.checkedKeys=n(s),e.halfCheckedKeys=n(r),e.defaultCheckedKeys=[].concat(n(r),n(s)),e.expandedKeysss=e.allTreeKeys,e.loading=!1}))}))}}},f=h,b=a("2877"),v=Object(b["a"])(f,s,r,!1,null,"359fc2db",null);t["default"]=v.exports},"1d46":function(e,t,a){"use strict";var s=a("f266"),r=a.n(s);r.a},"1f70":function(e,t,a){},2010:function(e,t,a){},2285:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:"数据规则/按钮权限配置",width:"365",closable:!1,visible:e.visible},on:{close:e.onClose}},[a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"数据规则"}},[e.dataruleList.length>0?a("a-checkbox-group",{model:{value:e.dataruleChecked,callback:function(t){e.dataruleChecked=t},expression:"dataruleChecked"}},[a("a-row",[e._l(e.dataruleList,(function(t,s){return a("a-col",{key:"dr"+s,attrs:{span:24}},[a("a-checkbox",{attrs:{value:t.id}},[e._v(e._s(t.ruleName))])],1)})),a("a-col",{attrs:{span:24}},[a("div",{staticStyle:{width:"100%","margin-top":"15px"}},[a("a-button",{attrs:{type:"primary",size:"small",icon:"save"},on:{click:e.saveDataruleForRole}},[e._v("点击保存")])],1)])],2)],1):a("div",[a("h3",[e._v("无配置信息!")])])],1)],1)],1)},r=[],l=a("290c"),i=a("da05"),o=a("0fea"),n={name:"RoleDataruleModal",components:{ACol:i["b"],ARow:l["a"]},data:function(){return{functionId:"",roleId:"",visible:!1,tabList:[{key:"1",tab:"数据规则"},{key:"2",tab:"按钮权限"}],activeTabKey:"1",url:{datarule:"/sys/role/datarule"},dataruleList:[],dataruleChecked:[]}},methods:{loadData:function(){var e=this;Object(o["c"])("".concat(this.url.datarule,"/").concat(this.functionId,"/").concat(this.roleId)).then((function(t){if(t.success){e.dataruleList=t.result.datarule;var a=t.result.drChecked;a&&(e.dataruleChecked=a.split(","))}}))},saveDataruleForRole:function(){var e=this;this.dataruleChecked&&0!=this.dataruleChecked.length||this.$message.warning("请注意，现未勾选任何数据权限!");var t={permissionId:this.functionId,roleId:this.roleId,dataRuleIds:this.dataruleChecked.join(",")};Object(o["i"])(this.url.datarule,t).then((function(t){t.success?e.$message.success(t.message):e.$message.error(t.message)}))},show:function(e,t){this.onReset(),this.functionId=e,this.roleId=t,this.visible=!0,this.loadData()},onClose:function(){this.visible=!1,this.onReset()},onTabChange:function(e){this.activeTabKey=e},onReset:function(){this.functionId="",this.roleId="",this.dataruleList=[],this.dataruleChecked=[]}}},c=n,d=a("2877"),u=Object(d["a"])(c,s,r,!1,null,"5fa38486",null);t["default"]=u.exports},"24b9":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:10}},[a("a-col",{attrs:{md:10,sm:12}},[a("a-form-item",{staticStyle:{"margin-left":"8px"},attrs:{label:"部门角色名称"}},[a("a-input",{attrs:{placeholder:"请输入部门角色"},model:{value:e.queryParam.roleName,callback:function(t){e.$set(e.queryParam,"roleName",t)},expression:"queryParam.roleName"}})],1)],1),a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-button",{staticStyle:{"margin-left":"18px"},attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"table-operator",attrs:{md:24,sm:24}},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新建部门角色")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作 "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v("\n        "+e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,s){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(s)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("\n            更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{on:{click:function(t){return e.handlePerssion(s)}}},[e._v("授权")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定删除吗?"},on:{confirm:function(){return e.handleDelete(s.id)}}},[a("a",[e._v("删除")])])],1)],1)],1)],1)}}])})],1),a("sys-depart-role-modal",{ref:"modalForm",on:{ok:e.modalFormOk}}),a("dept-role-auth-modal",{ref:"modalDeptRole"})],1)},r=[],l=a("b65a"),i=a("0fea"),o=a("a119"),n=a("dc4b"),c={name:"DeptRoleInfo",components:{DeptRoleAuthModal:n["default"],SysDepartRoleModal:o["default"]},mixins:[l["a"]],data:function(){return{description:"部门角色信息",currentDeptId:"",columns:[{title:"部门角色名称",align:"center",dataIndex:"roleName"},{title:"部门角色编码",align:"center",dataIndex:"roleCode"},{title:"部门",align:"center",dataIndex:"departId_dictText"},{title:"备注",align:"center",dataIndex:"description"},{title:"操作",dataIndex:"action",scopedSlots:{customRender:"action"},align:"center",width:170}],url:{list:"/sys/sysDepartRole/list",delete:"/sys/sysDepartRole/delete",deleteBatch:"/sys/sysDepartRole/deleteBatch"}}},created:function(){},methods:{searchReset:function(){this.queryParam={},this.loadData(1)},loadData:function(e){var t=this;if(this.url.list){1===e&&(this.ipagination.current=1);var a=this.getQueryParams();a.deptId=this.currentDeptId,Object(i["c"])(this.url.list,a).then((function(e){e.success&&e.result&&(t.dataSource=e.result.records,t.ipagination.total=e.result.total)}))}else this.$message.error("请设置url.list属性!")},open:function(e){this.currentDeptId=e.id,this.loadData(1)},clearList:function(){this.currentDeptId="",this.dataSource=[]},hasSelectDept:function(){return""!=this.currentDeptId||(this.$message.error("请选择一个部门!"),!1)},handleEdit:function(e){this.$refs.modalForm.title="编辑",this.$refs.modalForm.departDisabled=!0,this.$refs.modalForm.disableSubmit=!1,this.$refs.modalForm.edit(e,e.departId)},handleAdd:function(){""==this.currentDeptId?this.$message.error("请选择一个部门!"):(this.$refs.modalForm.departDisabled=!0,this.$refs.modalForm.add(this.currentDeptId),this.$refs.modalForm.title="新增")},handlePerssion:function(e){this.$refs.modalDeptRole.show(e.id,e.departId)}}},d=c,u=a("2877"),m=Object(u["a"])(d,s,r,!1,null,"37090947",null);t["default"]=m.exports},"25fd":function(e,t,a){},"2a70":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{staticStyle:{top:"5%",height:"85%","overflow-y":"hidden"},attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭",wrapClassName:"ant-modal-cust-warp"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-form-model-item",{attrs:{label:"角色编码",required:"",prop:"roleCode"}},[a("a-input",{attrs:{disabled:e.roleDisabled,placeholder:"请输入角色编码"},model:{value:e.model.roleCode,callback:function(t){e.$set(e.model,"roleCode",t)},expression:"model.roleCode"}})],1),a("a-form-model-item",{attrs:{label:"角色名称",required:"",prop:"roleName"}},[a("a-input",{attrs:{placeholder:"请输入角色名称"},model:{value:e.model.roleName,callback:function(t){e.$set(e.model,"roleName",t)},expression:"model.roleName"}})],1),a("a-form-model-item",{attrs:{label:"描述",prop:"description"}},[a("a-textarea",{attrs:{rows:5,placeholder:"请输入角色描述"},model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1)],1)],1)],1)},r=[],l=a("4ec3"),i={name:"RoleModal",data:function(){return{title:"操作",visible:!1,roleDisabled:!1,model:{},layout:{labelCol:{span:3},wrapperCol:{span:14}},confirmLoading:!1,validatorRules:{roleName:[{required:!0,message:"请输入角色名称!"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],roleCode:[{required:!0,message:"请输入角色名称!"},{min:0,max:64,message:"长度不超过 64 个字符",trigger:"blur"},{validator:this.validateRoleCode}],description:[{min:0,max:126,message:"长度不超过 126 个字符",trigger:"blur"}]}}},created:function(){this.modelDefault=JSON.parse(JSON.stringify(this.model))},methods:{add:function(){this.edit(this.modelDefault)},edit:function(e){this.model=Object.assign({},e),this.visible=!0,this.model.id?this.roleDisabled=!0:this.roleDisabled=!1},close:function(){this.$refs.form.clearValidate(),this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;var s;t.confirmLoading=!0,s=e.model.id?Object(l["q"])(e.model):Object(l["d"])(e.model),s.then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},validateRoleCode:function(e,t,a){if(/[\u4E00-\u9FA5]/g.test(t))a("角色编码不可输入汉字!");else{var s={tableName:"sys_role",fieldName:"role_code",fieldVal:t,dataId:this.model.id};Object(l["m"])(s).then((function(e){e.success?a():a(e.message)}))}}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,"4cfa5d06",null);t["default"]=c.exports},"2a85":function(e,t,a){"use strict";var s=a("25fd"),r=a.n(s);r.a},"30a6":function(e,t,a){"use strict";var s=a("7437"),r=a.n(s);r.a},"34d2":function(e,t,a){},3864:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticStyle:{height:"100%"},attrs:{loading:e.cardLoading,bordered:!1}},[a("a-spin",{attrs:{spinning:e.loading}},[a("a-input-search",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{placeholder:"输入机构名称查询...",enterButton:""},on:{search:e.handleSearch}}),a("a-tree",{attrs:{showLine:"",checkStrictly:"",expandedKeys:e.expandedKeys,selectedKeys:e.selectedKeys,dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.treeDataSource},on:{"update:expandedKeys":function(t){e.expandedKeys=t},"update:expanded-keys":function(t){e.expandedKeys=t},select:e.handleTreeSelect}})],1)],1)},r=[],l=a("4ec3"),i={name:"AddressListLeft",props:["value"],data:function(){return{cardLoading:!0,loading:!1,treeDataSource:[],selectedKeys:[],expandedKeys:[]}},created:function(){this.queryTreeData()},methods:{queryTreeData:function(e){this.commonRequestThen(Object(l["D"])({departName:e||void 0}))},handleSearch:function(e){e?this.commonRequestThen(Object(l["U"])({keyWord:e})):this.queryTreeData()},handleTreeSelect:function(e,t){if(e.length>0&&this.selectedKeys[0]!==e[0]){this.selectedKeys=[e[0]];var a=t.node.dataRef.orgCode;this.emitInput(a)}},emitInput:function(e){this.$emit("input",e)},commonRequestThen:function(e){var t=this;this.loading=!0,e.then((function(e){e.success?t.treeDataSource=e.result:t.$message.warn(e.message)})).finally((function(){t.loading=!1,t.cardLoading=!1}))}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,"1d28f146",null);t["default"]=c.exports},"39f6":function(e,t,a){"use strict";var s=a("1b2f"),r=a.n(s);r.a},"3ab1":function(e,t,a){"use strict";var s=a("b940"),r=a.n(s);r.a},"3cf4":function(e,t,a){"use strict";var s=a("ee00"),r=a.n(s);r.a},"418f":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{staticStyle:{height:"100%",overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:e.drawerWidth,placement:"right",closable:!0,visible:e.visible},on:{close:e.handleCancel}},[a("template",{slot:"title"},[a("div",{staticStyle:{width:"100%"}},[a("span",[e._v(e._s(e.title))]),a("span",{staticStyle:{display:"inline-block",width:"calc(100% - 51px)","padding-right":"10px","text-align":"right"}},[a("a-button",{staticStyle:{height:"20px",width:"20px",border:"0px"},attrs:{icon:"appstore"},on:{click:e.toggleScreen}})],1)])]),a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{label:"用户账号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"username"}},[a("a-input",{attrs:{placeholder:"请输入用户账号",readOnly:!!e.model.id},model:{value:e.model.username,callback:function(t){e.$set(e.model,"username",t)},expression:"model.username"}})],1),e.model.id?e._e():[a("a-form-model-item",{attrs:{label:"登录密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"password"}},[a("a-input",{attrs:{type:"password",placeholder:"请输入登录密码"},model:{value:e.model.password,callback:function(t){e.$set(e.model,"password",t)},expression:"model.password"}})],1),a("a-form-model-item",{attrs:{label:"确认密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"confirmpassword"}},[a("a-input",{attrs:{type:"password",placeholder:"请重新输入登录密码"},on:{blur:e.handleConfirmBlur},model:{value:e.model.confirmpassword,callback:function(t){e.$set(e.model,"confirmpassword",t)},expression:"model.confirmpassword"}})],1)],a("a-form-model-item",{attrs:{label:"用户姓名",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"realname"}},[a("a-input",{attrs:{placeholder:"请输入用户姓名"},model:{value:e.model.realname,callback:function(t){e.$set(e.model,"realname",t)},expression:"model.realname"}})],1),a("a-form-model-item",{attrs:{label:"工号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"workNo"}},[a("a-input",{attrs:{placeholder:"请输入工号"},model:{value:e.model.workNo,callback:function(t){e.$set(e.model,"workNo",t)},expression:"model.workNo"}})],1),a("a-form-model-item",{attrs:{label:"职务",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-select-position",{attrs:{placeholder:"请选择职务",multiple:!1},model:{value:e.model.post,callback:function(t){e.$set(e.model,"post",t)},expression:"model.post"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.roleDisabled,expression:"!roleDisabled"}],attrs:{label:"角色分配",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-multi-select-tag",{attrs:{disabled:e.disableSubmit,options:e.rolesOptions,placeholder:"请选择角色"},model:{value:e.model.selectedroles,callback:function(t){e.$set(e.model,"selectedroles",t)},expression:"model.selectedroles"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.departDisabled,expression:"!departDisabled"}],attrs:{label:"部门分配",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-select-depart",{attrs:{multi:!0,backDepart:!0,treeOpera:!0},on:{back:e.backDepartInfo},model:{value:e.model.selecteddeparts,callback:function(t){e.$set(e.model,"selecteddeparts",t)},expression:"model.selecteddeparts"}},[e._v(">")])],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.departDisabled,expression:"!departDisabled"}],attrs:{label:"租户分配",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-multi-select-tag",{attrs:{disabled:e.disableSubmit,options:e.tenantsOptions,placeholder:"请选择租户"},model:{value:e.model.relTenantIds,callback:function(t){e.$set(e.model,"relTenantIds",t)},expression:"model.relTenantIds"}})],1),a("a-form-model-item",{attrs:{label:"身份",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{on:{change:e.identityChange},model:{value:e.model.userIdentity,callback:function(t){e.$set(e.model,"userIdentity",t)},expression:"model.userIdentity"}},[a("a-radio",{attrs:{value:1}},[e._v("普通用户")]),a("a-radio",{attrs:{value:2}},[e._v("上级")])],1)],1),1==e.departIdShow?a("a-form-model-item",{attrs:{label:"负责部门",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-multi-select-tag",{attrs:{disabled:e.disableSubmit,options:e.nextDepartOptions,placeholder:"请选择负责部门"},model:{value:e.model.departIds,callback:function(t){e.$set(e.model,"departIds",t)},expression:"model.departIds"}})],1):e._e(),a("a-form-model-item",{attrs:{label:"头像",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-image-upload",{staticClass:"avatar-uploader",attrs:{text:"上传"},model:{value:e.model.avatar,callback:function(t){e.$set(e.model,"avatar",t)},expression:"model.avatar"}})],1),a("a-form-model-item",{attrs:{label:"生日",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择生日",format:e.dateFormat,getCalendarContainer:function(e){return e.parentNode}},model:{value:e.model.birthday,callback:function(t){e.$set(e.model,"birthday",t)},expression:"model.birthday"}})],1),a("a-form-model-item",{attrs:{label:"性别",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-select",{attrs:{placeholder:"请选择性别",getPopupContainer:function(e){return e.parentNode}},model:{value:e.model.sex,callback:function(t){e.$set(e.model,"sex",t)},expression:"model.sex"}},[a("a-select-option",{attrs:{value:1}},[e._v("男")]),a("a-select-option",{attrs:{value:2}},[e._v("女")])],1)],1),a("a-form-model-item",{attrs:{label:"邮箱",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"email"}},[a("a-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.model.email,callback:function(t){e.$set(e.model,"email",t)},expression:"model.email"}})],1),a("a-form-model-item",{attrs:{label:"手机号码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"phone"}},[a("a-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.model.phone,callback:function(t){e.$set(e.model,"phone",t)},expression:"model.phone"}})],1),a("a-form-model-item",{attrs:{label:"座机",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"telephone"}},[a("a-input",{attrs:{placeholder:"请输入座机"},model:{value:e.model.telephone,callback:function(t){e.$set(e.model,"telephone",t)},expression:"model.telephone"}})],1),a("a-form-model-item",{attrs:{label:"工作流引擎",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否同步工作流引擎",type:"radio",dictCode:"activiti_sync"},model:{value:e.model.activitiSync,callback:function(t){e.$set(e.model,"activitiSync",t)},expression:"model.activitiSync"}})],1),a("a-form-model-item",{attrs:{label:"是否是作者",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择是否是作者",type:"radio",dictCode:"yes_no"},model:{value:e.model.isAuthor,callback:function(t){e.$set(e.model,"isAuthor",t)},expression:"model.isAuthor"}})],1)],2)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.disableSubmit,expression:"!disableSubmit"}],staticClass:"drawer-bootom-button"},[a("a-popconfirm",{attrs:{title:"确定放弃编辑？",okText:"确定",cancelText:"取消"},on:{confirm:e.handleCancel}},[a("a-button",{staticStyle:{"margin-right":".8rem"}},[e._v("取消")])],1),a("a-button",{attrs:{type:"primary",loading:e.confirmLoading},on:{click:e.handleSubmit}},[e._v("提交")])],1)],2)},r=[],l=a("c1df"),i=a.n(l),o=a("2b0e"),n=a("9fb0"),c=a("0fea"),d=a("4ec3"),u=a("c82c"),m={name:"UserModal",components:{},data:function(){return{departDisabled:!1,roleDisabled:!1,modalWidth:800,drawerWidth:700,modaltoggleFlag:!0,confirmDirty:!1,userId:"",disableSubmit:!1,dateFormat:"YYYY-MM-DD",validatorRules:{username:[{required:!0,message:"请输入用户账号!"},{validator:this.validateUsername}],password:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!"},{validator:this.validateToNextPassword,trigger:"change"}],confirmpassword:[{required:!0,message:"请重新输入登录密码!"},{validator:this.compareToFirstPassword}],realname:[{required:!0,message:"请输入用户名称!"}],phone:[{required:!0,message:"请输入手机号!"},{validator:this.validatePhone}],email:[{validator:this.validateEmail}],roles:{},workNo:[{required:!0,message:"请输入工号"},{validator:this.validateWorkNo}],telephone:[{pattern:/^0\d{2,3}-[1-9]\d{6,7}$/,message:"请输入正确的座机号码"}]},departIdShow:!1,title:"操作",visible:!1,model:{isAuthor:0},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},uploadLoading:!1,confirmLoading:!1,headers:{},url:{fileUpload:window._CONFIG["domianURL"]+"/sys/common/upload",userWithDepart:"/sys/user/userDepartList",userId:"/sys/user/generateUserId",syncUserByUserName:"/act/process/extActProcess/doSyncUserByUserName",queryTenantList:"/sys/tenant/queryList"},tenantsOptions:[],rolesOptions:[],nextDepartOptions:[]}},created:function(){var e=o["default"].ls.get(n["a"]);this.headers={"X-Access-Token":e},this.initRoleList(),this.initTenantList()},computed:{uploadAction:function(){return this.url.fileUpload}},methods:{add:function(){this.refresh(),this.edit({activitiSync:"1",userIdentity:1,isAuthor:0})},edit:function(e){var t=this;t.visible=!0,this.resetScreenSize(),t.userId=e.id,t.model=Object.assign({},{selectedroles:"",selecteddeparts:""},e),void 0!==t.model.isAuthor&&null!==t.model.isAuthor||(t.model.isAuthor=0),2==this.model.userIdentity?this.departIdShow=!0:this.departIdShow=!1,e.hasOwnProperty("id")&&(t.getUserRoles(e.id),t.getUserDeparts(e.id))},isDisabledAuth:function(e){return Object(u["a"])(e)},toggleScreen:function(){this.modaltoggleFlag?this.modalWidth=window.innerWidth:this.modalWidth=800,this.modaltoggleFlag=!this.modaltoggleFlag},resetScreenSize:function(){var e=document.body.clientWidth;this.drawerWidth=e<500?e:700},initTenantList:function(){var e=this;Object(c["c"])(this.url.queryTenantList).then((function(t){t.success&&(e.tenantsOptions=t.result.map((function(e,t,a){var s={label:e.name,value:e.id+""};return s})))}))},initRoleList:function(){var e=this;Object(d["Q"])().then((function(t){t.success&&(e.rolesOptions=t.result.map((function(e,t,a){var s={label:e.roleName,value:e.id};return s})))}))},getUserRoles:function(e){var t=this;Object(d["P"])({userid:e}).then((function(e){e.success&&(t.model.selectedroles=e.result.join(","))}))},getUserDeparts:function(e){var t=this;Object(c["c"])(t.url.userWithDepart,{userId:e}).then((function(e){if(e.success){for(var a=[],s=[],r=0;r<e.result.length;r++)s.push(e.result[r].key),a.push({value:e.result[r].key,label:e.result[r].title});t.model.selecteddeparts=s.join(","),t.nextDepartOptions=a}}))},backDepartInfo:function(e){this.model.departIds=this.model.selecteddeparts,this.nextDepartOptions=e.map((function(e,t,a){var s={label:e.text,value:e.value+""};return s}))},refresh:function(){this.userId="",this.nextDepartOptions=[],this.departIdShow=!1},close:function(){this.$emit("close"),this.visible=!1,this.disableSubmit=!1,this.nextDepartOptions=[],this.departIdShow=!1,this.$refs.form.resetFields()},moment:i.a,handleSubmit:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;var s;t.confirmLoading=!0,2!==e.model.userIdentity&&(e.model.departIds=""),e.model.id?s=Object(d["r"])(e.model):(e.model.id=e.userId,s=Object(d["e"])(e.model)),s.then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},validateToNextPassword:function(e,t,a){var s=this.model.confirmpassword;t&&s&&t!==s&&a("两次输入的密码不一样！"),t&&this.confirmDirty&&this.$refs.form.validateField(["confirmpassword"]),a()},compareToFirstPassword:function(e,t,a){t&&t!==this.model.password?a("两次输入的密码不一样！"):a()},validatePhone:function(e,t,a){if(t)if(new RegExp(/^1[3|4|5|7|8|9][0-9]\d{8}$/).test(t)){var s={tableName:"sys_user",fieldName:"phone",fieldVal:t,dataId:this.userId};Object(d["m"])(s).then((function(e){e.success?a():a("手机号已存在!")}))}else a("请输入正确格式的手机号码!");else a()},validateEmail:function(e,t,a){if(t)if(new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/).test(t)){var s={tableName:"sys_user",fieldName:"email",fieldVal:t,dataId:this.userId};Object(d["m"])(s).then((function(e){e.success?a():a("邮箱已存在!")}))}else a("请输入正确格式的邮箱!");else a()},validateUsername:function(e,t,a){var s={tableName:"sys_user",fieldName:"username",fieldVal:t,dataId:this.userId};Object(d["m"])(s).then((function(e){e.success?a():a("用户名已存在!")}))},validateWorkNo:function(e,t,a){var s={tableName:"sys_user",fieldName:"work_no",fieldVal:t,dataId:this.userId};Object(d["m"])(s).then((function(e){e.success?a():a("工号已存在!")}))},handleConfirmBlur:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t},beforeUpload:function(e){var t=e.type;if(t.indexOf("image")<0)return this.$message.warning("请上传图片"),!1},identityChange:function(e){1===e.target.value?this.departIdShow=!1:this.departIdShow=!0}}},p=m,h=(a("3cf4"),a("2877")),f=Object(h["a"])(p,s,r,!1,null,"6cec97f1",null);t["default"]=f.exports},4647:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:900,visible:e.visible,confirmLoading:e.confirmLoading,okButtonProps:{props:{disabled:e.disabled}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-row",{staticStyle:{width:"100%"}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"titile",label:"标题"}},[a("a-input",{attrs:{placeholder:"请输入标题",readOnly:e.disableSubmit},model:{value:e.model.titile,callback:function(t){e.$set(e.model,"titile",t)},expression:"model.titile"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"msgCategory",label:"消息类型"}},[a("a-select",{attrs:{placeholder:"请选择消息类型",disabled:e.disableSubmit,getPopupContainer:function(e){return e.parentNode}},model:{value:e.model.msgCategory,callback:function(t){e.$set(e.model,"msgCategory",t)},expression:"model.msgCategory"}},[a("a-select-option",{attrs:{value:"1"}},[e._v("通知公告")]),a("a-select-option",{attrs:{value:"2"}},[e._v("系统消息")])],1)],1)],1)],1),a("a-row",{staticStyle:{width:"100%"}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"startTime",label:"开始时间:"}},[a("j-date",{staticStyle:{width:"100%"},attrs:{getCalendarContainer:function(e){return e.parentNode},placeholder:"请选择开始时间",showTime:"",dateFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.startTime,callback:function(t){e.$set(e.model,"startTime",t)},expression:"model.startTime"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{staticClass:"endTime",attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"endTime",label:"结束时间"}},[a("j-date",{staticStyle:{width:"100%"},attrs:{getCalendarContainer:function(e){return e.parentNode},placeholder:"请选择结束时间",showTime:"",dateFormat:"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.endTime,callback:function(t){e.$set(e.model,"endTime",t)},expression:"model.endTime"}})],1)],1)],1),a("a-row",{staticStyle:{width:"100%"}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"优先级"}},[a("a-select",{attrs:{placeholder:"请选择优先级",disabled:e.disableSubmit,getPopupContainer:function(e){return e.parentNode}},model:{value:e.model.priority,callback:function(t){e.$set(e.model,"priority",t)},expression:"model.priority"}},[a("a-select-option",{attrs:{value:"L"}},[e._v("低")]),a("a-select-option",{attrs:{value:"M"}},[e._v("中")]),a("a-select-option",{attrs:{value:"H"}},[e._v("高")])],1)],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"msgType",label:"通告类型"}},[a("a-select",{attrs:{placeholder:"请选择通告类型",disabled:e.disableSubmit,getPopupContainer:function(e){return e.parentNode}},on:{change:e.chooseMsgType},model:{value:e.model.msgType,callback:function(t){e.$set(e.model,"msgType",t)},expression:"model.msgType"}},[a("a-select-option",{attrs:{value:"USER"}},[e._v("指定用户")]),a("a-select-option",{attrs:{value:"ALL"}},[e._v("全体用户")])],1)],1)],1)],1),a("a-row",{staticStyle:{width:"100%"}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"msgAbstract",label:"摘要"}},[a("a-textarea",{attrs:{placeholder:"请输入摘要"},model:{value:e.model.msgAbstract,callback:function(t){e.$set(e.model,"msgAbstract",t)},expression:"model.msgAbstract"}})],1)],1),a("a-col",{attrs:{span:12}},[e.userType?a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"指定用户"}},[a("a-select",{attrs:{mode:"multiple",placeholder:"请选择用户",labelInValue:!0},on:{dropdownVisibleChange:e.selectUserIds,change:e.handleChange},model:{value:e.selectedUser,callback:function(t){e.selectedUser=t},expression:"selectedUser"}})],1):e._e()],1)],1),a("a-row",{staticStyle:{width:"100%"}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{staticClass:"j-field-content",attrs:{labelCol:e.labelColX1,wrapperCol:e.wrapperColX1,label:"内容"}},[a("j-editor",{model:{value:e.model.msgContent,callback:function(t){e.$set(e.model,"msgContent",t)},expression:"model.msgContent"}})],1)],1)],1)],1)],1),a("select-user-list-modal",{ref:"UserListModal",on:{choseUser:e.choseUser}})],1)},r=[],l=a("0fea"),i=(a("88bc"),a("2dab")),o=a("a061"),n=a("ed2a"),c=a("c1df"),d=a.n(c),u={components:{JEditor:o["default"],JDate:i["default"],SelectUserListModal:n["default"]},name:"SysAnnouncementModal",data:function(){return{title:"操作",visible:!1,disableSubmit:!1,model:{},labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:18}},labelColX1:{xs:{span:24},sm:{span:3}},wrapperColX1:{xs:{span:24},sm:{span:21}},confirmLoading:!1,validatorRules:{titile:[{required:!0,message:"请输入标题!"}],msgCategory:[{required:!0,message:"请选择消息类型!"}],msgType:[{required:!0,message:"请选择通告对象类型!"}],endTime:[{required:!0,message:"请选择结束时间!"},{validator:this.endTimeValidate}],startTime:[{required:!0,message:"请选择开始时间!"},{validator:this.startTimeValidate}],msgAbstract:[{required:!0,message:"请输入摘要!"}]},url:{queryByIds:"/sys/user/queryByIds",add:"/sys/annountCement/add",edit:"/sys/annountCement/edit"},userType:!1,userIds:[],selectedUser:[],disabled:!1,msgContent:"",userList:[]}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){this.model={},this.disable=!1,this.visible=!0,this.getUser(e)},getUser:function(e){var t=this;this.model=Object.assign({},e),e&&"USER"===e.msgType&&(this.userType=!0,this.userIds=e.userIds,Object(l["c"])(this.url.queryByIds,{userIds:this.userIds}).then((function(e){if(e.success){for(var a=[],s=0;s<e.result.length;s++){var r={};r.label=e.result[s].realname,r.key=e.result[s].id,a.push(r)}t.selectedUser=a,t.$refs.UserListModal.edit(e.result,t.userIds)}})))},close:function(){this.$emit("close"),this.selectedUser=[],this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;!this.userType||null!=this.userIds&&this.userIds.length>0?this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),e.userType&&(e.model.userIds=e.userIds),Object(l["h"])(s,e.model,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.resetUser()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))})):this.$message.warning("指定用户不能为空！")},handleCancel:function(){this.visible=!1,this.$emit("close"),this.$refs.form.resetFields(),this.resetUser()},resetUser:function(){this.userType=!1,this.userIds=[],this.selectedUser=[],this.disabled=!1,this.$refs.UserListModal.edit(null,null)},selectUserIds:function(){this.$refs.UserListModal.add(this.selectedUser,this.userIds)},chooseMsgType:function(e){"USER"==e?this.userType=!0:(this.userType=!1,this.selectedUser=[],this.userIds=[])},choseUser:function(e){this.selectedUser=[],this.userIds=[];for(var t=0;t<e.length;t++){var a={};a.label=e[t].realname,a.key=e[t].id,this.selectedUser.push(a),this.userIds+=e[t].id+","}},startTimeValidate:function(e,t,a){var s=this.model.endTime;t&&s?d()(t).isBefore(s)?a():a("开始时间需小于结束时间"):a()},endTimeValidate:function(e,t,a){var s=this.model.startTime;t&&s?d()(s).isBefore(t)?a():a("结束时间需大于开始时间"):a()},handleChange:function(e){if(e){this.userIds=[];for(var t=[],a=0;a<e.length;a++){var s={};s.id=e[a].key,s.realname=e[a].label,this.userIds+=e[a].key+",",t.push(s)}}this.$refs.UserListModal.edit(t,this.userIds)}}},m=u,p=a("2877"),h=Object(p["a"])(m,s,r,!1,null,"c43436e2",null);t["default"]=h.exports},"47fd":function(e,t,a){"use strict";var s=a("fe90"),r=a.n(s);r.a},4890:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("j-form-container",{attrs:{disabled:e.formDisabled}},[a("a-form-model",{ref:"form",attrs:{slot:"detail",model:e.model,rules:e.validatorRules},slot:"detail"},[a("a-row",[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"租户名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{attrs:{placeholder:"请输入租户名称"},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"租户编号",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"id"}},[a("a-input-number",{staticStyle:{width:"100%"},attrs:{min:1,placeholder:"请输入租户编号",disabled:e.disabledId},model:{value:e.model.id,callback:function(t){e.$set(e.model,"id",t)},expression:"model.id"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"开始时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-date",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择开始时间","show-time":!0,"date-format":"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.beginDate,callback:function(t){e.$set(e.model,"beginDate",t)},expression:"model.beginDate"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"结束时间",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("j-date",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择结束时间","show-time":!0,"date-format":"YYYY-MM-DD HH:mm:ss"},model:{value:e.model.endDate,callback:function(t){e.$set(e.model,"endDate",t)},expression:"model.endDate"}})],1)],1),a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"状态",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{attrs:{name:"tenantStatus"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-radio",{attrs:{value:1}},[e._v("正常")]),a("a-radio",{attrs:{value:0}},[e._v("冻结")])],1)],1)],1),e.showFlowSubmitButton?a("a-col",{staticStyle:{"text-align":"center"},attrs:{span:24}},[a("a-button",{on:{click:e.submitForm}},[e._v("提 交")])],1):e._e()],1)],1)],1)],1)},r=[],l=a("0fea"),i=(a("ca00"),a("c681")),o=a("2dab"),n=a("7b16"),c={name:"TenantForm",components:{JFormContainer:i["default"],JDate:o["default"],JDictSelectTag:n["default"]},props:{formData:{type:Object,default:function(){},required:!1},normal:{type:Boolean,default:!1,required:!1},disabled:{type:Boolean,default:!1,required:!1}},data:function(){return{model:{status:1},id:"",labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{id:[{required:!0,message:"请输入租户编号!"}]},url:{add:"/sys/tenant/add",edit:"/sys/tenant/edit",queryById:"/sys/tenant/queryById"}}},computed:{formDisabled:function(){return!1===this.normal?!1!==this.formData.disabled:this.disabled},disabledId:function(){return!!this.id},showFlowSubmitButton:function(){return!1===this.normal&&!1===this.formData.disabled}},created:function(){this.showFlowData()},methods:{show:function(e){this.model=e?Object.assign({},e):this.model,this.id=e?e.id:"",this.visible=!0},showFlowData:function(){var e=this;if(!1===this.normal){var t={id:this.formData.dataId};Object(l["c"])(this.url.queryById,t).then((function(t){t.success&&e.edit(t.result)}))}},submitForm:function(){var e=this,t=this;t.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),Object(l["h"])(s,e.model,r).then((function(a){a.success?(t.$message.success(a.message),t.$emit("ok")):("该编号已存在!"==a.message&&(e.model.id=""),t.$message.warning(a.message))})).finally((function(){t.confirmLoading=!1}))}))},popupCallback:function(e){this.model=Object.assign(this.model,e)}}},d=c,u=a("2877"),m=Object(u["a"])(d,s,r,!1,null,null,null);t["default"]=m.exports},"490e":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"heartbeat-test-page"},[a("a-card",{staticClass:"test-card",attrs:{title:"🔄 心跳API测试页面"}},[a("div",{staticClass:"test-controls"},[a("a-space",[a("a-button",{attrs:{type:"primary",loading:e.testing.basic},on:{click:e.runBasicTest}},[e._v("\n          基础心跳测试\n        ")]),a("a-button",{attrs:{type:"primary",loading:e.testing.smart},on:{click:e.runSmartTest}},[e._v("\n          智能心跳测试\n        ")]),a("a-button",{attrs:{type:"primary",loading:e.testing.batch},on:{click:e.runBatchTest}},[e._v("\n          批量操作测试\n        ")]),a("a-button",{attrs:{type:"primary",loading:e.testing.full},on:{click:e.runFullSuite}},[e._v("\n          完整测试套件\n        ")]),a("a-button",{on:{click:e.clearResults}},[e._v("清空结果")])],1)],1),e.testResults.length>0?a("div",{staticClass:"test-results"},[a("a-divider",[e._v("测试结果")]),e._l(e.testResults,(function(t,s){return a("div",{key:s,staticClass:"result-item"},[a("a-alert",{attrs:{type:t.success?"success":"error",message:t.title,description:t.description,"show-icon":"",closable:""}}),t.details?a("div",{staticClass:"result-details"},[a("pre",[e._v(e._s(JSON.stringify(t.details,null,2)))])]):e._e()],1)}))],2):e._e(),e.performanceData?a("div",{staticClass:"performance-monitor"},[a("a-divider",[e._v("性能监控")]),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"总测试数",value:e.performanceData.totalTests}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"成功率",value:e.performanceData.successRate,suffix:"%"}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"平均响应时间",value:e.performanceData.avgResponseTime,suffix:"ms"}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"最大响应时间",value:e.performanceData.maxResponseTime,suffix:"ms"}})],1)],1)],1):e._e(),e.heartbeatState?a("div",{staticClass:"heartbeat-status"},[a("a-divider",[e._v("心跳状态监控")]),a("a-descriptions",{attrs:{bordered:"",column:2}},[a("a-descriptions-item",{attrs:{label:"心跳状态"}},[a("a-tag",{attrs:{color:e.heartbeatState.isActive?"green":"red"}},[e._v("\n            "+e._s(e.heartbeatState.isActive?"活跃":"停止")+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"网络状态"}},[a("a-tag",{attrs:{color:e.heartbeatState.isOnline?"green":"red"}},[e._v("\n            "+e._s(e.heartbeatState.isOnline?"在线":"离线")+"\n          ")])],1),a("a-descriptions-item",{attrs:{label:"总发送次数"}},[e._v("\n          "+e._s(e.heartbeatStats.totalSent)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"成功次数"}},[e._v("\n          "+e._s(e.heartbeatStats.totalSuccess)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"失败次数"}},[e._v("\n          "+e._s(e.heartbeatStats.totalFailure)+"\n        ")]),a("a-descriptions-item",{attrs:{label:"平均响应时间"}},[e._v("\n          "+e._s(e.heartbeatStats.avgResponseTime)+"ms\n        ")])],1)],1):e._e(),a("div",{staticClass:"api-config"},[a("a-divider",[e._v("API配置信息")]),a("a-collapse",[a("a-collapse-panel",{key:"1",attrs:{header:"当前页面配置"}},[a("pre",[e._v(e._s(JSON.stringify(e.currentConfig,null,2)))])]),a("a-collapse-panel",{key:"2",attrs:{header:"环境配置"}},[a("pre",[e._v(e._s(JSON.stringify(e.envConfig,null,2)))])])],1)],1)])],1)},r=[],l=a("a34a"),i=a.n(l),o=a("a73df"),n=(a("b27c"),a("ef4b")),c=a("7062");function d(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function u(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?d(Object(a),!0).forEach((function(t){m(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):d(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function m(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function p(e,t,a,s,r,l,i){try{var o=e[l](i),n=o.value}catch(c){return void a(c)}o.done?t(n):Promise.resolve(n).then(s,r)}function h(e){return function(){var t=this,a=arguments;return new Promise((function(s,r){var l=e.apply(t,a);function i(e){p(l,s,r,i,o,"next",e)}function o(e){p(l,s,r,i,o,"throw",e)}i(void 0)}))}}var f={name:"HeartbeatTest",mixins:[o["a"]],data:function(){return{heartbeatConfig:{enabled:!0,interval:1e4,apiKey:Object(n["b"])("admin"),pageType:"test",enableRetry:!0,maxRetries:3,enableCache:!1,enableDebugLog:!0},testing:{basic:!1,smart:!1,batch:!1,full:!1},testResults:[],performanceData:null,currentConfig:null,envConfig:null,tester:null}},created:function(){this.initializeTest()},methods:{initializeTest:function(){this.currentConfig=Object(n["a"])("test",this.heartbeatConfig),this.envConfig=Object(n["c"])(),this.tester=new c["a"]({enableLog:!0,enablePerformanceTest:!0}),this.addTestResult({success:!0,title:"测试环境初始化成功",description:"心跳测试页面已准备就绪，可以开始测试"})},runBasicTest:function(){var e=h(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.testing.basic=!0,e.prev=1,this.addTestResult({success:!0,title:"开始基础心跳测试",description:"测试基础心跳发送功能..."}),e.next=5,this.tester.testBasicHeartbeat("test");case 5:t=e.sent,this.addTestResult({success:t.success,title:"基础心跳测试".concat(t.success?"成功":"失败"),description:"响应时间: ".concat(t.responseTime,"ms"),details:t.response||t.error}),this.updatePerformanceData(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),this.addTestResult({success:!1,title:"基础心跳测试异常",description:e.t0.message,details:e.t0});case 13:return e.prev=13,this.testing.basic=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[1,10,13,16]])})));function t(){return e.apply(this,arguments)}return t}(),runSmartTest:function(){var e=h(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.testing.smart=!0,e.prev=1,this.addTestResult({success:!0,title:"开始智能心跳测试",description:"测试智能心跳（带重试和缓存）功能..."}),e.next=5,this.tester.testSmartHeartbeat("test");case 5:t=e.sent,this.addTestResult({success:t.success,title:"智能心跳测试".concat(t.success?"成功":"失败"),description:"响应时间: ".concat(t.responseTime,"ms"),details:t.response||t.error}),this.updatePerformanceData(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),this.addTestResult({success:!1,title:"智能心跳测试异常",description:e.t0.message,details:e.t0});case 13:return e.prev=13,this.testing.smart=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[1,10,13,16]])})));function t(){return e.apply(this,arguments)}return t}(),runBatchTest:function(){var e=h(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.testing.batch=!0,e.prev=1,this.addTestResult({success:!0,title:"开始批量操作测试",description:"测试批量用户状态查询功能..."}),e.next=5,this.tester.testBatchOperations();case 5:t=e.sent,this.addTestResult({success:t.success,title:"批量操作测试".concat(t.success?"成功":"失败"),description:"响应时间: ".concat(t.responseTime,"ms"),details:t.response||t.error}),this.updatePerformanceData(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),this.addTestResult({success:!1,title:"批量操作测试异常",description:e.t0.message,details:e.t0});case 13:return e.prev=13,this.testing.batch=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[1,10,13,16]])})));function t(){return e.apply(this,arguments)}return t}(),runFullSuite:function(){var e=h(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.testing.full=!0,e.prev=1,this.addTestResult({success:!0,title:"开始完整测试套件",description:"运行所有测试项目..."}),e.next=5,this.tester.runFullTestSuite();case 5:t=e.sent,this.addTestResult({success:t.success,title:"完整测试套件".concat(t.success?"成功":"失败"),description:"总时间: ".concat(t.totalTime,"ms, 成功: ").concat(t.successCount,"/").concat(t.totalCount),details:t.results}),this.performanceData={totalTests:t.performanceMetrics.totalTests,successRate:t.performanceMetrics.totalTests>0?(t.performanceMetrics.successCount/t.performanceMetrics.totalTests*100).toFixed(2):0,avgResponseTime:Math.round(t.performanceMetrics.averageResponseTime),maxResponseTime:Math.round(t.performanceMetrics.maxResponseTime)},e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),this.addTestResult({success:!1,title:"完整测试套件异常",description:e.t0.message,details:e.t0});case 13:return e.prev=13,this.testing.full=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[1,10,13,16]])})));function t(){return e.apply(this,arguments)}return t}(),addTestResult:function(e){this.testResults.unshift(u(u({},e),{},{timestamp:(new Date).toLocaleTimeString()})),this.testResults.length>20&&(this.testResults=this.testResults.slice(0,20))},updatePerformanceData:function(){if(this.tester){var e=this.tester.performanceMetrics;this.performanceData={totalTests:e.totalTests,successRate:e.totalTests>0?(e.successCount/e.totalTests*100).toFixed(2):0,avgResponseTime:Math.round(e.averageResponseTime),maxResponseTime:Math.round(e.maxResponseTime)}}},clearResults:function(){this.testResults=[],this.performanceData=null,this.tester&&this.tester.cleanup(),this.addTestResult({success:!0,title:"测试结果已清空",description:"可以开始新的测试"})}}},b=f,v=(a("1d46"),a("2877")),g=Object(v["a"])(b,s,r,!1,null,"beb8004c",null);t["default"]=g.exports},"4a1c":function(e,t,a){"use strict";var s=a("9c28"),r=a.n(s);r.a},"4cfd":function(e,t,a){"use strict";var s=a("a835"),r=a.n(s);r.a},5288:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"cache-management"},[a("a-card",{attrs:{title:"🗄️ 系统缓存管理",bordered:!1}},[a("div",{staticClass:"cache-stats",staticStyle:{"margin-bottom":"24px"}},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:8}},[a("a-statistic",{attrs:{title:"字典缓存数量",value:e.cacheStats.dictCacheCount}})],1),a("a-col",{attrs:{span:8}},[a("a-statistic",{attrs:{title:"启用字典缓存数量",value:e.cacheStats.enableDictCacheCount}})],1),a("a-col",{attrs:{span:8}},[a("a-statistic",{attrs:{title:"总缓存数量",value:e.cacheStats.totalCacheCount}})],1)],1)],1),a("a-divider"),a("div",{staticClass:"cache-operations"},[a("a-space",{attrs:{size:"large"}},[a("a-button",{attrs:{type:"danger",size:"large",loading:e.clearingAll,disabled:e.clearingSpecific},on:{click:e.clearAllCache}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          清除所有字典缓存\n        ")],1),a("a-input-group",{staticStyle:{width:"400px"},attrs:{compact:""}},[a("a-select",{staticStyle:{width:"70%"},attrs:{placeholder:"选择要清除的字典",disabled:e.clearingAll},model:{value:e.selectedDictCode,callback:function(t){e.selectedDictCode=t},expression:"selectedDictCode"}},[a("a-select-option",{attrs:{value:"plugin_category"}},[e._v("插件分类 (plugin_category)")]),a("a-select-option",{attrs:{value:"isTrue"}},[e._v("是否 (isTrue)")]),a("a-select-option",{attrs:{value:"plugin_status"}},[e._v("插件状态 (plugin_status)")]),a("a-select-option",{attrs:{value:"sex"}},[e._v("性别 (sex)")]),a("a-select-option",{attrs:{value:"priority"}},[e._v("优先级 (priority)")])],1),a("a-button",{staticStyle:{width:"30%"},attrs:{type:"primary",loading:e.clearingSpecific,disabled:!e.selectedDictCode||e.clearingAll},on:{click:e.clearSpecificCache}},[a("a-icon",{attrs:{type:"reload"}}),e._v("\n            清除指定缓存\n          ")],1)],1),a("a-button",{attrs:{loading:e.loadingStats,disabled:e.clearingAll||e.clearingSpecific},on:{click:e.refreshStats}},[a("a-icon",{attrs:{type:"sync"}}),e._v("\n          刷新统计\n        ")],1)],1)],1),a("a-divider"),a("div",{staticClass:"cache-help"},[a("a-alert",{attrs:{message:"缓存清除说明",type:"info","show-icon":""}},[a("div",{attrs:{slot:"description"},slot:"description"},[a("ul",[a("li",[a("strong",[e._v("清除所有字典缓存")]),e._v("：清除Redis中所有字典相关缓存，并通知所有在线用户刷新前端缓存")]),a("li",[a("strong",[e._v("清除指定缓存")]),e._v("：只清除选定字典的缓存，适用于单个字典更新后的缓存刷新")]),a("li",[a("strong",[e._v("自动通知")]),e._v("：缓存清除后会自动通知所有在线用户，用户无需手动刷新浏览器")]),a("li",[a("strong",[e._v("权限要求")]),e._v("：需要 system:cache:clear 权限才能执行缓存清除操作")])])])])],1),a("a-divider"),a("div",{staticClass:"operation-log"},[a("h3",[e._v("最近操作记录")]),a("a-list",{attrs:{"data-source":e.operationLogs,loading:e.loadingLogs},scopedSlots:e._u([{key:"renderItem",fn:function(t){return a("a-list-item",{},[a("a-list-item-meta",[a("div",{attrs:{slot:"title"},slot:"title"},[a("a-tag",{attrs:{color:"CLEAR_ALL"===t.type?"red":"blue"}},[e._v("\n                "+e._s("CLEAR_ALL"===t.type?"清除所有缓存":"清除指定缓存")+"\n              ")]),t.dictCode?a("span",[e._v(e._s(t.dictCode))]):e._e()],1),a("div",{attrs:{slot:"description"},slot:"description"},[e._v("\n              操作人："+e._s(t.operator)+" | 时间："+e._s(t.time)+" | 结果："+e._s(t.result)+"\n            ")])])],1)}}])})],1)],1)],1)},r=[],l=a("a34a"),i=a.n(l),o=a("0fea");function n(e,t,a,s,r,l,i){try{var o=e[l](i),n=o.value}catch(c){return void a(c)}o.done?t(n):Promise.resolve(n).then(s,r)}function c(e){return function(){var t=this,a=arguments;return new Promise((function(s,r){var l=e.apply(t,a);function i(e){n(l,s,r,i,o,"next",e)}function o(e){n(l,s,r,i,o,"throw",e)}i(void 0)}))}}var d={name:"CacheManagement",data:function(){return{cacheStats:{dictCacheCount:0,enableDictCacheCount:0,totalCacheCount:0},selectedDictCode:"",clearingAll:!1,clearingSpecific:!1,loadingStats:!1,loadingLogs:!1,operationLogs:[]}},mounted:function(){this.loadCacheStats(),this.loadOperationLogs()},methods:{loadCacheStats:function(){var e=c(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loadingStats=!0,e.prev=1,e.next=4,Object(o["c"])("/sys/cache/getCacheStats");case 4:t=e.sent,t.success?this.cacheStats=t.result:this.$message.error("获取缓存统计失败："+t.message),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](1),this.$message.error("获取缓存统计异常："+e.t0.message);case 11:return e.prev=11,this.loadingStats=!1,e.finish(11);case 14:case"end":return e.stop()}}),e,this,[[1,8,11,14]])})));function t(){return e.apply(this,arguments)}return t}(),clearAllCache:function(){var e=c(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=this,this.$confirm({title:"确认清除所有字典缓存？",content:"此操作将清除Redis中所有字典缓存，并通知所有在线用户刷新。操作不可撤销，请确认！",okText:"确认清除",okType:"danger",cancelText:"取消",onOk:function(){t.executeClearAllCache()}});case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),executeClearAllCache:function(){var e=c(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.clearingAll=!0,e.prev=1,e.next=4,Object(o["i"])("/sys/cache/clearAllDictCache");case 4:if(t=e.sent,!t.success){e.next=12;break}return this.$message.success(t.message),this.addOperationLog("CLEAR_ALL","","成功"),e.next=10,this.loadCacheStats();case 10:e.next=14;break;case 12:this.$message.error("清除缓存失败："+t.message),this.addOperationLog("CLEAR_ALL","","失败："+t.message);case 14:e.next=20;break;case 16:e.prev=16,e.t0=e["catch"](1),this.$message.error("清除缓存异常："+e.t0.message),this.addOperationLog("CLEAR_ALL","","异常："+e.t0.message);case 20:return e.prev=20,this.clearingAll=!1,e.finish(20);case 23:case"end":return e.stop()}}),e,this,[[1,16,20,23]])})));function t(){return e.apply(this,arguments)}return t}(),clearSpecificCache:function(){var e=c(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.selectedDictCode){e.next=3;break}return this.$message.warning("请选择要清除的字典"),e.abrupt("return");case 3:return this.clearingSpecific=!0,e.prev=4,e.next=7,Object(o["i"])("/sys/cache/clearSpecificDictCache",{dictCode:this.selectedDictCode});case 7:if(t=e.sent,!t.success){e.next=15;break}return this.$message.success(t.message),this.addOperationLog("CLEAR_SPECIFIC",this.selectedDictCode,"成功"),e.next=13,this.loadCacheStats();case 13:e.next=17;break;case 15:this.$message.error("清除缓存失败："+t.message),this.addOperationLog("CLEAR_SPECIFIC",this.selectedDictCode,"失败："+t.message);case 17:e.next=23;break;case 19:e.prev=19,e.t0=e["catch"](4),this.$message.error("清除缓存异常："+e.t0.message),this.addOperationLog("CLEAR_SPECIFIC",this.selectedDictCode,"异常："+e.t0.message);case 23:return e.prev=23,this.clearingSpecific=!1,e.finish(23);case 26:case"end":return e.stop()}}),e,this,[[4,19,23,26]])})));function t(){return e.apply(this,arguments)}return t}(),refreshStats:function(){var e=c(i.a.mark((function e(){return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.loadCacheStats();case 2:this.$message.success("统计信息已刷新");case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),loadOperationLogs:function(){var e=JSON.parse(localStorage.getItem("cache_operation_logs")||"[]");this.operationLogs=e.slice(0,10)},addOperationLog:function(e,t,a){var s={type:e,dictCode:t,result:a,operator:this.$store.getters.userInfo.username||"未知",time:(new Date).toLocaleString()},r=JSON.parse(localStorage.getItem("cache_operation_logs")||"[]");r.unshift(s),r=r.slice(0,50),localStorage.setItem("cache_operation_logs",JSON.stringify(r)),this.loadOperationLogs()}}},u=d,m=(a("910b"),a("2877")),p=Object(m["a"])(u,s,r,!1,null,"4ee69300",null);t["default"]=p.exports},"55e1":function(e,t,a){"use strict";var s=a("5654"),r=a.n(s);r.a},5654:function(e,t,a){},"57ed":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:600,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"dictName",required:"",label:"字典名称"}},[a("a-input",{attrs:{placeholder:"请输入字典名称"},model:{value:e.model.dictName,callback:function(t){e.$set(e.model,"dictName",t)},expression:"model.dictName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"dictCode",required:"",label:"字典编码"}},[a("a-input",{attrs:{placeholder:"请输入字典编码"},model:{value:e.model.dictCode,callback:function(t){e.$set(e.model,"dictCode",t)},expression:"model.dictCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述"}},[a("a-input",{model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1)],1)],1)],1)},r=[],l=(a("88bc"),a("4ec3")),i={name:"DictModal",data:function(){return{value:1,title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{dictName:[{required:!0,message:"请输入字典名称!"}],dictCode:[{required:!0,message:"请输入字典编码!"},{validator:this.validateDictCode}]}}},created:function(){},methods:{validateDictCode:function(e,t,a){var s={tableName:"sys_dict",fieldName:"dict_code",fieldVal:t,dataId:this.model.id};Object(l["m"])(s).then((function(e){e.success?a():a(e.message)}))},handleChange:function(e){this.model.status=e},add:function(){this.edit({})},edit:function(e){e.id?this.visiblekey=!0:this.visiblekey=!1,this.model=Object.assign({},e),this.visible=!0},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;var s;t.confirmLoading=!0,e.model.dictName=(e.model.dictName||"").trim(),e.model.dictCode=(e.model.dictCode||"").trim(),e.model.description=(e.model.description||"").trim(),s=e.model.id?Object(l["n"])(e.model):Object(l["a"])(e.model),s.then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,null,null);t["default"]=c.exports},5859:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:10}},[a("a-col",{attrs:{md:10,sm:12}},[a("a-form-item",{staticStyle:{"margin-left":"8px"},attrs:{label:"用户账号"}},[a("a-input",{attrs:{placeholder:"请输入账号"},model:{value:e.queryParam.username,callback:function(t){e.$set(e.queryParam,"username",t)},expression:"queryParam.username"}})],1)],1),a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-button",{staticStyle:{"margin-left":"18px"},attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)],1)],1)],1)],1),a("div",{staticClass:"table-operator",staticStyle:{"margin-top":"-15px"},attrs:{md:24,sm:24}},[a("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.handleAddUserDepart}},[e._v("添加已有用户")]),a("a-button",{staticStyle:{"margin-top":"16px"},attrs:{type:"primary",icon:"plus"},on:{click:e.handleAdd}},[e._v("新建用户")]),e.selectedRowKeys.length>0?a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.batchDel}},[a("a-icon",{attrs:{type:"delete"}}),e._v("\n          取消关联\n        ")],1)],1),a("a-button",{staticStyle:{"margin-left":"8px"}},[e._v(" 批量操作\n        "),a("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),a("div",[a("div",{staticClass:"ant-alert ant-alert-info",staticStyle:{"margin-bottom":"16px"}},[a("i",{staticClass:"anticon anticon-info-circle ant-alert-icon"}),e._v(" 已选择 "),a("a",{staticStyle:{"font-weight":"600"}},[e._v(e._s(e.selectedRowKeys.length))]),e._v("项\n      "),a("a",{staticStyle:{"margin-left":"24px"},on:{click:e.onClearSelected}},[e._v("清空")])]),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,loading:e.loading,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange}},on:{change:e.handleTableChange},scopedSlots:e._u([{key:"action",fn:function(t,s){return a("span",{},[a("a",{on:{click:function(t){return e.handleEdit(s)}}},[e._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[e._v("\n            更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDeptRole(s)}}},[e._v("部门角色")])]),a("a-menu-item",[a("a",{attrs:{href:"javascript:;"},on:{click:function(t){return e.handleDetail(s)}}},[e._v("用户详情")])]),a("a-menu-item",[a("a-popconfirm",{attrs:{title:"确定取消与选中部门关联吗?"},on:{confirm:function(){return e.handleDelete(s.id)}}},[a("a",[e._v("取消关联")])])],1)],1)],1)],1)}}])})],1),a("user-modal",{ref:"modalForm",on:{ok:e.modalFormOk}}),a("Select-User-Modal",{ref:"selectUserModal",on:{selectFinished:e.selectOK}}),a("dept-role-user-modal",{ref:"deptRoleUser"})],1)},r=[],l=a("b65a"),i=a("0fea"),o=a("b3c4"),n=a("418f"),c=a("8034"),d={name:"DeptUserInfo",mixins:[l["a"]],components:{DeptRoleUserModal:c["default"],SelectUserModal:o["default"],UserModal:n["default"]},data:function(){return{description:"用户信息",currentDeptId:"",currentDept:{},columns:[{title:"用户账号",align:"center",dataIndex:"username"},{title:"用户名称",align:"center",dataIndex:"realname"},{title:"部门",align:"center",dataIndex:"orgCode"},{title:"性别",align:"center",dataIndex:"sex_dictText"},{title:"电话",align:"center",dataIndex:"phone"},{title:"操作",dataIndex:"action",scopedSlots:{customRender:"action"},align:"center",width:150}],url:{list:"/sys/user/departUserList",edit:"/sys/user/editSysDepartWithUser",delete:"/sys/user/deleteUserInDepart",deleteBatch:"/sys/user/deleteUserInDepartBatch"}}},created:function(){},methods:{searchReset:function(){this.queryParam={},this.loadData(1)},loadData:function(e){var t=this;if(this.url.list){1===e&&(this.ipagination.current=1);var a=this.getQueryParams();a.depId=this.currentDeptId,Object(i["c"])(this.url.list,a).then((function(e){e.success&&e.result&&(t.dataSource=e.result.records,t.ipagination.total=e.result.total)}))}else this.$message.error("请设置url.list属性!")},batchDel:function(){if(this.url.deleteBatch)if(this.currentDeptId)if(this.selectedRowKeys.length<=0)this.$message.warning("请选择一条记录！");else{for(var e="",t=0;t<this.selectedRowKeys.length;t++)e+=this.selectedRowKeys[t]+",";var a=this;this.$confirm({title:"确认取消",content:"是否取消用户与选中部门的关联?",onOk:function(){Object(i["a"])(a.url.deleteBatch,{depId:a.currentDeptId,userIds:e}).then((function(e){e.success?(a.$message.success("删除用户与选中部门关系成功！"),a.loadData(),a.onClearSelected()):a.$message.warning(e.message)}))}})}else this.$message.error("未选中任何部门，无法取消部门与用户的关联!");else this.$message.error("请设置url.deleteBatch属性!")},handleDelete:function(e){var t=this;if(this.url.delete)if(this.currentDeptId){var a=this;Object(i["a"])(a.url.delete,{depId:this.currentDeptId,userId:e}).then((function(s){if(s.success){if(a.$message.success("删除用户与选中部门关系成功！"),t.selectedRowKeys.length>0)for(var r=0;r<t.selectedRowKeys.length;r++)if(t.selectedRowKeys[r]==e){t.selectedRowKeys.splice(r,1);break}a.loadData()}else a.$message.warning(s.message)}))}else this.$message.error("未选中任何部门，无法取消部门与用户的关联!");else this.$message.error("请设置url.delete属性!")},open:function(e){this.currentDeptId=e.id,this.currentDept=e,this.loadData(1)},clearList:function(){this.currentDeptId="",this.dataSource=[]},hasSelectDept:function(){return""!=this.currentDeptId||(this.$message.error("请选择一个部门!"),!1)},handleAddUserDepart:function(){""==this.currentDeptId?this.$message.error("请选择一个部门!"):this.$refs.selectUserModal.visible=!0},handleEdit:function(e){this.$refs.modalForm.title="编辑",this.$refs.modalForm.departDisabled=!0,this.$refs.modalForm.disableSubmit=!1,this.$refs.modalForm.edit(e)},handleAdd:function(){""==this.currentDeptId?this.$message.error("请选择一个部门!"):(this.$refs.modalForm.departDisabled=!0,this.$refs.modalForm.nextDepartOptions=[{value:this.currentDept.key,label:this.currentDept.title}],this.$refs.modalForm.title="新增",this.$refs.modalForm.edit({activitiSync:"1",userIdentity:1,selecteddeparts:this.currentDeptId}))},selectOK:function(e){var t=this,a={};a.depId=this.currentDeptId,a.userIdList=[];for(var s=0;s<e.length;s++)a.userIdList.push(e[s]);Object(i["i"])(this.url.edit,a).then((function(e){e.success?(t.$message.success(e.message),t.loadData()):t.$message.warning(e.message)}))},handleDeptRole:function(e){""!=this.currentDeptId?(this.$refs.deptRoleUser.add(e,this.currentDeptId),this.$refs.deptRoleUser.title="部门角色分配"):this.$message.warning("请先选择一个部门!")}}},u=d,m=(a("80a0"),a("2877")),p=Object(m["a"])(u,s,r,!1,null,"6adc63d1",null);t["default"]=p.exports},6675:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:"数据规则/按钮权限配置",width:"365",closable:!1,visible:e.visible},on:{close:e.onClose}},[a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"数据规则"}},[e.dataruleList.length>0?a("a-checkbox-group",{model:{value:e.dataruleChecked,callback:function(t){e.dataruleChecked=t},expression:"dataruleChecked"}},[a("a-row",[e._l(e.dataruleList,(function(t,s){return a("a-col",{key:"dr"+s,attrs:{span:24}},[a("a-checkbox",{attrs:{value:t.id}},[e._v(e._s(t.ruleName))])],1)})),a("a-col",{attrs:{span:24}},[a("div",{staticStyle:{width:"100%","margin-top":"15px"}},[a("a-button",{attrs:{type:"primary",size:"small",icon:"save"},on:{click:e.saveDataruleForRole}},[e._v("点击保存")])],1)])],2)],1):a("div",[a("h3",[e._v("无配置信息!")])])],1)],1)],1)},r=[],l=a("290c"),i=a("da05"),o=a("0fea"),n={name:"DepartDataruleModal",components:{ACol:i["b"],ARow:l["a"]},data:function(){return{functionId:"",departId:"",visible:!1,tabList:[{key:"1",tab:"数据规则"},{key:"2",tab:"按钮权限"}],activeTabKey:"1",url:{datarule:"/sys/sysDepartPermission/datarule"},dataruleList:[],dataruleChecked:[]}},methods:{loadData:function(){var e=this;Object(o["c"])("".concat(this.url.datarule,"/").concat(this.functionId,"/").concat(this.departId)).then((function(t){if(t.success){e.dataruleList=t.result.datarule;var a=t.result.drChecked;a&&(e.dataruleChecked=a.split(","))}}))},saveDataruleForRole:function(){var e=this;this.dataruleChecked&&0!=this.dataruleChecked.length||this.$message.warning("请注意，现未勾选任何数据权限!");var t={permissionId:this.functionId,departId:this.departId,dataRuleIds:this.dataruleChecked.join(",")};Object(o["i"])(this.url.datarule,t).then((function(t){t.success?e.$message.success(t.message):e.$message.error(t.message)}))},show:function(e,t){this.onReset(),this.functionId=e,this.departId=t,this.visible=!0,this.loadData()},onClose:function(){this.visible=!1,this.onReset()},onTabChange:function(e){this.activeTabKey=e},onReset:function(){this.functionId="",this.departId="",this.dataruleList=[],this.dataruleChecked=[]}}},c=n,d=a("2877"),u=Object(d["a"])(c,s,r,!1,null,"73771a6a",null);t["default"]=u.exports},"6b7a":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,maskClosable:!1,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则名称",prop:"ruleName"}},[a("a-input",{attrs:{placeholder:"请输入规则名称"},model:{value:e.model.ruleName,callback:function(t){e.$set(e.model,"ruleName",t)},expression:"model.ruleName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则Code",prop:"ruleCode"}},[a("a-input",{attrs:{placeholder:"请输入规则Code",disabled:e.disabledCode},model:{value:e.model.ruleCode,callback:function(t){e.$set(e.model,"ruleCode",t)},expression:"model.ruleCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则实现类",prop:"ruleClass"}},[a("a-input",{attrs:{placeholder:"请输入规则实现类"},model:{value:e.model.ruleClass,callback:function(t){e.$set(e.model,"ruleClass",t)},expression:"model.ruleClass"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则参数",prop:"ruleParams"}},[a("a-textarea",{attrs:{placeholder:"请输入规则参数",rows:5},model:{value:e.model.ruleParams,callback:function(t){e.$set(e.model,"ruleParams",t)},expression:"model.ruleParams"}})],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("ca00"),o={name:"SysFillRuleModal",components:{},data:function(){var e=this;return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{ruleName:[{required:!0,message:"规则名称不能为空"}],ruleCode:[{required:!0,message:"规则Code不能为空"},{validator:function(t,a,s){return Object(i["r"])("sys_fill_rule","rule_code",a,e.model.id,s)}}],ruleClass:[{required:!0,message:"规则实现类不能为空"}],ruleParams:[{validator:function(e,t,a){try{var s=JSON.parse(t);s instanceof Array?a("只能传递JSON对象，不能传递JSON数组"):s instanceof Object?a():a("请输入JSON字符串")}catch(r){a("请输入JSON字符串")}}}]},url:{add:"/sys/fillRule/add",edit:"/sys/fillRule/edit"}}},computed:{disabledCode:function(){return!!this.model.id}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.form.resetFields(),t.model=Object.assign({},e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a,s){if(a){t.confirmLoading=!0;var r=e.url.add,i="post";e.model.id&&(r=e.url.edit,i="put"),Object(l["h"])(r,e.model,i).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.close()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"5e87d41a",null);t["default"]=d.exports},"6e8d":function(e,t,a){"use strict";var s=a("e3fb"),r=a.n(s);r.a},7437:function(e,t,a){},7474:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("j-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,switchFullscreen:"",okButtonProps:{class:{"jee-hidden":e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("tenant-form",{ref:"realForm",attrs:{disabled:e.disableSubmit,normal:""},on:{ok:e.submitCallback}})],1)},r=[],l=a("4890"),i={name:"TenantModal",components:{TenantForm:l["default"]},data:function(){return{title:"",width:800,visible:!1,disableSubmit:!1}},methods:{add:function(){var e=this;this.visible=!0,this.$nextTick((function(){e.$refs.realForm.show()}))},edit:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.realForm.show(e)}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){this.$refs.realForm.submitForm()},submitCallback:function(){this.$emit("ok"),this.visible=!1},handleCancel:function(){this.close()}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,null,null);t["default"]=c.exports},"74c6":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"用户名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["userName",{}],expression:"['userName', {}]"}],attrs:{placeholder:"请输入用户名",readOnly:""}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"代理人用户名"}},[a("j-select-user-by-dep",{directives:[{name:"decorator",rawName:"v-decorator",value:["agentUserName",e.validatorRules.agentUserName],expression:"['agentUserName', validatorRules.agentUserName]"}],attrs:{placeholder:"请输入代理人用户名","trigger-change":!0}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"代理开始时间"}},[a("j-date",{directives:[{name:"decorator",rawName:"v-decorator",value:["startTime",e.validatorRules.startTime],expression:"[ 'startTime', validatorRules.startTime]"}],staticStyle:{width:"100%"},attrs:{"trigger-change":!0,showTime:!0,"date-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择开始时间"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"代理结束时间"}},[a("j-date",{directives:[{name:"decorator",rawName:"v-decorator",value:["endTime",e.validatorRules.endTime],expression:"[ 'endTime', validatorRules.endTime]"}],staticStyle:{width:"100%"},attrs:{"trigger-change":!0,showTime:!0,"date-format":"YYYY-MM-DD HH:mm:ss",placeholder:"请选择结束时间"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态"}},[a("a-radio-group",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{}],expression:"[ 'status', {}]"}],staticClass:"fontiframe",attrs:{name:"radioGroup"}},[a("a-radio",{staticClass:"radioGroup",attrs:{value:"1"}},[e._v("有效")]),a("a-radio",{staticClass:"radioGroup",attrs:{value:"0"}},[e._v("无效")])],1)],1)],1)],1)],1)},r=[],l=a("88bc"),i=a.n(l),o=a("0fea"),n=a("2dab"),c=a("c14a"),d={name:"SysUserAgentModal",components:{JDate:n["default"],JSelectUserByDep:c["default"]},data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},username:"",confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{agentUserName:{rules:[{required:!0,message:"请输入代理人用户名!"}]},startTime:{rules:[{required:!0,message:"请输入代理开始时间!"}]},endTime:{rules:[{required:!0,message:"请输入代理结束时间!"}]}},url:{add:"/sys/sysUserAgent/add",edit:"/sys/sysUserAgent/edit",queryByUserName:"/sys/sysUserAgent/queryByUserName"}}},created:function(){},methods:{agentSettings:function(e){this.username=e,this.init()},init:function(){var e=this,t={userName:this.username};Object(o["c"])(this.url.queryByUserName,t).then((function(t){t.success?e.edit(t.result):e.edit({userName:e.username,status:"0"})}))},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(i()(t.model,"userName","agentUserName","status","startTime","endTime"))}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.form.validateFields((function(a,s){if(!a){t.confirmLoading=!0;var r="",l="";e.model.id?(r+=e.url.edit,l="put"):(r+=e.url.add,l="post");var i=Object.assign(e.model,s);Object(o["h"])(r,i,l).then((function(e){e.success?t.$message.success(e.message):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}}))},handleCancel:function(){this.close()}}},u=d,m=a("2877"),p=Object(m["a"])(u,s,r,!1,null,"012695d9",null);t["default"]=p.exports},"79c1":function(e,t,a){"use strict";var s=a("c8ad"),r=a.n(s);r.a},"7c333":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1e3,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"ruleName",label:"规则名称"}},[a("a-input",{attrs:{placeholder:"请输入规则名称"},model:{value:e.model.ruleName,callback:function(t){e.$set(e.model,"ruleName",t)},expression:"model.ruleName"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.showRuleColumn,expression:"showRuleColumn"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"ruleColumn",label:"规则字段"}},[a("a-input",{attrs:{placeholder:"请输入规则字段"},model:{value:e.model.ruleColumn,callback:function(t){e.$set(e.model,"ruleColumn","string"===typeof t?t.trim():t)},expression:"model.ruleColumn"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"ruleConditions",label:"条件规则"}},[a("j-dict-select-tag",{attrs:{placeholder:"请输入条件规则",dictCode:"rule_conditions"},on:{input:e.handleChangeRuleCondition},model:{value:e.model.ruleConditions,callback:function(t){e.$set(e.model,"ruleConditions",t)},expression:"model.ruleConditions"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"ruleValue",label:"规则值"}},[a("a-input",{attrs:{placeholder:"请输入规则值"},model:{value:e.model.ruleValue,callback:function(t){e.$set(e.model,"ruleValue","string"===typeof t?t.trim():t)},expression:"model.ruleValue"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态"}},[a("a-radio-group",{attrs:{buttonStyle:"solid"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}},[a("a-radio-button",{attrs:{value:"1"}},[e._v("有效")]),a("a-radio-button",{attrs:{value:"0"}},[e._v("无效")])],1)],1)],1)],1)],1)},r=[],l=a("0fea"),i={name:"PermissionDataRuleModal",data:function(){return{queryParam:{},title:"操作",visible:!1,model:{},ruleConditionList:[],labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,permissionId:"",validatorRules:{ruleConditions:[{required:!0,message:"请选择条件!"}],ruleName:[{required:!0,message:"请输入规则名称!"}],ruleValue:[{required:!0,message:"请输入规则值!"}],ruleColumn:[]},url:{list:"/sys/dictItem/list",add:"/sys/permission/addPermissionRule",edit:"/sys/permission/editPermissionRule"},showRuleColumn:!0}},created:function(){},methods:{add:function(e){this.permissionId=e,this.edit({status:"1"})},edit:function(e){this.model=Object.assign({},e),e.permissionId?this.model.permissionId=e.permissionId:this.model.permissionId=this.permissionId,this.visible=!0,this.initRuleCondition()},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),Object(l["h"])(s,e.model,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},initRuleCondition:function(){this.model.ruleConditions&&"USE_SQL_RULES"==this.model.ruleConditions?this.showRuleColumn=!1:this.showRuleColumn=!0},handleChangeRuleCondition:function(e){"USE_SQL_RULES"==e?(this.model.ruleColumn="",this.showRuleColumn=!1):this.showRuleColumn=!0}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,"110778b0",null);t["default"]=c.exports},"7d57":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:"功能测试",width:800,visible:e.visible},on:{ok:function(t){e.visible=!1},cancel:function(t){e.visible=!1}}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"功能测试"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["test",e.validatorRules.test],expression:"['test', validatorRules.test]"}],attrs:{placeholder:"请输入"},on:{change:function(t){return e.testValue=t.target.value}}})],1)],1),a("a-row",{attrs:{type:"flex",gutter:8}},e._l(e.testValue,(function(t,s){return a("a-col",{key:s},[a("a-row",[a("a-col",[a("a-input",{staticStyle:{"text-align":"center",width:"40px"},attrs:{value:t}})],1),a("a-col",{staticStyle:{"text-align":"center"}},[e._v(e._s(s+1))])],1)],1)})),1)],1)},r=[],l=a("ca00"),i={name:"SysCheckRuleTestModal",data:function(){var e=this;return{title:"操作",visible:!1,ruleCode:"",testValue:"",form:this.$form.createForm(this),validatorRules:{test:{rules:[{validator:function(t,a,s){return Object(l["q"])(e.ruleCode,a,s)}}]}}}},methods:{open:function(e){this.ruleCode=e,this.form.resetFields(),this.testValue="",this.visible=!0}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,"1ea5c763",null);t["default"]=c.exports},"7f76":function(e,t,a){"use strict";var s=a("8a01"),r=a.n(s);r.a},8034:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{staticStyle:{overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:"600",placement:"right",closable:!0,visible:e.visible},on:{close:e.close}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[e.designNameOption.length>0?a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:""}},[a("a-col",{attrs:{xl:24,lg:24,md:24,sm:24,xs:24}},[a("a-card",{style:{marginTop:"12px",height:"auto"}},[a("a-checkbox-group",{staticStyle:{width:"100%"},on:{change:e.designNameChange},model:{value:e.designNameValue,callback:function(t){e.designNameValue=t},expression:"designNameValue"}},[a("a-row",[e._l(e.designNameOption,(function(t){return[a("a-col",{attrs:{span:6}},[a("a-checkbox",{attrs:{value:t.value}},[e._v(e._s(t.text))])],1)]}))],2)],1)],1)],1)],1)],1):a("div",[a("h3",[e._v("无可配置角色!")])])],1),a("div",{staticClass:"drawer-bootom-button"},[a("a-dropdown",{staticStyle:{float:"left"},attrs:{trigger:["click"],placement:"topCenter"}},[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:e.checkALL}},[e._v("全部勾选")]),a("a-menu-item",{key:"2",on:{click:e.cancelCheckALL}},[e._v("取消全选")])],1),a("a-button",[e._v("\n        操作 "),a("a-icon",{attrs:{type:"up"}})],1)],1),a("a-popconfirm",{attrs:{title:"确定放弃编辑？",okText:"确定",cancelText:"取消"},on:{confirm:e.close}},[a("a-button",{staticStyle:{"margin-right":".8rem"}},[e._v("取消")])],1),a("a-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit(!0)}}},[e._v("保存")])],1)],1)},r=[],l=a("0fea"),i=a("d579");a("89f2");function o(e,t){var a;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(a=n(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var s=0,r=function(){};return{s:r,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,i=!0,o=!1;return{s:function(){a=e[Symbol.iterator]()},n:function(){var e=a.next();return i=e.done,e},e:function(e){o=!0,l=e},f:function(){try{i||null==a.return||a.return()}finally{if(o)throw l}}}}function n(e,t){if(e){if("string"===typeof e)return c(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}var d={name:"DeptRoleUserModal",components:{JEllipsis:i["default"]},data:function(){return{currentDeptId:"",title:"部门角色分配",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{},url:{add:"/sys/sysDepartRole/deptRoleUserAdd",getDeptRoleList:"/sys/sysDepartRole/getDeptRoleList",getDeptRoleByUserId:"/sys/sysDepartRole/getDeptRoleByUserId"},designNameOption:[],userId:"",newRoleId:"",oldRoleId:"",designNameValue:[],desformList:[]}},created:function(){},methods:{add:function(e,t){this.userId=e.id,this.currentDeptId=t,this.loadDesformList(),this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,Object(l["c"])(this.url.getDeptRoleByUserId,{userId:this.userId,departId:this.currentDeptId}).then((function(e){if(e.success){var a,s=[],r=o(e.result);try{for(r.s();!(a=r.n()).done;){var l=a.value;s.push(l.droleId)}}catch(i){r.e(i)}finally{r.f()}t.oldRoleId=s.join(","),t.designNameValue=s,t.newRoleId=s.join(",")}}))},close:function(){this.$emit("close"),this.visible=!1},handleSubmit:function(){var e=this;e.confirmLoading=!0;var t=this.url.add,a="post",s=Object.assign(this.model,{});s.userId=this.userId,s.newRoleId=this.newRoleId,s.oldRoleId=this.oldRoleId,Object(l["h"])(t,s,a).then((function(t){t.success?(e.$message.success(t.message),e.$emit("reload"),e.$emit("ok")):e.$message.warning(t.message)})).finally((function(){e.confirmLoading=!1,e.close()}))},handleCancel:function(){this.designNameOption=[],this.designNameValue=[],this.close()},designNameChange:function(e){this.newRoleId=e.join(",")},checkALL:function(){var e,t=[],a=o(this.desformList);try{for(a.s();!(e=a.n()).done;){var s=e.value;t.push(s.id)}}catch(r){a.e(r)}finally{a.f()}this.designNameValue=t,this.newRoleId=t.join(",")},cancelCheckALL:function(){this.designNameValue=[],this.newRoleId=""},loadDesformList:function(){var e=this;Object(l["c"])(this.url.getDeptRoleList,{departId:this.currentDeptId,userId:this.userId}).then((function(t){if(t.success){e.desformList=t.result;var a,s=[],r=o(e.desformList);try{for(r.s();!(a=r.n()).done;){var l=a.value;s.push({value:l.id,text:l.roleName})}}catch(i){r.e(i)}finally{r.f()}e.designNameOption=s}}))}}},u=d,m=(a("39f6"),a("2877")),p=Object(m["a"])(u,s,r,!1,null,"5943a896",null);t["default"]=p.exports},8046:function(e,t,a){"use strict";var s=a("e884"),r=a.n(s);r.a},"80a0":function(e,t,a){"use strict";var s=a("17d0"),r=a.n(s);r.a},8144:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"itemText",label:"名称"}},[a("a-input",{attrs:{placeholder:"请输入名称"},model:{value:e.model.itemText,callback:function(t){e.$set(e.model,"itemText",t)},expression:"model.itemText"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"itemValue",label:"数据值"}},[a("a-input",{attrs:{placeholder:"请输入数据值"},model:{value:e.model.itemValue,callback:function(t){e.$set(e.model,"itemValue",t)},expression:"model.itemValue"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述"}},[a("a-input",{model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"排序值"}},[a("a-input-number",{attrs:{min:1},model:{value:e.model.sortOrder,callback:function(t){e.$set(e.model,"sortOrder",t)},expression:"model.sortOrder"}}),e._v("\n        值越小越靠前\n      ")],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"是否启用",hasFeedback:""}},[a("a-switch",{attrs:{checkedChildren:"启用",unCheckedChildren:"禁用"},on:{change:e.onChose},model:{value:e.visibleCheck,callback:function(t){e.visibleCheck=t},expression:"visibleCheck"}})],1)],1)],1)],1)},r=[],l=(a("88bc"),a("4ec3")),i=a("0fea"),o={name:"DictItemModal",data:function(){return{title:"操作",visible:!1,visibleCheck:!0,model:{},dictId:"",status:1,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{itemText:[{required:!0,message:"请输入名称!"}],itemValue:[{required:!0,message:"请输入数据值!"},{validator:this.validateItemValue}]}}},created:function(){},methods:{add:function(e){this.dictId=e,this.edit({sortOrder:1,status:1})},edit:function(e){e.id&&(this.dictId=e.dictId),this.status=e.status,this.visibleCheck=1==e.status,this.model=Object.assign({},e),this.model.dictId=this.dictId,this.model.status=this.status,this.visible=!0},onChose:function(e){e?(this.status=1,this.visibleCheck=!0):(this.status=0,this.visibleCheck=!1)},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;var s;t.confirmLoading=!0,e.model.itemText=(e.model.itemText||"").trim(),e.model.itemValue=(e.model.itemValue||"").trim(),e.model.description=(e.model.description||"").trim(),e.model.status=e.status,s=e.model.id?Object(l["o"])(e.model):Object(l["b"])(e.model),s.then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},validateItemValue:function(e,t,a){var s={itemValue:t,dictId:this.dictId};if(this.model.id&&(s.id=this.model.id),t){var r=new RegExp("[`_~!@#$^&*()=|{}'.<>《》/?！￥（）—【】‘；：”“。，、？]");r.test(t)?a("数据值不能包含特殊字符！"):Object(i["c"])("/sys/dictItem/dictItemCheck",s).then((function(e){e.success?a():a(e.message)}))}else a()}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,null,null);t["default"]=d.exports},8573:function(e,t,a){"use strict";var s=a("2010"),r=a.n(s);r.a},"8a01":function(e,t,a){},"8e7c":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,maskClosable:!1,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"code",required:"",label:"职务编码"}},[a("a-input",{attrs:{placeholder:"请输入职务编码","read-only":e.readOnly},model:{value:e.model.code,callback:function(t){e.$set(e.model,"code",t)},expression:"model.code"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"name",required:"",label:"职务名称"}},[a("a-input",{attrs:{placeholder:"请输入职务名称"},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"postRank",required:"",label:"职级"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择职级",dictCode:"position_rank"},model:{value:e.model.postRank,callback:function(t){e.$set(e.model,"postRank",t)},expression:"model.postRank"}})],1)],1)],1)],1)},r=[],l=(a("88bc"),a("0fea")),i=a("4ec3"),o=a("7b16"),n=null,c={name:"SysPositionModal",components:{JDictSelectTag:o["default"]},data:function(){var e=this;return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{code:[{required:!0,message:"请输入职务编码"},{validator:function(t,a,s){n&&clearTimeout(n),n=setTimeout((function(){Object(i["m"])({tableName:"sys_position",fieldName:"code",fieldVal:a,dataId:e.model.id}).then((function(e){e.success?s():s(e.message)})).catch(console.error)}),300)}}],name:[{required:!0,message:"请输入职务名称"}],postRank:[{required:!0,message:"请选择职级"}]},url:{add:"/sys/position/add",edit:"/sys/position/edit"},readOnly:!1}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){this.model=Object.assign({},e),this.visible=!0,e.id?this.readOnly=!0:this.readOnly=!1},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),Object(l["h"])(s,e.model,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()}}},d=c,u=a("2877"),m=Object(u["a"])(d,s,r,!1,null,"6dbfc6c7",null);t["default"]=m.exports},"8fe7":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"simple-error-test"},[a("a-card",{attrs:{title:"服务器异常测试"}},[a("p",[e._v("点击下面的按钮测试服务器异常页面：")]),a("br"),a("a-button",{attrs:{type:"primary",size:"large",loading:e.testing},on:{click:e.testServerError}},[e._v("\n      测试服务器连接异常\n    ")]),a("br"),a("br"),a("a-button",{attrs:{type:"default"},on:{click:e.goToServerError}},[e._v("\n      直接访问异常页面\n    ")]),a("br"),a("br"),a("a-button",{attrs:{type:"danger",size:"large"},on:{click:e.testErrorHandler}},[e._v("\n      测试错误处理器\n    ")]),a("br"),a("br"),e.testResult?a("div",{staticClass:"test-result"},[a("a-alert",{attrs:{message:e.testResult.title,description:e.testResult.description,type:e.testResult.type,"show-icon":""}})],1):e._e()],1)],1)},r=[],l=a("a34a"),i=a.n(l),o=a("bc3a"),n=a.n(o),c=a("834a");function d(e,t,a,s,r,l,i){try{var o=e[l](i),n=o.value}catch(c){return void a(c)}o.done?t(n):Promise.resolve(n).then(s,r)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(s,r){var l=e.apply(t,a);function i(e){d(l,s,r,i,o,"next",e)}function o(e){d(l,s,r,i,o,"throw",e)}i(void 0)}))}}var m={name:"SimpleErrorTest",data:function(){return{testing:!1,testResult:null}},methods:{testServerError:function(){var e=u(i.a.mark((function e(){return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.testing=!0,this.testResult=null,e.prev=2,e.next=5,n.a.get("http://localhost:9999/test-connection",{timeout:3e3});case 5:this.testResult={title:"测试失败",description:"预期应该出现连接错误，但请求成功了",type:"warning"},e.next=12;break;case 8:e.prev=8,e.t0=e["catch"](2),"ERR_CONNECTION_REFUSED"===e.t0.code||e.t0.message.includes("ERR_CONNECTION_REFUSED")?this.testResult={title:"测试成功",description:"成功触发连接被拒绝错误，应该会自动跳转到服务器异常页面",type:"success"}:this.testResult={title:"测试结果",description:"捕获到错误: ".concat(e.t0.message),type:"info"};case 12:return e.prev=12,this.testing=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,this,[[2,8,12,15]])})));function t(){return e.apply(this,arguments)}return t}(),goToServerError:function(){this.$router.push({name:"ServerError",query:{errorType:"CONNECTION_REFUSED",errorMessage:"Test connection refused error",timestamp:Date.now()}})},testErrorHandler:function(){var e=new Error("Network Error");e.code="ERR_CONNECTION_REFUSED";var t=Object(c["b"])(e);this.testResult={title:"错误处理器测试",description:"错误处理器返回: ".concat(t,"，请查看控制台日志"),type:t?"success":"warning"}}}},p=m,h=(a("2a85"),a("2877")),f=Object(h["a"])(p,s,r,!1,null,"d79e06f8",null);t["default"]=f.exports},"910b":function(e,t,a){"use strict";var s=a("1f70"),r=a.n(s);r.a},9655:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1e3,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则名称",prop:"ruleName"}},[a("a-input",{attrs:{placeholder:"请输入规则名称"},model:{value:e.model.ruleName,callback:function(t){e.$set(e.model,"ruleName",t)},expression:"model.ruleName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则Code",prop:"ruleCode"}},[a("a-input",{attrs:{placeholder:"请输入规则Code"},model:{value:e.model.ruleCode,callback:function(t){e.$set(e.model,"ruleCode",t)},expression:"model.ruleCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则描述",prop:"ruleDescription"}},[a("a-textarea",{attrs:{placeholder:"请输入规则描述"},model:{value:e.model.ruleDescription,callback:function(t){e.$set(e.model,"ruleDescription",t)},expression:"model.ruleDescription"}})],1)],1),a("a-tabs",{model:{value:e.tabs.activeKey,callback:function(t){e.$set(e.tabs,"activeKey",t)},expression:"tabs.activeKey"}},[a("a-tab-pane",{key:e.tabs.design.key,attrs:{tab:"局部规则",forceRender:""}},[a("a-alert",{attrs:{type:"info",showIcon:"",message:"局部规则按照你输入的位数有序的校验。"}}),a("j-editable-table",{ref:"designTable",staticStyle:{"margin-top":"8px"},attrs:{dragSort:"",rowNumber:"",maxHeight:240,columns:e.tabs.design.columns,dataSource:e.tabs.design.dataSource},scopedSlots:e._u([{key:"action",fn:function(e){return[a("my-action-button",{attrs:{rowEvent:e}})]}}])})],1),a("a-tab-pane",{key:e.tabs.global.key,attrs:{tab:"全局规则",forceRender:""}},[a("j-editable-table",{ref:"globalTable",attrs:{dragSort:"",rowNumber:"",actionButton:"",maxHeight:240,columns:e.tabs.global.columns,dataSource:e.tabs.global.dataSource},scopedSlots:e._u([{key:"actionButtonAfter",fn:function(){return[a("a-alert",{staticStyle:{"margin-bottom":"8px"},attrs:{type:"info",showIcon:"",message:"全局规则可校验用户输入的所有字符；全局规则的优先级比局部规则的要高。"}})]},proxy:!0},{key:"action",fn:function(e){return[a("my-action-button",{attrs:{rowEvent:e,allowEmpty:""}})]}}])})],1)],1)],1)],1)},r=[],l=a("88bc"),i=a.n(l),o=a("0fea"),n=a("ca00"),c=a("e2e0"),d=a("7550");function u(e,t){return b(e)||f(e,t)||p(e,t)||m()}function m(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function p(e,t){if(e){if("string"===typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],s=!0,r=!1,l=void 0;try{for(var i,o=e[Symbol.iterator]();!(s=(i=o.next()).done);s=!0)if(a.push(i.value),t&&a.length===t)break}catch(n){r=!0,l=n}finally{try{s||null==o["return"]||o["return"]()}finally{if(r)throw l}}return a}}function b(e){if(Array.isArray(e))return e}var v={name:"SysCheckRuleModal",components:{JEditableTable:d["default"],"my-action-button":{props:{rowEvent:Object,allowEmpty:Boolean},methods:{confirmIsShow:function(){var e=this.rowEvent,t=e.index,a=e.allValues.inputValues,s=a[t];return s.digits||s.pattern},handleLineAdd:function(){var e=this.rowEvent.target;e.add()},handleLineDelete:function(){var e=this.rowEvent,t=e.rowId,a=e.target;a.removeRows(t)},renderDeleteButton:function(){var e=this.$createElement;return this.allowEmpty||this.rowEvent.index>0?this.confirmIsShow()?e("a-popconfirm",{attrs:{title:"确定要删除吗？"},on:{confirm:this.handleLineDelete}},[e("a-button",{attrs:{icon:"minus"}})]):e("a-button",{attrs:{icon:"minus"},on:{click:this.handleLineDelete}}):""}},render:function(){var e=arguments[0];return e("div",[e("a-button",{on:{click:this.handleLineAdd},attrs:{icon:"plus"}})," ",this.renderDeleteButton()])}}},data:function(){var e=this;return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{ruleName:[{required:!0,message:"请输入规则名称!"}],ruleCode:[{required:!0,message:"请输入规则Code!"},{validator:function(t,a,s){return Object(n["r"])("sys_check_rule","rule_code",a,e.model.id,s)}}]},tabs:{activeKey:"design",global:{key:"global",columns:[{title:"优先级",key:"priority",width:"15%",type:c["a"].select,defaultValue:"1",options:[{title:"优先运行",value:"1"},{title:"最后运行",value:"0"}],validateRules:[]},{title:"规则（正则表达式）",key:"pattern",width:"50%",type:c["a"].input,validateRules:[{required:!0,message:"规则不能为空"},{handler:this.validatePatternHandler}]},{title:"提示文本",key:"message",width:"20%",type:c["a"].input,validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"操作",key:"action",width:"15%",slotName:"action",type:c["a"].slot}],dataSource:[]},design:{key:"design",columns:[{title:"位数",key:"digits",width:"15%",type:c["a"].inputNumber,validateRules:[{required:!0,message:"${title}不能为空"},{pattern:/^[1-9]\d*$/,message:"请输入零以上的正整数"}]},{title:"规则（正则表达式）",key:"pattern",width:"50%",type:c["a"].input,validateRules:[{required:!0,message:"规则不能为空"},{handler:this.validatePatternHandler}]},{title:"提示文本",key:"message",width:"20%",type:c["a"].input,validateRules:[{required:!0,message:"${title}不能为空"}]},{title:"操作",key:"action",width:"15%",slotName:"action",type:c["a"].slot}],dataSource:[]}},url:{add:"/sys/checkRule/add",edit:"/sys/checkRule/edit"}}},created:function(){},methods:{validatePatternHandler:function(e,t,a,s,r,l){if("blur"===e||"getValues"===e)try{new RegExp(t),r(!0)}catch(i){r(!1,"请输入正确的正则表达式")}else r(!0)},add:function(){this.edit({})},edit:function(e){var t=this;this.tabs.activeKey=this.tabs.design.key,this.tabs.global.dataSource=[],this.tabs.design.dataSource=[{digits:"",pattern:"",message:""}],this.visible=!0,this.$nextTick((function(){t.$refs.form.resetFields(),t.model=Object.assign({},e);var a=t.model.ruleJson;if(a){var s=JSON.parse(a),r=[],l=[],i="1";s.forEach((function(e){"*"===e.digits?r.push(Object.assign(e,{priority:i})):(i="0",l.push(e))})),t.tabs.global.dataSource=r,t.tabs.design.dataSource=l}}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this;Promise.all([Object(n["a"])(new Promise((function(t,a){e.$refs.form.validate((function(s,r){return s?t(e.model):a(r)}))}))),Object(n["a"])(this.$refs.designTable.getValuesPromise),Object(n["a"])(this.$refs.globalTable.getValuesPromise)]).then((function(t){var a=u(t,3),s=a[0],r=a[1],l=a[2];if(s.type===n["c"])return Promise.reject("主表校验未通过");if(r.type===n["c"])return e.tabs.activeKey=e.tabs.design.key,Promise.reject("局部规则子表校验未通过");if(l.type===n["c"])return e.tabs.activeKey=e.tabs.global.key,Promise.reject("全局规则子表校验未通过");var c=s.data,d=l.data,m=r.data,p=[],h=[];d.forEach((function(e){e.digits="*","1"===e.priority?p.push(e):h.push(e)}));var f=p.concat(m).concat(h),b=f.map((function(e){return i()(e,"digits","pattern","message")})),v=JSON.stringify(b),g=Object.assign(e.model,c,{ruleJson:v}),y="post",C=e.url.add;return e.model.id&&(y="put",C=e.url.edit),e.confirmLoading=!0,Object(o["h"])(C,g,y)})).then((function(t){t.success?(e.$message.success(t.message),e.$emit("ok"),e.close()):e.$message.warning(t.message)})).catch((function(e){})).finally((function(){e.confirmLoading=!1}))},handleCancel:function(){this.close()}}},g=v,y=a("2877"),C=Object(y["a"])(g,s,r,!1,null,"717e3950",null);t["default"]=C.exports},"9c28":function(e,t,a){},a119:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"roleName",label:"部门角色名称"}},[a("a-input",{attrs:{placeholder:"请输入部门角色名称"},model:{value:e.model.roleName,callback:function(t){e.$set(e.model,"roleName",t)},expression:"model.roleName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"roleCode",label:"部门角色编码"}},[a("a-input",{attrs:{placeholder:"请输入部门角色编码"},model:{value:e.model.roleCode,callback:function(t){e.$set(e.model,"roleCode",t)},expression:"model.roleCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"description",label:"描述"}},[a("a-input",{attrs:{placeholder:"请输入描述"},model:{value:e.model.description,callback:function(t){e.$set(e.model,"description",t)},expression:"model.description"}})],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("4ec3"),o={name:"SysDepartRoleModal",data:function(){return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{roleName:[{required:!0,message:"请输入部门角色名称!"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],roleCode:[{required:!0,message:"请输入部门角色编码!"},{min:0,max:64,message:"长度不超过 64 个字符",trigger:"blur"},{validator:this.validateRoleCode}],description:[{min:0,max:126,message:"长度不超过 126 个字符",trigger:"blur"}]},url:{add:"/sys/sysDepartRole/add",edit:"/sys/sysDepartRole/edit"}}},created:function(){},methods:{add:function(e){this.edit({},e)},edit:function(e,t){this.departId=t,this.model=Object.assign({},e),this.visible=!0},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),e.model.departId=e.departId,Object(l["h"])(s,e.model,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},validateRoleCode:function(e,t,a){if(/[\u4E00-\u9FA5]/g.test(t))a("部门角色编码不可输入汉字!");else{var s={tableName:"sys_depart_role",fieldName:"role_code",fieldVal:t,dataId:this.model.id};Object(i["m"])(s).then((function(e){e.success?a():a(e.message)}))}}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"593cc9e9",null);t["default"]=d.exports},a835:function(e,t,a){},ab93:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:"敏感词统计分析",width:1200,visible:e.visible,footer:null},on:{cancel:e.handleCancel}},[a("div",{staticClass:"statistics-container"},[a("div",{staticClass:"time-range-section"},[a("a-radio-group",{on:{change:e.handleTimeRangeChange},model:{value:e.timeRange,callback:function(t){e.timeRange=t},expression:"timeRange"}},[a("a-radio-button",{attrs:{value:"7"}},[e._v("最近7天")]),a("a-radio-button",{attrs:{value:"30"}},[e._v("最近30天")]),a("a-radio-button",{attrs:{value:"90"}},[e._v("最近90天")])],1),a("a-button",{staticStyle:{"margin-left":"16px"},attrs:{type:"primary",loading:e.loading},on:{click:e.loadStatistics}},[a("a-icon",{attrs:{type:"reload"}}),e._v("\n        刷新数据\n      ")],1)],1),e.overviewData?a("div",{staticClass:"overview-cards"},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-card",[a("a-statistic",{attrs:{title:"敏感词总数",value:e.overviewData.total_words,"value-style":{color:"#1890ff"}}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-card",[a("a-statistic",{attrs:{title:"启用敏感词",value:e.overviewData.enabled_words,"value-style":{color:"#52c41a"}}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-card",[a("a-statistic",{attrs:{title:"总命中次数",value:e.overviewData.total_hits,"value-style":{color:"#fa8c16"}}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-card",[a("a-statistic",{attrs:{title:"高危敏感词",value:e.overviewData.high_risk_words,"value-style":{color:"#f5222d"}}})],1)],1)],1)],1):e._e(),a("div",{staticClass:"charts-section"},[a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:12}},[a("a-card",{attrs:{title:"敏感词分类分布",loading:e.loading}},[a("div",{ref:"categoryChart",staticStyle:{height:"300px"}})])],1),a("a-col",{attrs:{span:12}},[a("a-card",{attrs:{title:"敏感词级别分布",loading:e.loading}},[a("div",{ref:"levelChart",staticStyle:{height:"300px"}})])],1)],1),a("a-row",{staticStyle:{"margin-top":"16px"},attrs:{gutter:16}},[a("a-col",{attrs:{span:24}},[a("a-card",{attrs:{title:"敏感词命中趋势",loading:e.loading}},[a("div",{ref:"trendChart",staticStyle:{height:"300px"}})])],1)],1)],1),a("div",{staticClass:"top-words-section"},[a("a-card",{attrs:{title:"热门敏感词 TOP 10",loading:e.loading}},[a("a-table",{attrs:{columns:e.topWordsColumns,dataSource:e.topWordsData,pagination:!1,size:"small",rowKey:"word"},scopedSlots:e._u([{key:"level",fn:function(t,s){return[a("a-tag",{attrs:{color:e.getLevelColor(s.level)}},[e._v("\n              "+e._s(s.level_text)+"\n            ")])]}},{key:"hitCount",fn:function(e){return[a("a-badge",{attrs:{count:e,"number-style":{backgroundColor:"#52c41a"}}})]}}])})],1)],1),a("div",{staticClass:"detail-stats-section"},[a("a-tabs",[a("a-tab-pane",{key:"category",attrs:{tab:"分类统计"}},[a("a-table",{attrs:{columns:e.categoryStatsColumns,dataSource:e.categoryStatsData,pagination:!1,size:"small",rowKey:"category"}})],1),a("a-tab-pane",{key:"level",attrs:{tab:"级别统计"}},[a("a-table",{attrs:{columns:e.levelStatsColumns,dataSource:e.levelStatsData,pagination:!1,size:"small",rowKey:"level"}})],1)],1)],1)])])},r=[],l=a("a34a"),i=a.n(l),o=a("313e"),n=a("db01"),c=a("89f2");function d(e,t){return f(e)||h(e,t)||m(e,t)||u()}function u(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}function h(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var a=[],s=!0,r=!1,l=void 0;try{for(var i,o=e[Symbol.iterator]();!(s=(i=o.next()).done);s=!0)if(a.push(i.value),t&&a.length===t)break}catch(n){r=!0,l=n}finally{try{s||null==o["return"]||o["return"]()}finally{if(r)throw l}}return a}}function f(e){if(Array.isArray(e))return e}function b(e,t,a,s,r,l,i){try{var o=e[l](i),n=o.value}catch(c){return void a(c)}o.done?t(n):Promise.resolve(n).then(s,r)}function v(e){return function(){var t=this,a=arguments;return new Promise((function(s,r){var l=e.apply(t,a);function i(e){b(l,s,r,i,o,"next",e)}function o(e){b(l,s,r,i,o,"throw",e)}i(void 0)}))}}var g={name:"SensitiveWordStatisticsModal",data:function(){var e=this;return{visible:!1,loading:!1,timeRange:"7",categoryDictOptions:[],overviewData:null,topWordsData:[],categoryStatsData:[],levelStatsData:[],hitTrendData:[],categoryChart:null,levelChart:null,trendChart:null,topWordsColumns:[{title:"排名",dataIndex:"rank",width:60,customRender:function(e,t,a){return a+1}},{title:"敏感词",dataIndex:"word",width:120},{title:"分类",dataIndex:"category",width:80,customRender:function(t){return e.getCategoryText(t)}},{title:"级别",dataIndex:"level",width:80,scopedSlots:{customRender:"level"}},{title:"命中次数",dataIndex:"hit_count",width:100,scopedSlots:{customRender:"hitCount"}}],categoryStatsColumns:[{title:"分类",dataIndex:"category",width:100,customRender:function(t){return e.getCategoryText(t)}},{title:"敏感词数量",dataIndex:"word_count",width:120},{title:"总命中次数",dataIndex:"total_hits",width:120},{title:"启用数量",dataIndex:"enabled_count",width:100},{title:"禁用数量",dataIndex:"disabled_count",width:100}],levelStatsColumns:[{title:"级别",dataIndex:"level_text",width:100},{title:"敏感词数量",dataIndex:"word_count",width:120},{title:"总命中次数",dataIndex:"total_hits",width:120},{title:"启用数量",dataIndex:"enabled_count",width:100}]}},created:function(){this.initDictConfig()},methods:{initDictConfig:function(){var e=v(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(c["d"])("sensitive_word_category");case 3:t=e.sent,t.success&&(this.categoryDictOptions=t.result),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0);case 10:case"end":return e.stop()}}),e,this,[[0,7]])})));function t(){return e.apply(this,arguments)}return t}(),getCategoryText:function(e){return e&&this.categoryDictOptions.length&&Object(c["a"])(this.categoryDictOptions,e)||e},show:function(){this.visible=!0,this.loadStatistics()},handleCancel:function(){this.visible=!1,this.destroyCharts()},handleTimeRangeChange:function(){this.loadStatistics()},loadStatistics:function(){var e=v(i.a.mark((function e(){var t,a,s,r,l,o,c,u=this;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.loading=!0,e.prev=1,e.next=4,Promise.all([Object(n["e"])(),Object(n["f"])(10),Object(n["b"])(),Object(n["d"])(),Object(n["c"])(parseInt(this.timeRange))]);case 4:t=e.sent,a=d(t,5),s=a[0],r=a[1],l=a[2],o=a[3],c=a[4],s.success&&(this.overviewData=s.result),r.success&&(this.topWordsData=r.result),l.success&&(this.categoryStatsData=l.result),o.success&&(this.levelStatsData=o.result),c.success&&(this.hitTrendData=c.result),this.$nextTick((function(){u.renderCharts()})),e.next=22;break;case 19:e.prev=19,e.t0=e["catch"](1),this.$message.error("加载统计数据失败："+e.t0.message);case 22:return e.prev=22,this.loading=!1,e.finish(22);case 25:case"end":return e.stop()}}),e,this,[[1,19,22,25]])})));function t(){return e.apply(this,arguments)}return t}(),renderCharts:function(){this.renderCategoryChart(),this.renderLevelChart(),this.renderTrendChart()},renderCategoryChart:function(){var e=this;if(this.$refs.categoryChart){this.categoryChart&&this.categoryChart.dispose(),this.categoryChart=o["a"](this.$refs.categoryChart);var t={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"分类分布",type:"pie",radius:["40%","70%"],data:this.categoryStatsData.map((function(t){return{name:e.getCategoryText(t.category),value:t.word_count}}))}]};this.categoryChart.setOption(t)}},renderLevelChart:function(){if(this.$refs.levelChart){this.levelChart&&this.levelChart.dispose(),this.levelChart=o["a"](this.$refs.levelChart);var e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"级别分布",type:"pie",radius:["40%","70%"],data:this.levelStatsData.map((function(e){return{name:e.level_text,value:e.word_count}}))}]};this.levelChart.setOption(e)}},renderTrendChart:function(){if(this.$refs.trendChart){this.trendChart&&this.trendChart.dispose(),this.trendChart=o["a"](this.$refs.trendChart);var e={tooltip:{trigger:"axis"},xAxis:{type:"category",data:this.hitTrendData.map((function(e){return e.hit_date}))},yAxis:{type:"value"},series:[{name:"命中次数",type:"line",data:this.hitTrendData.map((function(e){return e.hit_count})),smooth:!0}]};this.trendChart.setOption(e)}},destroyCharts:function(){this.categoryChart&&(this.categoryChart.dispose(),this.categoryChart=null),this.levelChart&&(this.levelChart.dispose(),this.levelChart=null),this.trendChart&&(this.trendChart.dispose(),this.trendChart=null)},getLevelColor:function(e){switch(e){case 1:return"green";case 2:return"orange";case 3:return"red";default:return"default"}}},beforeDestroy:function(){this.destroyCharts()}},y=g,C=(a("8573"),a("2877")),w=Object(C["a"])(y,s,r,!1,null,"eabaff56",null);t["default"]=w.exports},b3c4:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-modal",{attrs:{centered:"",title:e.title,width:1e3,visible:e.visible,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchQuery(t)}}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:10}},[a("a-form-item",{attrs:{label:"用户账号"}},[a("a-input",{attrs:{placeholder:"请输入用户账号"},model:{value:e.queryParam.username,callback:function(t){e.$set(e.queryParam,"username",t)},expression:"queryParam.username"}})],1)],1),a("a-col",{attrs:{span:8}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)])],1)],1)],1),a("div",[a("a-table",{attrs:{size:"small",bordered:"",rowKey:"id",columns:e.columns1,dataSource:e.dataSource1,pagination:e.ipagination,loading:e.loading,scroll:{y:240},rowSelection:{selectedRowKeys:e.selectedRowKeys,onSelectAll:e.onSelectAll,onSelect:e.onSelect,onChange:e.onSelectChange}},on:{change:e.handleTableChange}})],1)])],1)},r=[],l=a("ca00"),i=a("0fea"),o={name:"SelectUserModal",data:function(){return{title:"添加已有用户",names:[],visible:!1,placement:"right",description:"",queryParam:{},columns1:[{title:"#",dataIndex:"",key:"rowIndex",width:50,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"用户账号",align:"center",width:100,dataIndex:"username"},{title:"用户名称",align:"center",width:100,dataIndex:"realname"},{title:"性别",align:"center",width:100,dataIndex:"sex_dictText"},{title:"电话",align:"center",width:100,dataIndex:"phone"},{title:"部门",align:"center",width:150,dataIndex:"orgCode"}],columns2:[{title:"用户账号",align:"center",dataIndex:"username"},{title:"用户名称",align:"center",dataIndex:"realname"},{title:"操作",dataIndex:"action",align:"center",width:100,scopedSlots:{customRender:"action"}}],dataSource1:[],dataSource2:[],ipagination:{current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},isorter:{column:"createTime",order:"desc"},loading:!1,selectedRowKeys:[],selectedRows:[],url:{list:"/sys/user/list"}}},created:function(){this.loadData()},methods:{searchQuery:function(){this.loadData(1)},searchReset:function(){this.queryParam={},this.loadData(1)},handleCancel:function(){this.visible=!1},handleOk:function(){this.dataSource2=this.selectedRowKeys,this.$emit("selectFinished",this.dataSource2),this.visible=!1},add:function(){this.visible=!0},loadData:function(e){var t=this;1===e&&(this.ipagination.current=1);var a=this.getQueryParams();Object(i["c"])(this.url.list,a).then((function(e){e.success&&(t.dataSource1=e.result.records,t.ipagination.total=e.result.total)}))},getQueryParams:function(){var e=Object.assign({},this.queryParam,this.isorter);return e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,Object(l["d"])(e)},getQueryField:function(){},onSelectAll:function(e,t,a){if(!0===e)for(var s=0;s<a.length;s++)this.dataSource2.push(a[s]);else for(var r=0;r<a.length;r++)this.dataSource2.splice(this.dataSource2.indexOf(a[r]),1)},onSelect:function(e,t){if(!0===t)this.dataSource2.push(e);else{var a=this.dataSource2.indexOf(e);a>=0&&this.dataSource2.splice(this.dataSource2.indexOf(e),1)}},onSelectChange:function(e,t){this.selectedRowKeys=e,this.selectionRows=t},onClearSelected:function(){this.selectedRowKeys=[],this.selectionRows=[]},handleDelete:function(e){this.dataSource2.splice(this.dataSource2.indexOf(e),1)},handleTableChange:function(e,t,a){Object.keys(a).length>0&&(this.isorter.column=a.field,this.isorter.order="ascend"==a.order?"asc":"desc"),this.ipagination=e,this.loadData()}}},n=o,c=(a("7f76"),a("2877")),d=Object(c["a"])(n,s,r,!1,null,"3f080a9a",null);t["default"]=d.exports},b4690:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("a-modal",{attrs:{width:e.modalWidth,visible:e.visible,title:"数据对比窗口",confirmLoading:e.confirmLoading,cancelText:"取消"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{staticClass:"form",attrs:{form:e.form},on:{submit:e.handleSubmit}},[a("a-row",{staticClass:"form-row",attrs:{gutter:24}},[a("a-col",{attrs:{md:12,sm:8}},[a("a-form-item",{attrs:{label:"数据库表名","label-col":{span:6},"wrapper-col":{span:15}}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dataTale",{}],expression:"[ 'dataTale', {}]"}],attrs:{placeholder:"请输入数据库表名",disabled:""},on:{blur:e.handleTableBlur}})],1)],1),a("a-col",{attrs:{md:12,sm:8}},[a("a-form-item",{attrs:{label:"数据ID","label-col":{span:5},"wrapper-col":{span:15}}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dataId",{}],expression:"[ 'dataId', {}]"}],attrs:{placeholder:"请输入数据ID",disabled:""},on:{blur:e.handleIdBlur}})],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:24}},[a("a-col",{attrs:{md:12,sm:8}},[a("a-form-item",{attrs:{label:"版本号1","label-col":{span:6},"wrapper-col":{span:15}}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["dataVersion1",{}],expression:"[ 'dataVersion1', {}]"}],attrs:{placeholder:"请选择版本号"},on:{change:e.handleChange1}},e._l(e.DataVersionList,(function(t,s){return a("a-select-option",{key:s.toString(),attrs:{value:t.id}},[e._v("\n                  "+e._s(t.dataVersion)+"\n                ")])})),1)],1)],1),a("a-col",{attrs:{md:12,sm:8}},[a("a-form-item",{attrs:{label:"版本号2","label-col":{span:5},"wrapper-col":{span:15}}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["dataVersion2",{}],expression:"[ 'dataVersion2', {}]"}],attrs:{placeholder:"请选择版本号"},on:{change:e.handleChange2}},e._l(e.DataVersionList,(function(t,s){return a("a-select-option",{key:s.toString(),attrs:{value:t.id}},[e._v("\n                  "+e._s(t.dataVersion)+"\n                ")])})),1)],1)],1)],1)],1)],1),a("data-log-compare-modal",{ref:"modal",on:{ok:e.modalFormOk}})],1)],1)},r=[],l=a("0fea"),i=a("fcbc"),o={name:"DataLogModal",components:{DataLogCompareModal:i["default"]},dataId1:"",dataId2:"",dataTable1:"",dataID3:"",data:function(){return{modalWidth:700,modaltoggleFlag:!0,confirmDirty:!1,title:"操作",visible:!1,model:{},confirmLoading:!1,headers:{},form:this.$form.createForm(this),url:{queryDataVerListUrl:"/sys/dataLog/queryDataVerList"},DataVersionList:[]}},created:function(){},methods:{addModal:function(e){var t=this,a=e[0].dataTable,s=e[0].dataId,r=e[0].dataVersion,l=e[1].dataVersion;this.dataId1=e[0].id,this.dataId2=e[1].id,this.dataTable1=e[0].dataTable,this.dataID3=e[0].dataId,this.initDataVersionList(),this.form.resetFields(),this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue({dataTale:a,dataId:s,dataVersion1:r,dataVersion2:l})}))},handleOk:function(){this.close(),this.$refs.modal.compareModal(this.dataId1,this.dataId2),this.$refs.modal.title="数据比较"},handleCancel:function(){this.close()},handleSubmit:function(){},close:function(){this.$emit("close"),this.visible=!1,this.disableSubmit=!1},modalFormOk:function(){},initDataVersionList:function(){var e=this,t=this;Object(l["c"])(t.url.queryDataVerListUrl,{dataTable:this.dataTable1,dataId:this.dataID3}).then((function(t){t.success?e.DataVersionList=t.result:(e.DataVersionList=[],e.dataId1="",e.dataId2="")}))},handleChange1:function(e){this.dataId1=e},handleChange2:function(e){this.dataId2=e},handleTableBlur:function(e){this.dataTable1=e.target.value,this.initDataVersionList()},handleIdBlur:function(e){this.dataID3=e.target.value,this.initDataVersionList()}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"499ee242",null);t["default"]=d.exports},b90e:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:e.title,width:e.drawerWidth,visible:e.visible,confirmLoading:e.confirmLoading},on:{close:e.handleCancel}},[a("div",{style:{width:"100%",border:"1px solid #e9e9e9",padding:"10px 16px",background:"#fff"}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{label:"菜单类型",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-radio-group",{on:{change:e.onChangeMenuType},model:{value:e.model.menuType,callback:function(t){e.$set(e.model,"menuType",t)},expression:"model.menuType"}},[a("a-radio",{attrs:{value:0}},[e._v("一级菜单")]),a("a-radio",{attrs:{value:1}},[e._v("子菜单")]),a("a-radio",{attrs:{value:2}},[e._v("按钮/权限")])],1)],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:e.menuLabel,prop:"name",hasFeedback:""}},[a("a-input",{attrs:{placeholder:"请输入菜单名称",readOnly:e.disableSubmit},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:0!=e.model.menuType,expression:"model.menuType!=0"}],attrs:{label:"上级菜单",labelCol:e.labelCol,wrapperCol:e.wrapperCol,"validate-status":e.validateStatus,hasFeedback:!0,required:!0}},[a("span",{attrs:{slot:"help"},slot:"help"},[e._v(e._s("error"==e.validateStatus?"请选择上级菜单":"  "))]),a("a-tree-select",{staticStyle:{width:"100%"},attrs:{dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.treeData,placeholder:"请选择父级菜单",disabled:e.disableSubmit},on:{change:e.handleParentIdChange},model:{value:e.model.parentId,callback:function(t){e.$set(e.model,"parentId",t)},expression:"model.parentId"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"url",label:"菜单路径"}},[a("a-input",{attrs:{placeholder:"请输入菜单路径",readOnly:e.disableSubmit},model:{value:e.model.url,callback:function(t){e.$set(e.model,"url",t)},expression:"model.url"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"component",label:"前端组件"}},[a("a-input",{attrs:{placeholder:"请输入前端组件",readOnly:e.disableSubmit},model:{value:e.model.component,callback:function(t){e.$set(e.model,"component",t)},expression:"model.component"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:0==e.model.menuType,expression:"model.menuType==0"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"默认跳转地址"}},[a("a-input",{attrs:{placeholder:"请输入路由参数 redirect",readOnly:e.disableSubmit},model:{value:e.model.redirect,callback:function(t){e.$set(e.model,"redirect",t)},expression:"model.redirect"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.show,expression:"!show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"perms",label:"授权标识"}},[a("a-input",{attrs:{placeholder:"请输入授权标识, 如: user:list",readOnly:e.disableSubmit},model:{value:e.model.perms,callback:function(t){e.$set(e.model,"perms",t)},expression:"model.perms"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.show,expression:"!show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"授权策略"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择授权策略",type:"radio",dictCode:"global_perms_type"},model:{value:e.model.permsType,callback:function(t){e.$set(e.model,"permsType",t)},expression:"model.permsType"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:!e.show,expression:"!show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择状态",type:"radio",dictCode:"valid_status"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"菜单图标"}},[a("a-input",{attrs:{placeholder:"点击选择图标",readOnly:e.disableSubmit},model:{value:e.model.icon,callback:function(t){e.$set(e.model,"icon",t)},expression:"model.icon"}},[a("a-icon",{attrs:{slot:"addonAfter",type:"setting"},on:{click:e.selectIcons},slot:"addonAfter"})],1)],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"sortNo",label:"排序"}},[a("a-input-number",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入菜单排序",readOnly:e.disableSubmit},model:{value:e.model.sortNo,callback:function(t){e.$set(e.model,"sortNo",t)},expression:"model.sortNo"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"是否路由菜单"}},[a("a-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"},model:{value:e.model.route,callback:function(t){e.$set(e.model,"route",t)},expression:"model.route"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"隐藏路由"}},[a("a-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"},model:{value:e.model.hidden,callback:function(t){e.$set(e.model,"hidden",t)},expression:"model.hidden"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"是否缓存路由"}},[a("a-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"},model:{value:e.model.keepAlive,callback:function(t){e.$set(e.model,"keepAlive",t)},expression:"model.keepAlive"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"聚合路由"}},[a("a-switch",{attrs:{checkedChildren:"是",unCheckedChildren:"否"},model:{value:e.model.alwaysShow,callback:function(t){e.$set(e.model,"alwaysShow",t)},expression:"model.alwaysShow"}})],1),a("a-form-model-item",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"打开方式"}},[a("a-switch",{attrs:{checkedChildren:"外部",unCheckedChildren:"内部"},model:{value:e.model.internalOrExternal,callback:function(t){e.$set(e.model,"internalOrExternal",t)},expression:"model.internalOrExternal"}})],1)],1),a("icons",{attrs:{iconChooseVisible:e.iconChooseVisible},on:{choose:e.handleIconChoose,close:e.handleIconCancel}})],1),a("a-row",{style:{textAlign:"right"}},[a("a-button",{style:{marginRight:"8px"},on:{click:e.handleCancel}},[e._v("\n        关闭\n      ")]),a("a-button",{attrs:{disabled:e.disableSubmit,type:"primary"},on:{click:e.handleOk}},[e._v("确定")])],1)],1)])},r=[],l=a("4ec3"),i=a("11cb"),o={name:"PermissionModal",components:{Icons:i["default"]},data:function(){return{drawerWidth:700,treeData:[],title:"操作",visible:!1,disableSubmit:!1,model:{},show:!0,menuLabel:"菜单名称",labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,iconChooseVisible:!1,validateStatus:""}},computed:{validatorRules:function(){return{name:[{required:!0,message:"请输入菜单标题!"}],component:[{required:this.show,message:"请输入前端组件!"}],url:[{required:this.show,message:"请输入菜单路径!"}],permsType:[{required:!0,message:"请输入授权策略!"}],perms:[{required:!1,message:"请输入授权标识!"},{validator:this.validatePerms}]}}},created:function(){},methods:{loadTree:function(){var e=this;Object(l["L"])().then((function(t){if(t.success){e.treeData=[];for(var a=t.result.treeList,s=0;s<a.length;s++){var r=a[s];r.isLeaf=r.leaf,e.treeData.push(r)}}}))},add:function(){this.edit({status:"1",permsType:"1",sortNo:1,route:!0,menuType:0})},edit:function(e){this.resetScreenSize(),this.model=Object.assign({},e),this.show=2!=e.menuType,this.menuLabel=2==e.menuType?"按钮/权限":"菜单名称",this.visible=!0,this.loadTree()},close:function(){this.$emit("close"),this.disableSubmit=!1,this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){return!!a&&(1!=e.model.menuType&&2!=e.model.menuType||e.model.parentId?(t.validateStatus="success",t.confirmLoading=!0,s=e.model.id?Object(l["p"])(e.model):Object(l["c"])(e.model),void s.then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))):(t.validateStatus="error",void t.$message.error("请检查你填的类型以及信息是否正确！")));var s}))},handleCancel:function(){this.close()},validateNumber:function(e,t,a){!t||new RegExp(/^[0-9]*[1-9][0-9]*$/).test(t)?a():a("请输入正整数!")},validatePerms:function(e,t,a){if(t&&t.length>0){var s={tableName:"sys_permission",fieldName:"perms",fieldVal:t,dataId:this.model.id};Object(l["m"])(s).then((function(e){e.success?a():a("授权标识已存在!")}))}else a()},onChangeMenuType:function(e){var t=this;2==this.model.menuType?(this.show=!1,this.menuLabel="按钮/权限"):(this.show=!0,this.menuLabel="菜单名称"),this.$nextTick((function(){t.$refs.form.validateField(["url","component"])}))},selectIcons:function(){this.iconChooseVisible=!0},handleIconCancel:function(){this.iconChooseVisible=!1},handleIconChoose:function(e){this.model.icon=e,this.iconChooseVisible=!1},resetScreenSize:function(){var e=document.body.clientWidth;this.drawerWidth=e<500?e:700},handleParentIdChange:function(e){this.validateStatus=e?"success":"error"}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"020bf494",null);t["default"]=d.exports},b940:function(e,t,a){},c1af:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,ok:!1,visible:e.visible,confirmLoading:e.confirmLoading,okButtonProps:{props:{disabled:e.disableSubmit}},cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"机构名称",prop:"departName",hidden:!1,hasFeedback:""}},[a("a-input",{attrs:{id:"departName",placeholder:"请输入机构/部门名称"},model:{value:e.model.departName,callback:function(t){e.$set(e.model,"departName",t)},expression:"model.departName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,hidden:e.seen,label:"上级部门",hasFeedback:""}},[a("a-tree-select",{staticStyle:{width:"100%"},attrs:{dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.departTree,placeholder:"请选择上级部门",disabled:e.condition},model:{value:e.model.parentId,callback:function(t){e.$set(e.model,"parentId",t)},expression:"model.parentId"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"机构类型"}},[e.seen?[a("a-radio-group",{attrs:{placeholder:"请选择机构类型"},model:{value:e.model.orgCategory,callback:function(t){e.$set(e.model,"orgCategory",t)},expression:"model.orgCategory"}},[a("a-radio",{attrs:{value:"1"}},[e._v("\n              公司\n            ")])],1)]:[a("a-radio-group",{attrs:{placeholder:"请选择机构类型"},model:{value:e.model.orgCategory,callback:function(t){e.$set(e.model,"orgCategory",t)},expression:"model.orgCategory"}},[a("a-radio",{attrs:{value:"2"}},[e._v("\n              部门\n            ")]),a("a-radio",{attrs:{value:"3"}},[e._v("\n              岗位\n            ")])],1)]],2),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"mobile",label:"电话"}},[a("a-input",{attrs:{placeholder:"请输入电话"},model:{value:e.model.mobile,callback:function(t){e.$set(e.model,"mobile",t)},expression:"model.mobile"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"传真"}},[a("a-input",{attrs:{placeholder:"请输入传真"},model:{value:e.model.fax,callback:function(t){e.$set(e.model,"fax",t)},expression:"model.fax"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"地址"}},[a("a-input",{attrs:{placeholder:"请输入地址"},model:{value:e.model.address,callback:function(t){e.$set(e.model,"address",t)},expression:"model.address"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"排序"}},[a("a-input-number",{model:{value:e.model.departOrder,callback:function(t){e.$set(e.model,"departOrder",t)},expression:"model.departOrder"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"备注"}},[a("a-textarea",{attrs:{placeholder:"请输入备注"},model:{value:e.model.memo,callback:function(t){e.$set(e.model,"memo",t)},expression:"model.memo"}})],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("4ec3"),o=(a("88bc"),a("261e")),n={name:"SysDepartModal",components:{ATextarea:o["a"]},data:function(){return{departTree:[],orgTypeData:[],phoneWarning:"",departName:"",title:"操作",seen:!1,visible:!1,condition:!0,disableSubmit:!1,model:{},defaultModel:{departOrder:0,orgCategory:"1"},menuhidden:!1,menuusing:!0,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{departName:[{required:!0,message:"请输入机构/部门名称!"}],orgCode:[{required:!0,message:"请输入机构编码!"}],mobile:[{validator:this.validateMobile}],orgCategory:[{required:!0,message:"请输入机构类型!"}]},url:{add:"/sys/sysDepart/add"},dictDisabled:!0}},created:function(){},methods:{loadTreeData:function(){var e=this;Object(i["G"])().then((function(t){if(t.success){e.departTree=[];for(var a=0;a<t.result.length;a++){var s=t.result[a];e.departTree.push(s)}}}))},add:function(e){e?(this.seen=!1,this.dictDisabled=!1):(this.seen=!0,this.dictDisabled=!0),this.edit(e)},edit:function(e){this.visible=!0,this.model=Object.assign({},this.defaultModel,e),this.loadTreeData(),this.model.parentId=null!=e?e.toString():null,this.seen?this.model.orgCategory="1":this.model.orgCategory="2"},close:function(){this.$emit("close"),this.disableSubmit=!1,this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0,Object(l["h"])(e.url.add,e.model,"post").then((function(e){e.success?(t.$message.success(e.message),t.loadTreeData(),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},validateMobile:function(e,t,a){!t||new RegExp(/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/).test(t)?a():a("您的手机号码格式不正确!")}}},c=n,d=a("2877"),u=Object(d["a"])(c,s,r,!1,null,"13df8b05",null);t["default"]=u.exports},c5d7:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:"敏感词检测测试",width:900,visible:e.visible,footer:null},on:{cancel:e.handleCancel}},[a("div",{staticClass:"sensitive-test-container"},[a("div",{staticClass:"test-input-section"},[a("h4",[e._v("输入测试文本")]),a("a-textarea",{attrs:{placeholder:"请输入要检测的文本内容...",rows:6,maxLength:5e3,showCount:""},model:{value:e.testText,callback:function(t){e.testText=t},expression:"testText"}}),a("div",{staticClass:"test-actions"},[a("a-button",{attrs:{type:"primary",loading:e.checkLoading},on:{click:e.handleCheck}},[a("a-icon",{attrs:{type:"search"}}),e._v("\n          检测敏感词\n        ")],1),a("a-button",{attrs:{loading:e.replaceLoading,disabled:!e.checkResult||!e.checkResult.hasSensitiveWord},on:{click:e.handleReplace}},[a("a-icon",{attrs:{type:"edit"}}),e._v("\n          替换敏感词\n        ")],1),a("a-button",{on:{click:e.handleClear}},[a("a-icon",{attrs:{type:"clear"}}),e._v("\n          清空\n        ")],1)],1)],1),e.checkResult?a("div",{staticClass:"test-result-section"},[a("h4",[e._v("检测结果")]),a("div",{staticClass:"result-summary"},[a("a-alert",{attrs:{type:e.checkResult.hasSensitiveWord?"error":"success",message:e.checkResult.message,description:e.checkResult.hasSensitiveWord?"发现 "+e.checkResult.sensitiveWords.length+" 个敏感词":"文本内容安全",showIcon:""}})],1),e.checkResult.hasSensitiveWord&&e.checkResult.sensitiveWords.length>0?a("div",{staticClass:"sensitive-words-list"},[a("h5",[e._v("检测到的敏感词：")]),a("div",{staticClass:"words-tags"},e._l(e.checkResult.sensitiveWords,(function(t,s){return a("a-tag",{key:s,staticClass:"sensitive-word-tag",attrs:{color:"red"}},[e._v("\n            "+e._s(t)+"\n          ")])})),1)]):e._e(),e.replaceResult?a("div",{staticClass:"replace-result"},[a("h5",[e._v("替换后的文本：")]),a("div",{staticClass:"replaced-text"},[a("a-textarea",{staticClass:"replaced-textarea",attrs:{value:e.replaceResult,rows:4,readonly:""}}),a("a-button",{staticClass:"copy-btn",attrs:{type:"link",size:"small"},on:{click:e.copyReplacedText}},[a("a-icon",{attrs:{type:"copy"}}),e._v("\n            复制\n          ")],1)],1)]):e._e()]):e._e(),a("div",{staticClass:"quick-test-section"},[a("h4",[e._v("快速测试示例")]),a("div",{staticClass:"example-buttons"},e._l(e.testExamples,(function(t,s){return a("a-button",{key:s,staticClass:"example-btn",attrs:{size:"small"},on:{click:function(a){return e.loadExample(t.text)}}},[e._v("\n          "+e._s(t.name)+"\n        ")])})),1)]),e.testStats?a("div",{staticClass:"test-stats"},[a("h4",[e._v("检测统计")]),a("a-row",{attrs:{gutter:16}},[a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"检测次数",value:e.testStats.testCount}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"发现敏感词",value:e.testStats.sensitiveCount}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"安全文本",value:e.testStats.safeCount}})],1),a("a-col",{attrs:{span:6}},[a("a-statistic",{attrs:{title:"检测成功率",value:e.testStats.successRate,suffix:"%"}})],1)],1)],1):e._e()])])},r=[],l=a("db01"),i={name:"SensitiveWordTestModal",data:function(){return{visible:!1,testText:"",checkResult:null,replaceResult:"",checkLoading:!1,replaceLoading:!1,testStats:{testCount:0,sensitiveCount:0,safeCount:0,successRate:100},testExamples:[{name:"正常文本",text:"这是一段正常的文本内容，没有任何问题。"},{name:"包含敏感词",text:"这段文本可能包含一些敏感词汇，需要进行检测。"},{name:"长文本测试",text:"这是一段比较长的文本内容，用来测试系统对长文本的处理能力。在实际应用中，用户可能会输入各种长度的文本，系统需要能够快速准确地检测出其中的敏感词汇，并给出相应的处理建议。"},{name:"混合内容",text:"这段文本包含中文、English、数字123、特殊符号@#$%，测试多语言混合内容的检测效果。"}]}},methods:{show:function(){this.visible=!0,this.resetForm()},handleCancel:function(){this.visible=!1},resetForm:function(){this.testText="",this.checkResult=null,this.replaceResult=""},handleCheck:function(){var e=this;this.testText.trim()?(this.checkLoading=!0,this.checkResult=null,this.replaceResult="",Object(l["a"])(this.testText).then((function(t){t.success?(e.checkResult=t.result,e.updateTestStats(t.result.hasSensitiveWord)):e.$message.error("检测失败："+t.message)})).catch((function(t){e.$message.error("检测失败："+t.message)})).finally((function(){e.checkLoading=!1}))):this.$message.warning("请输入要检测的文本")},handleReplace:function(){var e=this;this.testText.trim()?(this.replaceLoading=!0,Object(l["i"])(this.testText,"*").then((function(t){t.success?e.replaceResult=t.result:e.$message.error("替换失败："+t.message)})).catch((function(t){e.$message.error("替换失败："+t.message)})).finally((function(){e.replaceLoading=!1}))):this.$message.warning("请输入要替换的文本")},handleClear:function(){this.resetForm()},loadExample:function(e){this.testText=e,this.checkResult=null,this.replaceResult=""},copyReplacedText:function(){var e=this;this.replaceResult&&this.$copyText(this.replaceResult).then((function(){e.$message.success("已复制到剪贴板")})).catch((function(){e.$message.error("复制失败")}))},updateTestStats:function(e){this.testStats.testCount++,e?this.testStats.sensitiveCount++:this.testStats.safeCount++,this.testStats.successRate=Math.round(this.testStats.safeCount/this.testStats.testCount*100)}}},o=i,n=(a("d62c"),a("2877")),c=Object(n["a"])(o,s,r,!1,null,"326d57de",null);t["default"]=c.exports},c63e:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{width:e.modalWidth,visible:e.visible,title:"部门搜索",confirmLoading:e.confirmLoading,cancelText:"关闭",wrapClassName:"ant-modal-cust-warp"},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"上级部门"}},[a("a-tree",{attrs:{multiple:"",treeCheckable:"tree",checkable:"",expandedKeys:e.expandedKeysss,checkedKeys:e.checkedKeys,allowClear:"true",checkStrictly:!0,dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.departTree,placeholder:"请选择上级部门"},on:{expand:e.onExpand,check:e.onCheck}})],1)],1)]],2)},r=[],l=a("88bc"),i=a.n(l),o=a("0fea"),n=a("4ec3"),c={name:"DepartWindow",components:{},data:function(){return{checkedKeys:[],expandedKeysss:[],userId:"",model:{},userDepartModel:{userId:"",departIdList:[]},departList:[],modalWidth:400,departTree:[],title:"操作",visible:!1,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,headers:{},form:this.$form.createForm(this),url:{userId:"/sys/user/generateUserId"}}},methods:{add:function(e,t){this.checkedKeys=e,this.userId=t,this.edit({})},edit:function(e){var t=this;this.departList=[],this.queryDepartTree(),this.form.resetFields(),this.visible=!0,this.model=Object.assign({},e);var a=i()(this.model,"id","userId","departIdList");this.$nextTick((function(){t.form.setFieldsValue(a)}))},close:function(){this.$emit("close"),this.visible=!1,this.departList=[],this.checkedKeys=[]},handleSubmit:function(){var e=this,t=this;this.form.validateFields((function(a){if(!a)if(t.confirmLoading=!0,null==e.userId)Object(o["c"])(e.url.userId).then((function(a){if(a.success){var s={userId:a.result,departIdList:e.departList};t.$emit("ok",s)}})).finally((function(){t.departList=[],t.confirmLoading=!1,t.close()}));else{var s={userId:e.userId,departIdList:e.departList};t.departList=[],t.$emit("ok",s),t.confirmLoading=!1,t.close()}}))},handleCancel:function(){this.close()},onCheck:function(e,t){this.departList=[],this.checkedKeys=e.checked;for(var a=t.checkedNodes,s=0;s<a.length;s++){var r=a[s].data.props,l={key:"",value:"",title:""};l.key=r.value,l.value=r.value,l.title=r.title,this.departList.push(l)}},queryDepartTree:function(){var e=this;Object(n["G"])().then((function(t){if(t.success&&(e.departTree=t.result,e.checkedKeys&&e.checkedKeys.length>0)){var a=[],s=t.result;s&&s.length>0&&(s.forEach((function(e){a.push(e.key)})),e.expandedKeysss=a)}}))},onExpand:function(e){this.expandedKeysss=e},modalFormOk:function(){}}},d=c,u=(a("47fd"),a("2877")),m=Object(u["a"])(d,s,r,!1,null,"56367c89",null);t["default"]=m.exports},c8ad:function(e,t,a){},c9af:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",e._b({ref:"form",attrs:{model:e.model,rules:e.validatorRules}},"a-form-model",e.layout,!1),[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"敏感词",prop:"word"}},[a("a-input",{attrs:{placeholder:"请输入敏感词",disabled:!!e.model.id},model:{value:e.model.word,callback:function(t){e.$set(e.model,"word",t)},expression:"model.word"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"分类",prop:"category"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择分类",dictCode:"sensitive_word_category"},model:{value:e.model.category,callback:function(t){e.$set(e.model,"category",t)},expression:"model.category"}})],1)],1)],1),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"敏感级别",prop:"level"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择级别",dictCode:"sensitive_word_level"},model:{value:e.model.level,callback:function(t){e.$set(e.model,"level",t)},expression:"model.level"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"状态",prop:"status"}},[a("j-dict-select-tag",{attrs:{placeholder:"请选择状态",dictCode:"valid_status"},model:{value:e.model.status,callback:function(t){e.$set(e.model,"status",t)},expression:"model.status"}})],1)],1)],1),e.model.id?a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"命中次数"}},[a("a-input",{attrs:{disabled:""},model:{value:e.model.hitCount,callback:function(t){e.$set(e.model,"hitCount",t)},expression:"model.hitCount"}})],1)],1),a("a-col",{attrs:{span:12}},[a("a-form-model-item",{attrs:{label:"来源"}},[a("a-input",{attrs:{disabled:""},model:{value:e.model.source,callback:function(t){e.$set(e.model,"source",t)},expression:"model.source"}})],1)],1)],1):e._e(),a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"备注",prop:"remark"}},[a("a-textarea",{attrs:{placeholder:"请输入备注",rows:3},model:{value:e.model.remark,callback:function(t){e.$set(e.model,"remark",t)},expression:"model.remark"}})],1)],1)],1),e.model.id?e._e():a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-form-model-item",{attrs:{label:"敏感词检测"}},[a("div",{staticClass:"sensitive-check-area"},[a("a-button",{attrs:{type:"primary",loading:e.checkLoading,size:"small"},on:{click:e.handleCheckWord}},[e._v("\n                检测敏感词\n              ")]),e.checkResult?a("span",{staticClass:"check-result",class:e.checkResult.hasSensitiveWord?"error":"success"},[a("a-icon",{attrs:{type:e.checkResult.hasSensitiveWord?"close-circle":"check-circle"}}),e._v("\n                "+e._s(e.checkResult.message)+"\n              ")],1):e._e()],1)])],1)],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("db01"),o=a("88bc"),n=a.n(o),c={name:"SensitiveWordModal",components:{},data:function(){return{layout:{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}}},title:"操作",visible:!1,model:{},confirmLoading:!1,checkLoading:!1,checkResult:null,form:this.$form.createForm(this),validatorRules:{word:[{required:!0,message:"请输入敏感词!"},{min:1,max:100,message:"敏感词长度在1到100个字符之间!"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/,message:"敏感词只能包含中文、英文、数字、下划线、连字符和空格!"}],category:[{required:!0,message:"请选择分类!"}],level:[{required:!0,message:"请选择敏感级别!"}],status:[{required:!0,message:"请选择状态!"}]},url:{add:"/sys/sensitiveWord/add",edit:"/sys/sensitiveWord/edit",queryById:"/sys/sensitiveWord/queryById"}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.checkResult=null,this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(n()(t.model,"word","category","level","status","hitCount","source","remark"))}))},detail:function(e){var t=this;this.model=Object.assign({},e),this.checkResult=null,this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(n()(t.model,"word","category","level","status","hitCount","source","remark"))}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(a){t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),Object(l["h"])(s,e.model,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok")):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},handleCheckWord:function(){var e=this;this.model.word?(this.checkLoading=!0,this.checkResult=null,Object(i["a"])(this.model.word).then((function(t){t.success?e.checkResult=t.result:e.$message.error("检测失败："+t.message)})).catch((function(t){e.$message.error("检测失败："+t.message)})).finally((function(){e.checkLoading=!1}))):this.$message.warning("请先输入敏感词")},popupCallback:function(e,t){this.model=Object.assign(this.model,t)}}},d=c,u=(a("6e8d"),a("2877")),m=Object(u["a"])(d,s,r,!1,null,"3526df12",null);t["default"]=m.exports},cb6b:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{staticClass:"j-address-list-right-card-box",attrs:{loading:e.cardLoading,bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form-model",{attrs:{layout:"inline",model:e.queryParam}},[a("a-row",{attrs:{gutter:10}},[a("a-col",{attrs:{md:6,sm:12}},[a("a-form-model-item",{staticStyle:{"margin-left":"8px"},attrs:{label:"姓名",prop:"realname"}},[a("a-input",{attrs:{placeholder:"请输入姓名查询"},model:{value:e.queryParam.realname,callback:function(t){e.$set(e.queryParam,"realname",t)},expression:"queryParam.realname"}})],1)],1),a("a-col",{attrs:{md:6,sm:12}},[a("a-form-model-item",{staticStyle:{"margin-left":"8px"},attrs:{label:"工号",prop:"workNo"}},[a("a-input",{attrs:{placeholder:"请输入工号查询"},model:{value:e.queryParam.workNo,callback:function(t){e.$set(e.queryParam,"workNo",t)},expression:"queryParam.workNo"}})],1)],1),a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-col",{attrs:{md:6,sm:24}},[a("a-button",{staticStyle:{"margin-left":"18px"},attrs:{type:"primary",icon:"search"},on:{click:e.searchQuery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")])],1)],1)],1)],1)],1),a("a-table",{ref:"table",attrs:{size:"middle",bordered:"",rowKey:"userId",pagination:e.ipagination,columns:e.columns,dataSource:e.dataSource,loading:e.loading},on:{change:e.handleTableChange}})],1)},r=[],l=a("0fea"),i=a("b65a");function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function n(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){c(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var d={name:"AddressListRight",mixins:[i["a"]],components:{},props:["value"],data:function(){var e=this;return{description:"用户信息",cardLoading:!0,positionInfo:{},columns:[{title:"#",key:"rowIndex",dataIndex:"",width:60,align:"center",customRender:function(e,t,a){return parseInt(a)+1}},{title:"姓名",width:"15%",align:"center",dataIndex:"realname"},{title:"工号",width:"15%",align:"center",dataIndex:"workNo"},{title:"部门",width:"20%",align:"center",dataIndex:"departName"},{title:"职务",width:"15%",align:"center",dataIndex:"post",customRender:function(t){return(t||"").split(",").map((function(t){return e.positionInfo[t]?e.positionInfo[t]:t})).join(",")}},{title:"手机",width:"15%",align:"center",dataIndex:"telephone"},{title:"公司邮箱",width:"15%",align:"center",dataIndex:"email"}],url:{list:"/sys/user/queryByOrgCodeForAddressList",listByPosition:"/sys/position/list"}}},watch:{value:{immediate:!0,handler:function(e){this.dataSource=[],this.loadData(1,e)}}},created:function(){this.queryPositionInfo()},methods:{loadData:function(e,t){var a=this;this.loading=!0,1===e&&(this.ipagination.current=1),t?Object(l["c"])(this.url.list,n({orgCode:t},this.getQueryParams())).then((function(e){e.success&&(a.dataSource=e.result.records,a.ipagination.total=e.result.total)})).finally((function(){a.loading=!1,a.cardLoading=!1})):Object(l["c"])(this.url.list,n({},this.getQueryParams())).then((function(e){e.success&&(a.dataSource=e.result.records,a.ipagination.total=e.result.total)})).finally((function(){a.loading=!1,a.cardLoading=!1}))},searchQuery:function(){this.loadData(1,this.value)},searchReset:function(){this.queryParam={},this.loadData(1,this.value)},handleTableChange:function(e,t,a){Object.keys(a).length>0&&(this.isorter.column=a.field,this.isorter.order="ascend"===a.order?"asc":"desc"),this.ipagination=e,this.loadData(null,this.value)},queryPositionInfo:function(){var e=this;Object(l["c"])(this.url.listByPosition,{pageSize:99999}).then((function(t){if(t.success){var a={};t.result.records.forEach((function(e){a[e["code"]]=e["name"]})),e.positionInfo=a}}))}}},u=d,m=(a("d5e7"),a("f772d"),a("2877")),p=Object(m["a"])(u,s,r,!1,null,"4f403d12",null);t["default"]=p.exports},d25a:function(e,t,a){},d456:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{attrs:{title:"数据规则/按钮权限配置",width:"365",closable:!1,visible:e.visible},on:{close:e.onClose}},[a("a-tabs",{attrs:{defaultActiveKey:"1"}},[a("a-tab-pane",{key:"1",attrs:{tab:"数据规则"}},[e.dataruleList.length>0?a("a-checkbox-group",{model:{value:e.dataruleChecked,callback:function(t){e.dataruleChecked=t},expression:"dataruleChecked"}},[a("a-row",[e._l(e.dataruleList,(function(t,s){return a("a-col",{key:"dr"+s,attrs:{span:24}},[a("a-checkbox",{attrs:{value:t.id}},[e._v(e._s(t.ruleName))])],1)})),a("a-col",{attrs:{span:24}},[a("div",{staticStyle:{width:"100%","margin-top":"15px"}},[a("a-button",{attrs:{type:"primary",size:"small",icon:"save"},on:{click:e.saveDataruleForRole}},[e._v("点击保存")])],1)])],2)],1):a("div",[a("h3",[e._v("无配置信息!")])])],1)],1)],1)},r=[],l=a("290c"),i=a("da05"),o=a("0fea"),n={name:"DeptRoleDataruleModal",components:{ACol:i["b"],ARow:l["a"]},data:function(){return{departId:"",functionId:"",roleId:"",visible:!1,tabList:[{key:"1",tab:"数据规则"},{key:"2",tab:"按钮权限"}],activeTabKey:"1",url:{datarule:"/sys/sysDepartRole/datarule"},dataruleList:[],dataruleChecked:[]}},methods:{loadData:function(){var e=this;Object(o["c"])("".concat(this.url.datarule,"/").concat(this.functionId,"/").concat(this.departId,"/").concat(this.roleId)).then((function(t){if(t.success){e.dataruleList=t.result.datarule;var a=t.result.drChecked;a&&(e.dataruleChecked=a.split(","))}}))},saveDataruleForRole:function(){var e=this;this.dataruleChecked&&0!=this.dataruleChecked.length||this.$message.warning("请注意，现未勾选任何数据权限!");var t={permissionId:this.functionId,roleId:this.roleId,dataRuleIds:this.dataruleChecked.join(",")};Object(o["i"])(this.url.datarule,t).then((function(t){t.success?e.$message.success(t.message):e.$message.error(t.message)}))},show:function(e,t,a){this.onReset(),this.departId=t,this.functionId=e,this.roleId=a,this.visible=!0,this.loadData()},onClose:function(){this.visible=!1,this.onReset()},onTabChange:function(e){this.activeTabKey=e},onReset:function(){this.functionId="",this.roleId="",this.dataruleList=[],this.dataruleChecked=[]}}},c=n,d=a("2877"),u=Object(d["a"])(c,s,r,!1,null,"776b339a",null);t["default"]=u.exports},d57c:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"数据源编码"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["code",e.validatorRules.code],expression:"['code', validatorRules.code]"}],attrs:{placeholder:"请输入数据源编码",disabled:!!e.model.id}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"数据源名称"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",e.validatorRules.name],expression:"['name', validatorRules.name]"}],attrs:{placeholder:"请输入数据源名称"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"数据库类型"}},[a("j-dict-select-tag",{directives:[{name:"decorator",rawName:"v-decorator",value:["dbType",e.validatorRules.dbType],expression:"['dbType', validatorRules.dbType]"}],attrs:{placeholder:"请选择数据库类型","dict-code":"database_type",triggerChange:""},on:{change:e.handleDbTypeChange}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"驱动类"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dbDriver",e.validatorRules.dbDriver],expression:"['dbDriver', validatorRules.dbDriver]"}],attrs:{placeholder:"请输入驱动类"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"数据源地址"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dbUrl",e.validatorRules.dbUrl],expression:"['dbUrl', validatorRules.dbUrl]"}],attrs:{placeholder:"请输入数据源地址"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"用户名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["dbUsername",e.validatorRules.dbUsername],expression:"['dbUsername', validatorRules.dbUsername]"}],attrs:{placeholder:"请输入用户名"}})],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"密码"}},[a("a-row",{attrs:{gutter:8}},[a("a-col",{attrs:{span:21}},[a("a-input-password",{directives:[{name:"decorator",rawName:"v-decorator",value:["dbPassword",e.validatorRules.dbPassword],expression:"['dbPassword', validatorRules.dbPassword]"}],attrs:{placeholder:"请输入密码"}})],1),a("a-col",{attrs:{span:3}},[a("a-button",{staticStyle:{width:"100%"},attrs:{type:"primary",size:"small"},on:{click:e.handleTest}},[e._v("测试")])],1)],1)],1),a("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"备注"}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["remark",{}],expression:"['remark', {}]"}],attrs:{placeholder:"请输入备注"}})],1)],1)],1)],1)},r=[],l=a("88bc"),i=a.n(l),o=a("0fea"),n=a("ca00"),c={name:"SysDataSourceModal",components:{},data:function(){var e=this;return{title:"操作",visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,form:this.$form.createForm(this),validatorRules:{code:{validateFirst:!0,rules:[{required:!0,message:"请输入数据源编码!"},{validator:function(t,a,s){var r=/^[a-z|A-Z][a-z|A-Z\d_-]{0,}$/;r.test(a)?Object(n["r"])("sys_data_source","code",a,e.model.id,s):s("编码必须以字母开头，可包含数字、下划线、横杠")}}]},name:{rules:[{required:!0,message:"请输入数据源名称!"}]},dbType:{rules:[{required:!0,message:"请选择数据库类型!"}]},dbDriver:{rules:[{required:!0,message:"请输入驱动类!"}]},dbUrl:{rules:[{required:!0,message:"请输入数据源地址!"}]},dbName:{rules:[{required:!0,message:"请输入数据库名称!"}]},dbUsername:{rules:[{required:!0,message:"请输入用户名!"}]},dbPassword:{rules:[{required:!1,message:"请输入密码!"}]}},url:{add:"/sys/dataSource/add",edit:"/sys/dataSource/edit"},dbDriverMap:{1:{dbDriver:"com.mysql.jdbc.Driver"},4:{dbDriver:"com.mysql.cj.jdbc.Driver"},2:{dbDriver:"oracle.jdbc.OracleDriver"},3:{dbDriver:"com.microsoft.sqlserver.jdbc.SQLServerDriver"},5:{dbDriver:"org.mariadb.jdbc.Driver"},6:{dbDriver:"org.postgresql.Driver"},7:{dbDriver:"dm.jdbc.driver.DmDriver"},8:{dbDriver:"com.kingbase8.Driver"},9:{dbDriver:"com.oscar.Driver"},10:{dbDriver:"org.sqlite.JDBC"},11:{dbDriver:"com.ibm.db2.jcc.DB2Driver"},12:{dbDriver:"org.hsqldb.jdbc.JDBCDriver"},13:{dbDriver:"org.apache.derby.jdbc.ClientDriver"},14:{dbDriver:"org.h2.Driver"},15:{dbDriver:""}},dbUrlMap:{1:{dbUrl:"*******************************************************************************************"},4:{dbUrl:"*******************************************************************************************&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai"},2:{dbUrl:"*************************************"},3:{dbUrl:"**************************************************************************"},5:{dbUrl:"*****************************************************************************"},6:{dbUrl:"*******************************************"},7:{dbUrl:"jdbc:dm://127.0.0.1:5236/?jeecg-boot&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8"},8:{dbUrl:"*******************************************"},9:{dbUrl:"******************************************"},10:{dbUrl:"*************************"},11:{dbUrl:"*************************************"},12:{dbUrl:"***************************************"},13:{dbUrl:"**************************************"},14:{dbUrl:"jdbc:h2:tcp://127.0.0.1:8082/jeecg-boot"},15:{dbUrl:""}}}},created:function(){},methods:{add:function(){this.edit({})},edit:function(e){var t=this;this.form.resetFields(),this.model=Object.assign({},e),this.visible=!0,this.$nextTick((function(){t.form.setFieldsValue(i()(t.model,"code","name","remark","dbType","dbDriver","dbUrl","dbName","dbUsername","dbPassword"))}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var e=this;this.form.validateFields((function(t,a){if(!t){e.confirmLoading=!0;var s=Object.assign(e.model,a),r=e.url.add,l="post";e.model.id&&(r=e.url.edit,l="put",s["code"]=void 0),Object(o["h"])(r,s,l).then((function(t){t.success?(e.$message.success(t.message),e.$emit("ok"),e.close()):e.$message.warning(t.message)})).finally((function(){e.confirmLoading=!1}))}}))},handleCancel:function(){this.close()},handleTest:function(){var e=this,t=["dbType","dbDriver","dbUrl","dbName","dbUsername","dbPassword"],a=this.form.getFieldsValue(t),s={};t.forEach((function(e){return s[e]={value:a[e],errors:null}})),this.form.setFields(s),this.$nextTick((function(){e.form.validateFields(t,(function(t,s){if(!t){var r=e.$message.loading("连接中……",0);Object(o["i"])("/online/cgreport/api/testConnection",a).then((function(t){if(!t.success)throw new Error(t.message);e.$message.success("连接成功")})).catch((function(t){e.$warning({title:"连接失败",content:t.message||t})})).finally((function(){return r()}))}}))}))},handleDbTypeChange:function(e){var t=this.dbDriverMap[e],a=this.dbUrlMap[e];t&&this.form.setFieldsValue(t),a&&this.form.setFieldsValue(a)}}},d=c,u=a("2877"),m=Object(u["a"])(d,s,r,!1,null,"0fb5e495",null);t["default"]=m.exports},d5e7:function(e,t,a){"use strict";var s=a("d25a"),r=a.n(s);r.a},d62c:function(e,t,a){"use strict";var s=a("34d2"),r=a.n(s);r.a},dc4b:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{staticStyle:{overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:"650",placement:"right",closable:!0,visible:e.visible},on:{close:e.close}},[a("a-form",[a("a-form-item",{attrs:{label:"所拥有的部门权限"}},[e.treeData.length>0?a("a-tree",{attrs:{checkable:"",checkedKeys:e.checkedKeys,treeData:e.treeData,selectedKeys:e.selectedKeys,expandedKeys:e.expandedKeysss,checkStrictly:e.checkStrictly},on:{check:e.onCheck,expand:e.onExpand,select:e.onTreeNodeSelect},scopedSlots:e._u([{key:"hasDatarule",fn:function(t){var s=t.slotTitle,r=t.ruleFlag;return a("span",{},[e._v("\n          "+e._s(s)),r?a("a-icon",{staticStyle:{"margin-left":"5px",color:"red"},attrs:{type:"align-left"}}):e._e()],1)}}],null,!1,2869785871)}):a("div",[a("h3",[e._v("无可配置部门权限!")])])],1)],1),a("div",{staticClass:"drawer-bootom-button"},[a("a-dropdown",{staticStyle:{float:"left"},attrs:{trigger:["click"],placement:"topCenter"}},[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:function(t){return e.switchCheckStrictly(1)}}},[e._v("父子关联")]),a("a-menu-item",{key:"2",on:{click:function(t){return e.switchCheckStrictly(2)}}},[e._v("取消关联")]),a("a-menu-item",{key:"3",on:{click:e.checkALL}},[e._v("全部勾选")]),a("a-menu-item",{key:"4",on:{click:e.cancelCheckALL}},[e._v("取消全选")]),a("a-menu-item",{key:"5",on:{click:e.expandAll}},[e._v("展开所有")]),a("a-menu-item",{key:"6",on:{click:e.closeAll}},[e._v("合并所有")])],1),a("a-button",[e._v("\n        树操作 "),a("a-icon",{attrs:{type:"up"}})],1)],1),a("a-popconfirm",{attrs:{title:"确定放弃编辑？",okText:"确定",cancelText:"取消"},on:{confirm:e.close}},[a("a-button",{staticStyle:{"margin-right":".8rem"}},[e._v("取消")])],1),a("a-button",{staticStyle:{"margin-right":"0.8rem"},attrs:{type:"primary",loading:e.loading,ghost:""},on:{click:function(t){return e.handleSubmit(!1)}}},[e._v("仅保存")]),a("a-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.handleSubmit(!0)}}},[e._v("保存并关闭")])],1),a("dept-role-datarule-modal",{ref:"datarule"})],1)},r=[],l=a("4ec3"),i=a("2285"),o=a("d456");function n(e){return u(e)||d(e)||p(e)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function u(e){if(Array.isArray(e))return h(e)}function m(e,t){var a;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(a=p(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var s=0,r=function(){};return{s:r,n:function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var l,i=!0,o=!1;return{s:function(){a=e[Symbol.iterator]()},n:function(){var e=a.next();return i=e.done,e},e:function(e){o=!0,l=e},f:function(){try{i||null==a.return||a.return()}finally{if(o)throw l}}}}function p(e,t){if(e){if("string"===typeof e)return h(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}var f={name:"DeptRoleAuthModal",components:{DeptRoleDataruleModal:o["default"],RoleDataruleModal:i["default"]},data:function(){return{departId:"",roleId:"",treeData:[],defaultCheckedKeys:[],checkedKeys:[],halfCheckedKeys:[],expandedKeysss:[],allTreeKeys:[],autoExpandParent:!0,checkStrictly:!0,title:"部门角色权限配置",visible:!1,loading:!1,selectedKeys:[]}},methods:{switchCheckStrictly:function(e){1==e?this.checkStrictly=!1:2==e&&(this.checkStrictly=!0)},onTreeNodeSelect:function(e){e&&e.length>0&&(this.selectedKeys=e),this.$refs.datarule.show(this.selectedKeys[0],this.departId,this.roleId)},onCheck:function(e){this.checkStrictly?this.checkedKeys=e.checked:this.checkedKeys=e},show:function(e,t){this.departId=t,this.roleId=e,this.visible=!0},close:function(){this.reset(),this.$emit("close"),this.visible=!1},onExpand:function(e){this.expandedKeysss=e,this.autoExpandParent=!1},reset:function(){this.expandedKeysss=[],this.checkedKeys=[],this.defaultCheckedKeys=[],this.loading=!1},expandAll:function(){this.expandedKeysss=this.allTreeKeys},closeAll:function(){this.expandedKeysss=[]},checkALL:function(){this.checkedKeys=this.allTreeKeys},cancelCheckALL:function(){this.checkedKeys=[]},handleCancel:function(){this.close()},handleSubmit:function(e){var t=this,a=this,s={roleId:a.roleId,permissionIds:a.checkedKeys.join(","),lastpermissionIds:a.defaultCheckedKeys.join(",")};a.loading=!0,Object(l["S"])(s).then((function(s){s.success?(a.$message.success(s.message),a.loading=!1,e&&a.close()):(a.$message.error(s.message),a.loading=!1,e&&a.close()),t.loadData()}))},convertTreeListToKeyLeafPairs:function(e){var t,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=m(e);try{for(s.s();!(t=s.n()).done;){var r=t.value,l=r.key,i=r.isLeaf,o=r.children;a.push({key:l,isLeaf:i}),o&&o.length>0&&this.convertTreeListToKeyLeafPairs(o,a)}}catch(n){s.e(n)}finally{s.f()}return a},loadData:function(){var e=this;Object(l["M"])({departId:this.departId}).then((function(t){e.treeData=t.result.treeList,e.allTreeKeys=t.result.ids,Object(l["F"])({roleId:e.roleId}).then((function(t){e.checkedKeys=n(t.result),e.defaultCheckedKeys=n(t.result),e.expandedKeysss=e.allTreeKeys}))}))}},watch:{visible:function(){this.visible&&this.loadData()}}},b=f,v=(a("79c1"),a("2877")),g=Object(v["a"])(b,s,r,!1,null,"642e9f1c",null);t["default"]=g.exports},ddf9:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{staticStyle:{top:"20px"},attrs:{title:"重新设定密码",width:800,visible:e.visible,confirmLoading:e.confirmLoading,cancelText:"关闭"},on:{ok:e.handleSubmit,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form",{attrs:{form:e.form}},[a("a-form-item",{attrs:{label:"用户账号",labelCol:e.labelCol,wrapperCol:e.wrapperCol}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["username",{}],expression:"[ 'username', {}]"}],attrs:{placeholder:"请输入用户账号",readOnly:!0}})],1),a("a-form-item",{attrs:{label:"登录密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,hasFeedback:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["password",e.validatorRules.password],expression:"[ 'password', validatorRules.password]"}],attrs:{type:"password",placeholder:"请输入登录密码"}})],1),a("a-form-item",{attrs:{label:"确认密码",labelCol:e.labelCol,wrapperCol:e.wrapperCol,hasFeedback:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["confirmpassword",e.validatorRules.confirmpassword],expression:"[ 'confirmpassword', validatorRules.confirmpassword]"}],attrs:{type:"password",placeholder:"请重新输入登录密码"},on:{blur:e.handleConfirmBlur}})],1)],1)],1)],1)},r=[],l=a("4ec3"),i={name:"PasswordModal",data:function(){return{visible:!1,confirmLoading:!1,confirmDirty:!1,validatorRules:{password:{rules:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,message:"密码由8位数字、大小写字母和特殊符号组成!"},{validator:this.validateToNextPassword}]},confirmpassword:{rules:[{required:!0,message:"请重新输入登录密码!"},{validator:this.compareToFirstPassword}]}},model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},form:this.$form.createForm(this)}},created:function(){},methods:{show:function(e){var t=this;this.form.resetFields(),this.visible=!0,this.model.username=e,this.$nextTick((function(){t.form.setFieldsValue({username:e})}))},close:function(){this.$emit("close"),this.visible=!1,this.disableSubmit=!1,this.selectedRole=[]},handleSubmit:function(){var e=this;this.form.validateFields((function(t,a){if(!t){e.confirmLoading=!0;var s=Object.assign(e.model,a);Object(l["g"])(s).then((function(t){t.success?(e.$message.success(t.message),e.$emit("ok")):e.$message.warning(t.message)})).finally((function(){e.confirmLoading=!1,e.close()}))}}))},handleCancel:function(){this.close()},validateToNextPassword:function(e,t,a){var s=this.form,r=s.getFieldValue("confirmpassword");t&&r&&t!==r&&a("两次输入的密码不一样！"),t&&this.confirmDirty&&s.validateFields(["confirm"],{force:!0}),a()},compareToFirstPassword:function(e,t,a){var s=this.form;t&&t!==s.getFieldValue("password")?a("两次输入的密码不一样！"):a()},handleConfirmBlur:function(e){var t=e.target.value;this.confirmDirty=this.confirmDirty||!!t}}},o=i,n=a("2877"),c=Object(n["a"])(o,s,r,!1,null,null,null);t["default"]=c.exports},e326:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-card",{attrs:{visible:e.visible}},[a("a-form-model",{ref:"form",attrs:{model:e.model}},[a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"机构名称"}},[a("a-input",{staticStyle:{border:"0"},attrs:{placeholder:""},model:{value:e.model.departName,callback:function(t){e.$set(e.model,"departName",t)},expression:"model.departName"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"上级部门"}},[a("a-tree-select",{staticStyle:{width:"100%",border:"none",outline:"none"},attrs:{disabled:"",dropdownStyle:{maxHeight:"200px",overflow:"auto"},treeData:e.treeData,placeholder:"无"},model:{value:e.model.parentId,callback:function(t){e.$set(e.model,"parentId",t)},expression:"model.parentId"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"机构编码"}},[a("a-input",{staticStyle:{border:"0"},attrs:{placeholder:""},model:{value:e.model.orgCode,callback:function(t){e.$set(e.model,"orgCode",t)},expression:"model.orgCode"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"机构类型"}},[a("a-radio-group",{attrs:{disabled:!0,"read-only":""},model:{value:e.model.orgCategory,callback:function(t){e.$set(e.model,"orgCategory",t)},expression:"model.orgCategory"}},[a("a-radio",{attrs:{value:"1"}},[e._v("\n            公司\n          ")]),a("a-radio",{attrs:{value:"2"}},[e._v("\n            部门\n          ")]),a("a-radio",{attrs:{value:"3"}},[e._v("\n            岗位\n          ")])],1)],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"排序"}},[a("a-input-number",{staticStyle:{border:"0"},model:{value:e.model.departOrder,callback:function(t){e.$set(e.model,"departOrder",t)},expression:"model.departOrder"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"手机号"}},[a("a-input",{staticStyle:{border:"0"},attrs:{placeholder:""},model:{value:e.model.mobile,callback:function(t){e.$set(e.model,"mobile",t)},expression:"model.mobile"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"地址"}},[a("a-input",{staticStyle:{border:"0"},attrs:{placeholder:""},model:{value:e.model.address,callback:function(t){e.$set(e.model,"address",t)},expression:"model.address"}})],1),a("a-form-model-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"备注"}},[a("a-textarea",{staticStyle:{border:"0"},attrs:{placeholder:""},model:{value:e.model.memo,callback:function(t){e.$set(e.model,"memo",t)},expression:"model.memo"}})],1)],1)],1)},r=[],l=a("4ec3"),i={name:"DeptBaseInfo",components:{},data:function(){return{departTree:[],id:"",model:{},visible:!1,disable:!0,treeData:[],labelCol:{xs:{span:24},sm:{span:3}},wrapperCol:{xs:{span:24},sm:{span:16}}}},created:function(){this.loadTreeData()},methods:{loadTreeData:function(){var e=this;Object(l["G"])().then((function(t){if(t.success)for(var a=0;a<t.result.length;a++){var s=t.result[a];e.treeData.push(s)}}))},open:function(e){var t=this;this.visible=!0,this.$nextTick((function(){t.$refs.form.resetFields(),t.model=Object.assign({},e)}))},clearForm:function(){this.$refs.form.resetFields(),this.treeData=[]}}},o=i,n=(a("55e1"),a("2877")),c=Object(n["a"])(o,s,r,!1,null,"7cf52d0e",null);t["default"]=c.exports},e3fb:function(e,t,a){},e884:function(e,t,a){},eaf1:function(e,t,a){},eb9c:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{width:1e3,title:e.title,visible:e.innerVisible,cancelText:"关闭",okButtonProps:{style:{display:"none"}}},on:{cancel:e.handleCancel}},[a("a-alert",{staticStyle:{"margin-bottom":"16px"},attrs:{type:"info",showIcon:""}},[a("template",{slot:"message"},[a("span",[e._v("已选择")]),a("a",{staticStyle:{"font-weight":"600",padding:"0 4px"}},[e._v(e._s(e.selectedRowKeys.length))]),a("span",[e._v("项")]),e.selectedRowKeys.length>0?[a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleClearSelection}},[e._v("清空选择")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleRevertBatch}},[e._v("批量还原")]),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:e.handleDeleteBatch}},[e._v("批量删除")])]:e._e()],2)],2),a("a-table",{ref:"table",attrs:{rowKey:"id",size:"middle",bordered:"",columns:e.columns,loading:e.loading,dataSource:e.dataSource,pagination:!1,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.handleTableSelectChange}},scopedSlots:e._u([{key:"avatarslot",fn:function(t,s,r){return[a("div",{staticClass:"anty-img-wrap"},[a("a-avatar",{attrs:{shape:"square",src:e.url.getAvatar(s.avatar),icon:"user"}})],1)]}},{key:"action",fn:function(t,s){return a("span",{},[a("a",{on:{click:function(t){return e.handleRevert([s.id])}}},[a("a-icon",{attrs:{type:"redo"}}),e._v(" 还原用户")],1),a("a-divider",{attrs:{type:"vertical"}}),a("a",{on:{click:function(t){return e.handleDelete([s.id])}}},[a("a-icon",{attrs:{type:"delete"}}),e._v(" 彻底删除")],1)],1)}}])})],1)},r=[],l=a("0fea");function i(e){var t=this;this.loading=!!e.loading,e.promise.then((function(t){if(!t.success||"function"!==typeof e.success)throw new Error(t.message);e.success(t)})).catch((function(e){t.$message.warning("查询已删除的用户失败："+(e.message||e))})).finally((function(){t.loading=!1}))}var o={name:"UserRecycleBinModal",props:{visible:{type:Boolean,default:!1}},data:function(){return{title:"用户回收站",loading:!1,innerVisible:!1,selectedRowKeys:[],dataSource:[],columns:[{title:"#",align:"center",key:"rowIndex",width:80,customRender:function(e,t,a){return a+1}},{title:"账号",align:"center",dataIndex:"username"},{title:"姓名",align:"center",dataIndex:"realname"},{title:"头像",align:"center",dataIndex:"avatar",scopedSlots:{customRender:"avatarslot"}},{title:"部门",align:"center",dataIndex:"orgCode"},{title:"操作",align:"center",dataIndex:"action",width:200,scopedSlots:{customRender:"action"}}],url:{getAvatar:function(e){return Object(l["d"])("".concat(e))},recycleBin:"/sys/user/recycleBin",putRecycleBin:"/sys/user/putRecycleBin",deleteRecycleBin:"/sys/user/deleteRecycleBin"}}},watch:{visible:{immediate:!0,handler:function(e){e&&this.loadData(),this.innerVisible=e}},innerVisible:function(e){this.$emit("update:visible",e)}},methods:{loadData:function(){var e=this;i.call(this,{loading:!0,promise:this.$http.get(this.url.recycleBin),success:function(t){return e.dataSource=t.result}})},handleOk:function(){this.loadData(),this.$emit("ok")},handleCancel:function(){this.innerVisible=!1},handleRevert:function(e){var t=this;this.$confirm({title:"恢复用户",content:"您确定要恢复这 ".concat(e.length," 个用户吗？"),centered:!0,onOk:function(){Object(l["j"])(t.url.putRecycleBin,{userIds:e.join(",")}).then((function(a){a.success&&(t.handleOk(),t.handleClearSelection(),t.$message.success("还原 ".concat(e.length," 个用户成功！")))}))}})},handleDelete:function(e){var t=this,a=this.$createElement;this.$confirm({title:"彻底删除用户",content:a("div",[a("p",["您确定要彻底删除这 ",e.length," 个用户吗？"]),a("p",{style:"color:red;"},["注意：彻底删除后将无法恢复，请谨慎操作！"])]),centered:!0,onOk:function(){var a=t;Object(l["a"])(a.url.deleteRecycleBin,{userIds:e.join(",")}).then((function(s){s.success?(t.loadData(),t.handleClearSelection(),t.$message.success("彻底删除 ".concat(e.length," 个用户成功！"))):a.$message.warning(s.message)}))}})},handleRevertBatch:function(){this.handleRevert(this.selectedRowKeys)},handleDeleteBatch:function(){this.handleDelete(this.selectedRowKeys)},handleClearSelection:function(){this.handleTableSelectChange([],[])},handleTableSelectChange:function(e,t){this.selectedRowKeys=e,this.selectionRows=t}}},n=o,c=a("2877"),d=Object(c["a"])(n,s,r,!1,null,"08b620db",null);t["default"]=d.exports},ece6:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.visible?a("a-drawer",{staticStyle:{overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:"45%",placement:"right",closable:!0,visible:e.visible},on:{close:e.handleCancel}},[a("a-form-model",{ref:"form",attrs:{layout:e.layout,"label-col":e.labelCol,"wrapper-col":e.wrapperCol,model:e.router}},[a("a-form-model-item",{attrs:{label:"路由ID"}},[a("a-input",{attrs:{placeholder:"路由唯一ID"},model:{value:e.router.routerId,callback:function(t){e.$set(e.router,"routerId",t)},expression:"router.routerId"}})],1),a("a-form-model-item",{attrs:{label:"路由名称"}},[a("a-input",{attrs:{placeholder:"路由名称"},model:{value:e.router.name,callback:function(t){e.$set(e.router,"name",t)},expression:"router.name"}})],1),a("a-form-model-item",{attrs:{label:"路由URI"}},[a("a-input",{attrs:{placeholder:"路由URL"},model:{value:e.router.uri,callback:function(t){e.$set(e.router,"uri",t)},expression:"router.uri"}})],1),a("a-form-model-item",{attrs:{label:"路由状态",prop:"status"}},[a("a-switch",{attrs:{"default-checked":""},model:{value:e.router.status,callback:function(t){e.$set(e.router,"status",t)},expression:"router.status"}})],1),a("a-form-model-item",{attrs:{prop:"predicates",label:"路由条件"}},[e._l(e.router.predicates,(function(t,s){return a("div",[a("a-divider",[e._v(e._s(t.name)+"\n          "),a("a-icon",{attrs:{type:"delete",size:"22"},on:{click:function(t){return e.removePredicate(e.router,s)}}})],1),a("div",[e._l(t.args,(function(s,r){return[r==e.currentTagIndex?a("a-input",{ref:"input",refInFor:!0,style:{width:"190px"},attrs:{type:"text",size:"small",value:s},on:{change:e.handleInputChange,blur:function(a){return e.handleInputEditConfirm(t,s,r)},keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputEditConfirm(t,s,r)}}}):a("a-tag",{key:s,attrs:{closable:!0},on:{close:function(){return e.removeTag(t,s)},click:function(t){return e.editTag(s,r)}}},[e._v("\n              "+e._s(s)+"\n            ")])]})),e.inputVisible&&s==e.currentNameIndex?a("a-input",{ref:"input",refInFor:!0,style:{width:"100px"},attrs:{type:"text",size:"small",value:e.inputValue},on:{change:e.handleInputChange,blur:function(a){return e.handleInputConfirm(t)},keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleInputConfirm(t)}}}):a("a-tag",{staticStyle:{background:"#fff",borderStyle:"dashed"},on:{click:function(a){return e.showInput(t,s)}}},[a("a-icon",{attrs:{type:"plus"}}),e._v("\n            新建"+e._s(t.name)+"\n          ")],1)],2)],1)})),a("p",{staticClass:"btn",staticStyle:{"padding-top":"10px"}},[a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},on:{click:e.predicatesHandleMenuClick},slot:"overlay"},e._l(e.tagArray,(function(t){return a("a-menu-item",{key:t},[e._v(e._s(t))])})),1),a("a-button",{staticStyle:{"margin-left":"8px",width:"100%"},attrs:{type:"dashed"}},[e._v(" 添加路由条件\n            "),a("a-icon",{attrs:{type:"down"}})],1)],1)],1)],2),a("a-form-model-item",{attrs:{prop:"predicates",label:"过滤器"}},[e._l(e.router.filters,(function(t,s){return a("div",[a("a-divider",[e._v(e._s(t.name)+"\n          "),a("a-icon",{attrs:{type:"delete",size:"22"},on:{click:function(t){return e.removeFilter(e.router,s)}}})],1),e._l(t.args,(function(s,r){return a("div",{key:s.key},[a("a-input",{staticStyle:{width:"45%","margin-right":"8px"},attrs:{placeholder:"参数键"},model:{value:s.key,callback:function(t){e.$set(s,"key",t)},expression:"tag.key"}}),a("a-input",{staticStyle:{width:"40%","margin-right":"8px"},attrs:{placeholder:"参数值"},model:{value:s.value,callback:function(t){e.$set(s,"value",t)},expression:"tag.value"}}),a("a-icon",{staticClass:"dynamic-delete-button",attrs:{type:"minus-circle-o"},on:{click:function(a){return e.removeFilterParams(t,r)}}})],1)})),a("a-button",{staticStyle:{"margin-left":"28%",width:"30%"},attrs:{type:"dashed",size:"small"},on:{click:function(a){return e.addFilterParams(t)}}},[a("a-icon",{attrs:{type:"plus"}}),e._v("\n          添加参数\n        ")],1)],2)})),a("p",{staticClass:"btn",staticStyle:{"padding-top":"10px"}},[a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},on:{click:e.filterHandleMenuClick},slot:"overlay"},e._l(e.filterArray,(function(t){return a("a-menu-item",{key:t.key,attrs:{name:t.name}},[e._v(e._s(t.name))])})),1),a("a-button",{staticStyle:{"margin-left":"8px",width:"100%"},attrs:{type:"dashed"}},[e._v(" 添加过滤器\n            "),a("a-icon",{attrs:{type:"down"}})],1)],1)],1)],2),a("a-row",{staticClass:"drawer-bootom-button",style:{textAlign:"right"}},[a("a-button",{style:{marginRight:"8px"},on:{click:e.handleCancel}},[e._v("\n        关闭\n      ")]),a("a-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("确定")])],1)],1)],1):e._e()},r=[],l=a("0fea");function i(e,t){if(null==e)return{};var a,s,r=o(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)a=l[s],t.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}function o(e,t){if(null==e)return{};var a,s,r={},l=Object.keys(e);for(s=0;s<l.length;s++)a=l[s],t.indexOf(a)>=0||(r[a]=e[a]);return r}function n(e){return m(e)||u(e)||d(e)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(e,t){if(e){if("string"===typeof e)return p(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?p(e,t):void 0}}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function m(e){if(Array.isArray(e))return p(e)}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}var h={name:"GateWayRouteModal",components:{},data:function(){return{layout:"horizontal",labelCol:{span:3},wrapperCol:{span:14},currentNameIndex:0,currentTagIndex:-1,predicates:{},filterArray:[{key:0,name:"熔断器"},{key:1,name:"限流过滤器"}],tagArray:["Path","Host","Cookie","Header","Method","Query","After","Before","Between","RemoteAddr"],inputVisible:!1,inputValue:"",url:{update:"/sys/gatewayRoute/updateAll",clear:"/sys/gatewayRoute/clearRedis"},router:this.getRouter(),title:"路由编辑",visible:!1,loading:!1}},methods:{getRouter:function(){return{routerId:"",name:"",uri:"",predicates:[],filters:[]}},show:function(e){e?(e.status=Boolean(e.status),this.router=e):(this.router=this.getRouter(),this.inputValue=""),this.visible=!0,this.currentTagIndex=-1,this.currentNameIndex=-1},close:function(){this.reset(),this.$emit("close"),this.$refs["form"].resetFields(),this.visible=!1},removeTag:function(e,t){var a=e.args.filter((function(e){return e!==t}));e.args=a},predicatesHandleMenuClick:function(e){this.router.predicates.push({args:[],name:e.key})},editTag:function(e,t){this.currentTagIndex=t},showInput:function(e,t){this.inputVisible=!0,this.currentNameIndex=t},handleInputChange:function(e){this.inputValue=e.target.value},removePredicate:function(e,t){e.predicates.splice(t,1)},removeFilterParams:function(e,t){e.args.splice(t,1)},removeFilter:function(e,t){e.filters.splice(t,1)},addFilterParams:function(e){e.args.push({key:"key"+e.args.length+1,value:""})},filterHandleMenuClick:function(e){0==e.key&&this.router.filters.push({args:[{key:"name",value:"default"},{key:"fallbackUri",value:"forward:/fallback"}],name:"Hystrix",title:this.filterArray[0].name}),1==e.key&&this.router.filters.push({args:[{key:"key-resolver",value:"#{@ipKeyResolver}"},{key:"redis-rate-limiter.replenishRate",value:20},{key:"redis-rate-limiter.burstCapacity",value:20}],name:"RequestRateLimiter",title:this.filterArray[1].name})},handleInputConfirm:function(e){var t=this.inputValue,a=e.args;t&&-1===a.indexOf(t)&&(e.args=[].concat(n(a),[t])),Object.assign(this,{tags:a,inputVisible:!1,inputValue:""}),this.currentTagIndex=-1},handleInputEditConfirm:function(e,t,a){if(this.inputValue){var s=this.inputValue;e.args[a]=s}this.currentTagIndex=-1},reset:function(){this.expandedKeysss=[],this.checkedKeys=[],this.defaultCheckedKeys=[],this.loading=!1},handleCancel:function(){this.close()},handleSubmit:function(){var e=this,t=this.router,a=(t.predicates,t.filters,i(t,["predicates","filters"])),s=a;s.predicates=JSON.stringify(this.router.predicates),s.filters=JSON.stringify(this.router.filters),Object(l["i"])(this.url.update,{router:s}).then((function(t){t.success?(e.close(),e.$emit("ok"),e.$message.success(t.message)):e.$message.error(t.message)}))}}},f=h,b=(a("4cfd"),a("2877")),v=Object(b["a"])(f,s,r,!1,null,"7df3d545",null);t["default"]=v.exports},ed2a:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:1200,visible:e.visible,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"账号"}},[a("a-input",{attrs:{placeholder:"请输入账号"},model:{value:e.queryParam.username,callback:function(t){e.$set(e.queryParam,"username",t)},expression:"queryParam.username"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"性别"}},[a("a-select",{attrs:{placeholder:"请选择性别"},model:{value:e.queryParam.sex,callback:function(t){e.$set(e.queryParam,"sex",t)},expression:"queryParam.sex"}},[a("a-select-option",{attrs:{value:""}},[e._v("请选择性别查询")]),a("a-select-option",{attrs:{value:"1"}},[e._v("男性")]),a("a-select-option",{attrs:{value:"2"}},[e._v("女性")])],1)],1)],1),e.toggleSearchStatus?[a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"邮箱"}},[a("a-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.queryParam.email,callback:function(t){e.$set(e.queryParam,"email",t)},expression:"queryParam.email"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"手机号码"}},[a("a-input",{attrs:{placeholder:"请输入手机号码"},model:{value:e.queryParam.phone,callback:function(t){e.$set(e.queryParam,"phone",t)},expression:"queryParam.phone"}})],1)],1),a("a-col",{attrs:{span:6}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择状态"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[a("a-select-option",{attrs:{value:""}},[e._v("请选择状态")]),a("a-select-option",{attrs:{value:"1"}},[e._v("正常")]),a("a-select-option",{attrs:{value:"2"}},[e._v("解冻")])],1)],1)],1)]:e._e(),a("a-col",{attrs:{span:6}},[a("span",{staticClass:"table-page-search-submitButtons",staticStyle:{float:"left",overflow:"hidden"}},[a("a-button",{attrs:{type:"primary",icon:"search"},on:{click:e.searchByquery}},[e._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"reload"},on:{click:e.searchReset}},[e._v("重置")]),a("a",{staticStyle:{"margin-left":"8px"},on:{click:e.handleToggleSearch}},[e._v("\n                "+e._s(e.toggleSearchStatus?"收起":"展开")+"\n                "),a("a-icon",{attrs:{type:e.toggleSearchStatus?"up":"down"}})],1)],1)])],2)],1)],1),a("a-table",{ref:"table",attrs:{rowKey:"id",columns:e.columns,dataSource:e.dataSource,pagination:e.ipagination,rowSelection:{selectedRowKeys:e.selectedRowKeys,onChange:e.onSelectChange,onSelect:e.onSelect}},on:{change:e.handleTableChange}})],1)},r=[],l=a("ca00"),i=a("4ec3"),o={name:"SelectUserListModal",components:{},data:function(){return{title:"选择用户",queryParam:{},columns:[{title:"用户账号",align:"center",dataIndex:"username",fixed:"left",width:200},{title:"用户名称",align:"center",dataIndex:"realname"},{title:"性别",align:"center",dataIndex:"sex",customRender:function(e){return 1==e?"男":2==e?"女":e}},{title:"手机号码",align:"center",dataIndex:"phone"},{title:"邮箱",align:"center",dataIndex:"email"},{title:"状态",align:"center",dataIndex:"status",customRender:function(e){return 1==e?"正常":2==e?"冻结":e}}],dataSource:[],ipagination:{current:1,pageSize:5,pageSizeOptions:["5","10","20"],showTotal:function(e,t){return t[0]+"-"+t[1]+" 共"+e+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},isorter:{column:"createTime",order:"desc"},selectedRowKeys:[],selectionRows:[],visible:!1,toggleSearchStatus:!1}},created:function(){this.loadData()},methods:{add:function(e,t){this.visible=!0,this.edit(e,t)},edit:function(e,t){if(t&&t.length>0?this.selectedRowKeys=t.split(","):this.selectedRowKeys=[],e){var a=this;a.selectionRows=[],e.forEach((function(e,t){a.selectionRows.push({id:a.selectedRowKeys[t],realname:e.label})}))}else this.selectionRows=[]},loadData:function(e){var t=this;1===e&&(this.ipagination.current=1);var a=this.getQueryParams();Object(i["A"])(a).then((function(e){e.success&&(t.dataSource=e.result.records,t.ipagination.total=e.result.total)}))},getQueryParams:function(){var e=Object.assign({},this.queryParam,this.isorter);return e.field=this.getQueryField(),e.pageNo=this.ipagination.current,e.pageSize=this.ipagination.pageSize,Object(l["d"])(e)},getQueryField:function(){for(var e="id,",t=0;t<this.columns.length;t++)e+=","+this.columns[t].dataIndex;return e},onSelectChange:function(e){this.selectedRowKeys=e},onSelect:function(e,t){1==t?this.selectionRows.push(e):this.selectionRows.forEach((function(t,a,s){t.id==e.id&&s.splice(a,1)}))},searchReset:function(){var e=this;Object.keys(e.queryParam).forEach((function(t){e.queryParam[t]=""})),e.loadData(1)},handleTableChange:function(e,t,a){Object.keys(a).length>0&&(this.isorter.column=a.field,this.isorter.order="ascend"==a.order?"asc":"desc"),this.ipagination=e,this.loadData()},handleCancel:function(){this.selectionRows=[],this.selectedRowKeys=[],this.visible=!1},handleOk:function(){this.$emit("choseUser",this.selectionRows),this.handleCancel()},searchByquery:function(){this.loadData(1)},handleToggleSearch:function(){this.toggleSearchStatus=!this.toggleSearchStatus}}},n=o,c=(a("30a6"),a("2877")),d=Object(c["a"])(n,s,r,!1,null,"7521bc2e",null);t["default"]=d.exports},ee00:function(e,t,a){},ee18:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-drawer",{staticStyle:{overflow:"auto","padding-bottom":"53px"},attrs:{title:e.title,maskClosable:!0,width:"650",placement:"right",closable:!0,visible:e.visible},on:{close:e.close}},[a("a-form",[a("a-form-item",{attrs:{label:"所拥有的权限"}},[a("a-tree",{attrs:{checkable:"",checkedKeys:e.checkedKeys,treeData:e.treeData,selectedKeys:e.selectedKeys,expandedKeys:e.expandedKeysss,checkStrictly:e.checkStrictly},on:{check:e.onCheck,expand:e.onExpand,select:e.onTreeNodeSelect},scopedSlots:e._u([{key:"hasDatarule",fn:function(t){var s=t.slotTitle,r=t.ruleFlag;return a("span",{},[e._v("\n          "+e._s(s)),r?a("a-icon",{staticStyle:{"margin-left":"5px",color:"red"},attrs:{type:"align-left"}}):e._e()],1)}}])})],1)],1),a("div",{staticClass:"drawer-bootom-button"},[a("a-dropdown",{staticStyle:{float:"left"},attrs:{trigger:["click"],placement:"topCenter"}},[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"1",on:{click:function(t){return e.switchCheckStrictly(1)}}},[e._v("父子关联")]),a("a-menu-item",{key:"2",on:{click:function(t){return e.switchCheckStrictly(2)}}},[e._v("取消关联")]),a("a-menu-item",{key:"3",on:{click:e.checkALL}},[e._v("全部勾选")]),a("a-menu-item",{key:"4",on:{click:e.cancelCheckALL}},[e._v("取消全选")]),a("a-menu-item",{key:"5",on:{click:e.expandAll}},[e._v("展开所有")]),a("a-menu-item",{key:"6",on:{click:e.closeAll}},[e._v("合并所有")])],1),a("a-button",[e._v("\n        树操作 "),a("a-icon",{attrs:{type:"up"}})],1)],1),a("a-popconfirm",{attrs:{title:"确定放弃编辑？",okText:"确定",cancelText:"取消"},on:{confirm:e.close}},[a("a-button",{staticStyle:{"margin-right":".8rem"}},[e._v("取消")])],1),a("a-button",{staticStyle:{"margin-right":"0.8rem"},attrs:{type:"primary",loading:e.loading,ghost:""},on:{click:function(t){return e.handleSubmit(!1)}}},[e._v("仅保存")]),a("a-button",{attrs:{type:"primary",loading:e.loading},on:{click:function(t){return e.handleSubmit(!0)}}},[e._v("保存并关闭")])],1),a("role-datarule-modal",{ref:"datarule"})],1)},r=[],l=a("4ec3"),i=a("2285");function o(e){return u(e)||d(e)||c(e)||n()}function n(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"===typeof e)return m(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(e,t):void 0}}function d(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function u(e){if(Array.isArray(e))return m(e)}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,s=new Array(t);a<t;a++)s[a]=e[a];return s}var p={name:"RoleModal",components:{RoleDataruleModal:i["default"]},data:function(){return{roleId:"",treeData:[],defaultCheckedKeys:[],checkedKeys:[],expandedKeysss:[],allTreeKeys:[],autoExpandParent:!0,checkStrictly:!0,title:"角色权限配置",visible:!1,loading:!1,selectedKeys:[]}},methods:{onTreeNodeSelect:function(e){e&&e.length>0&&(this.selectedKeys=e),this.$refs.datarule.show(this.selectedKeys[0],this.roleId)},onCheck:function(e){this.checkStrictly?this.checkedKeys=e.checked:this.checkedKeys=e},show:function(e){this.roleId=e,this.visible=!0},close:function(){this.reset(),this.$emit("close"),this.visible=!1},onExpand:function(e){this.expandedKeysss=e,this.autoExpandParent=!1},reset:function(){this.expandedKeysss=[],this.checkedKeys=[],this.defaultCheckedKeys=[],this.loading=!1},expandAll:function(){this.expandedKeysss=this.allTreeKeys},closeAll:function(){this.expandedKeysss=[]},checkALL:function(){this.checkedKeys=this.allTreeKeys},cancelCheckALL:function(){this.checkedKeys=[]},switchCheckStrictly:function(e){1==e?this.checkStrictly=!1:2==e&&(this.checkStrictly=!0)},handleCancel:function(){this.close()},handleSubmit:function(e){var t=this,a=this,s={roleId:a.roleId,permissionIds:a.checkedKeys.join(","),lastpermissionIds:a.defaultCheckedKeys.join(",")};a.loading=!0,Object(l["T"])(s).then((function(s){s.success?(a.$message.success(s.message),a.loading=!1,e&&a.close()):(a.$message.error(s.message),a.loading=!1,e&&a.close()),t.loadData()}))},loadData:function(){var e=this;Object(l["N"])().then((function(t){e.treeData=t.result.treeList,e.allTreeKeys=t.result.ids,Object(l["K"])({roleId:e.roleId}).then((function(t){e.checkedKeys=o(t.result),e.defaultCheckedKeys=o(t.result),e.expandedKeysss=e.allTreeKeys}))}))}},watch:{visible:function(){this.visible&&this.loadData()}}},h=p,f=(a("8046"),a("2877")),b=Object(f["a"])(h,s,r,!1,null,"36fb778f",null);t["default"]=b.exports},f1cf:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{title:e.title,width:e.width,visible:e.visible,confirmLoading:e.confirmLoading,destroyOnClose:!0,cancelText:"关闭"},on:{ok:e.handleOk,cancel:e.handleCancel}},[a("a-spin",{attrs:{spinning:e.confirmLoading}},[a("a-form-model",{ref:"form",attrs:{model:e.model,rules:e.validatorRules}},[a("a-form-model-item",{attrs:{label:"父级节点",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"pid"}},[a("j-tree-select",{ref:"treeSelect",attrs:{placeholder:"请选择父级节点",dict:"sys_category,name,id",pidField:"pid",pidValue:"0",disabled:e.disabled},model:{value:e.model.pid,callback:function(t){e.$set(e.model,"pid",t)},expression:"model.pid"}})],1),a("a-form-model-item",{attrs:{label:"分类名称",labelCol:e.labelCol,wrapperCol:e.wrapperCol,prop:"name"}},[a("a-input",{attrs:{placeholder:"请输入分类名称"},model:{value:e.model.name,callback:function(t){e.$set(e.model,"name",t)},expression:"model.name"}})],1)],1)],1)],1)},r=[],l=a("0fea"),i=a("b098");function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,s)}return a}function n(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){c(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function c(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var d={name:"SysCategoryModal",components:{JTreeSelect:i["default"]},data:function(){return{title:"操作",width:800,visible:!1,model:{},labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},confirmLoading:!1,validatorRules:{pid:{},name:[{required:!0,message:"请输入类型名称!"}]},url:{add:"/sys/category/add",edit:"/sys/category/edit",checkCode:"/sys/category/checkCode"},expandedRowKeys:[],pidField:"pid",subExpandedKeys:[]}},created:function(){},computed:{disabled:function(){return!!this.model.id}},methods:{add:function(){this.edit({})},edit:function(e){this.model=Object.assign({},e),this.visible=!0},close:function(){this.$emit("close"),this.visible=!1,this.$refs.form.resetFields()},handleOk:function(){var e=this,t=this;this.$refs.form.validate((function(a){if(!a)return!1;t.confirmLoading=!0;var s="",r="";e.model.id?(s+=e.url.edit,r="put"):(s+=e.url.add,r="post"),Object(l["h"])(s,e.model,r).then((function(a){a.success?(t.$message.success(a.message),t.submitSuccess(n({},e.model))):t.$message.warning(a.message)})).finally((function(){t.confirmLoading=!1,t.close()}))}))},handleCancel:function(){this.close()},submitSuccess:function(e){if(e.id)this.$emit("ok",e);else{var t=this.$refs.treeSelect.getCurrTreeData();this.expandedRowKeys=[],this.getExpandKeysByPid(e[this.pidField],t,t),e.pid&&0==this.expandedRowKeys.length&&(this.expandedRowKeys=this.subExpandedKeys),this.$emit("ok",e,this.expandedRowKeys.reverse())}},getExpandKeysByPid:function(e,t,a){if(e&&t&&t.length>0)for(var s=0;s<t.length;s++)t[s].key==e?(this.expandedRowKeys.push(t[s].key),this.getExpandKeysByPid(t[s]["parentId"],a,a)):this.getExpandKeysByPid(e,t[s].children,a)}}},u=d,m=a("2877"),p=Object(m["a"])(u,s,r,!1,null,null,null);t["default"]=p.exports},f266:function(e,t,a){},f772d:function(e,t,a){"use strict";var s=a("eaf1"),r=a.n(s);r.a},fcbc:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a-modal",{attrs:{width:e.modalWidth,visible:e.visible,footer:null,cancelText:"关闭"},on:{cancel:e.handleCancel}},[a("div",{staticClass:"marginCss"},[a("a-table",{ref:"table",attrs:{size:"small",bordered:"",rowKey:"id",columns:e.columns,dataSource:e.dataSource,rowClassName:e.setdataCss,loading:e.loading,scroll:{y:700},pagination:!1},scopedSlots:e._u([{key:"avatarslot",fn:function(t,s){return[a("div",{staticClass:"anty-img-wrap"},[a("img",{attrs:{src:e.getAvatarView(s)}})])]}}])},[a("span",{attrs:{slot:"dataVersionTitle1"},slot:"dataVersionTitle1"},[a("a-icon",{attrs:{type:"smile-o"}}),e._v(" 版本:"+e._s(e.dataVersion1Num))],1),a("span",{attrs:{slot:"dataVersionTitle2"},slot:"dataVersionTitle2"},[a("a-icon",{attrs:{type:"smile-o"}}),e._v(" 版本:"+e._s(e.dataVersion2Num))],1)])],1)])},r=[],l=a("0fea"),i={name:"DataLogCompareModal",data:function(){return{modalWidth:1e3,modaltoggleFlag:!0,confirmDirty:!1,title:"操作",visible:!1,model:{},confirmLoading:!1,headers:{},dataVersion1Num:"",dataVersion2Num:"",columns:[{title:"字段名",align:"left",dataIndex:"code",width:"30%"},{align:"left",dataIndex:"dataVersion1",width:"30%",slots:{title:"dataVersionTitle1"}},{title:"",dataIndex:"imgshow",align:"center",scopedSlots:{customRender:"avatarslot"},width:"10%"},{align:"left",dataIndex:"dataVersion2",width:"30%",slots:{title:"dataVersionTitle2"}}],dataSource:[],loading:!1,url:{queryCompareUrl:"/sys/dataLog/queryCompareList"}}},created:function(){},methods:{loadData:function(e,t){var a=this;this.dataSource=[];var s=this;Object(l["c"])(s.url.queryCompareUrl,{dataId1:e,dataId2:t}).then((function(e){if(e.success){s.dataVersion1Num=e.result[0].dataVersion,s.dataVersion2Num=e.result[1].dataVersion;var t=JSON.parse(e.result[0].dataContent),r=JSON.parse(e.result[1].dataContent);for(var l in t)for(var i in r)l==i&&a.dataSource.push({code:l,imgshow:"",dataVersion1:t[l],dataVersion2:r[i]})}}))},compareModal:function(e,t){this.visible=!0,this.loadData(e,t)},handleCancel:function(){this.close()},modalFormOk:function(){},close:function(){this.$emit("close"),this.visible=!1,this.disableSubmit=!1},setdataCss:function(e){var t="trcolor",a=e.dataVersion1,s=e.dataVersion2;if(a!=s)return t},getAvatarView:function(e){return e.dataVersion1!=e.dataVersion2?"/goright.png":""}}},o=i,n=(a("4a1c"),a("2877")),c=Object(n["a"])(o,s,r,!1,null,"6f34d1e7",null);t["default"]=c.exports},fe90:function(e,t,a){}}]);