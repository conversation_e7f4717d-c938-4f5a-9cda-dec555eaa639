(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~0be3793a"],{"36d5f":function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));var r=a("a34a"),i=a.n(r),n=a("5f87"),s=a("7ded"),o=a("2b0e"),h=a("9fb0");function c(e,t,a,r,i,n,s){try{var o=e[n](s),h=o.value}catch(c){return void a(c)}o.done?t(h):Promise.resolve(h).then(r,i)}function u(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){c(n,r,i,s,o,"next",e)}function o(e){c(n,r,i,s,o,"throw",e)}s(void 0)}))}}var l={methods:{checkLoginStatus:function(){var e=Object(n["a"])();return!!e||(this.$message.warning("请先登录"),this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),!1)},handleApiError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作失败";return!e.success&&(401===e.code||e.message&&e.message.includes("Token")||e.message&&e.message.includes("登录")||e.message&&e.message.includes("认证")||e.message&&e.message.includes("权限")?(this.handleTokenExpired(),!0):(this.$message.error(e.message||t),!1))},handleApiException:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"请求失败，请重试";return e.response&&401===e.response.status?(this.handleTokenExpired(),!0):"ERR_NETWORK"===e.code||e.message&&e.message.includes("Network Error")?(this.$message.error("网络连接失败，请检查网络"),!1):(this.$message.error(t),!1)},handleError:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"操作失败";return e.response?this.handleApiException(e,t):void 0!==e.success?this.handleApiError(e,t):this.handleApiException(e,t)},performLogout:function(){var e=u(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,t=o["default"].ls.get(h["a"]),this.clearLocalUserData(),!t){e.next=14;break}return e.prev=5,e.next=8,Object(s["d"])(t);case 8:e.next=14;break;case 11:e.prev=11,e.t0=e["catch"](5);case 14:if(!this.$store){e.next=17;break}return e.next=17,this.$store.dispatch("Logout");case 17:return e.abrupt("return",Promise.resolve());case 21:return e.prev=21,e.t1=e["catch"](0),this.clearLocalUserData(),e.abrupt("return",Promise.reject(e.t1));case 26:case"end":return e.stop()}}),e,this,[[0,21],[5,11]])})));function t(){return e.apply(this,arguments)}return t}(),clearLocalUserData:function(){try{o["default"].ls.remove(h["a"]),o["default"].ls.remove(h["u"]),o["default"].ls.remove(h["v"]),o["default"].ls.remove(h["s"]),o["default"].ls.remove(h["b"]),o["default"].ls.remove(h["r"]),localStorage.removeItem("Access-Token"),localStorage.removeItem("USER_INFO"),localStorage.removeItem("USER_NAME")}catch(e){}},handleTokenExpired:function(){var e=u(i.a.mark((function e(){var t;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=4,this.performLogout();case 4:this.$message.warning("登录已过期，请重新登录"),t=this.$route?this.$route.fullPath:window.location.pathname,t.startsWith("/isystem")||t.startsWith("/dashboard")?this.$router.push({path:"/user/login",query:{redirect:t}}):this.$router.push({path:"/login",query:{redirect:t}}),e.next=14;break;case 10:e.prev=10,e.t0=e["catch"](0),window.location.href="/login";case 14:case"end":return e.stop()}}),e,this,[[0,10]])})));function t(){return e.apply(this,arguments)}return t}()}}},"535e":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var r=a("7550"),i=a("e2e0"),n=a("0fea"),s={components:{JEditableTable:r["default"]},data:function(){return{title:"操作",visible:!1,confirmLoading:!1,model:{},labelCol:{xs:{span:24},sm:{span:6}},wrapperCol:{xs:{span:24},sm:{span:18}}}},methods:{getAllTable:function(){var e=this;if(!(this.refKeys instanceof Array))throw this.throwNotArray("refKeys");var t=this.refKeys.map((function(t){return Object(i["c"])(e,t)}));return Promise.all(t)},eachAllTable:function(e){this.getAllTable().then((function(t){t.forEach((function(t,a){"function"===typeof e&&e(t,a)}))}))},add:function(){var e=this;return new Promise((function(t){e.tableReset(),t()})).then((function(){"function"===typeof e.addBefore&&e.addBefore();var t=e.addDefaultRowNum;"number"!==typeof t&&(t=1),e.eachAllTable((function(e){e.add(t)})),"function"===typeof e.addAfter&&e.addAfter(e.model),e.edit(e.model)}))},edit:function(e){e&&"{}"!=JSON.stringify(e)&&e.id&&this.tableReset(),"function"===typeof this.editBefore&&this.editBefore(e),this.visible=!0,this.activeKey=this.refKeys[0],this.$refs.form.resetFields(),this.model=Object.assign({},e),"function"===typeof this.editAfter&&this.editAfter(this.model)},close:function(){this.visible=!1,this.$emit("close")},tableReset:function(){this.eachAllTable((function(e){e.clearRow()}))},requestSubTableData:function(e,t,a,r){a.loading=!0,Object(n["c"])(e,t).then((function(e){var t=e.result,i=[];t&&(Array.isArray(t)?i=t:Array.isArray(t.records)&&(i=t.records)),a.dataSource=i,"function"===typeof r&&r(e)})).finally((function(){a.loading=!1}))},request:function(e){var t=this,a=this.url.add,r="post";this.model.id&&(a=this.url.edit,r="put"),this.confirmLoading=!0,Object(n["h"])(a,e,r).then((function(e){e.success?(t.$message.success(e.message),t.$emit("ok"),t.close()):t.$message.warning(e.message)})).finally((function(){t.confirmLoading=!1}))},handleChangeTabs:function(e){Object(i["c"])(this,e).then((function(e){e.resetScrollTop()}))},handleCancel:function(){this.close()},handleOk:function(){var e=this;this.getAllTable().then((function(t){return Object(i["d"])(e.$refs.form,e.model,t)})).then((function(t){return e.validateSubForm(t)})).then((function(t){if("function"!==typeof e.classifyIntoFormData)throw e.throwNotFunction("classifyIntoFormData");var a=e.classifyIntoFormData(t);return e.request(a)})).catch((function(t){t.error===i["b"]&&(e.activeKey=null==t.index?e.activeKey:t.paneKey?t.paneKey:e.refKeys[t.index])}))},validateSubForm:function(e){return new Promise((function(t){t(e)}))},throwNotFunction:function(e){return"".concat(e," 未定义或不是一个函数")},throwNotArray:function(e){return"".concat(e," 未定义或不是一个数组")}}}},a73df:function(e,t,a){"use strict";a.d(t,"a",(function(){return v}));var r=a("a34a"),i=a.n(r),n=a("2b0e"),s=a("4360"),o=a("9fb0"),h=a("b27c"),c=a("ef4b");function u(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function l(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?u(Object(a),!0).forEach((function(t){f(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function f(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function b(e,t,a,r,i,n,s){try{var o=e[n](s),h=o.value}catch(c){return void a(c)}o.done?t(h):Promise.resolve(h).then(r,i)}function d(e){return function(){var t=this,a=arguments;return new Promise((function(r,i){var n=e.apply(t,a);function s(e){b(n,r,i,s,o,"next",e)}function o(e){b(n,r,i,s,o,"throw",e)}s(void 0)}))}}var v={data:function(){return{heartbeatConfig:{enabled:!0,interval:3e4,maxRetries:3,retryDelay:5e3,apiKey:"",enableSmartInterval:!0,minInterval:15e3,maxInterval:12e4,enableVisibilityControl:!0,enableNetworkDetection:!0},heartbeatState:{timer:null,isActive:!1,retryCount:0,lastHeartbeatTime:null,consecutiveFailures:0,currentInterval:3e4,isPageVisible:!0,isOnline:!0,lastSuccessTime:null},heartbeatStats:{totalSent:0,totalSuccess:0,totalFailed:0,avgResponseTime:0,lastResponseTime:0}}},mounted:function(){this.initHeartbeat()},beforeDestroy:function(){this.stopHeartbeat(),this.removeEventListeners()},methods:{initHeartbeat:function(){try{if(!this.validateHeartbeatConfig())return;this.setupEventListeners(),this.startHeartbeat()}catch(e){}},validateHeartbeatConfig:function(){if(!this.heartbeatConfig.enabled)return!1;var e=n["default"].ls.get(o["a"]);return!!e&&(!!this.heartbeatConfig.apiKey&&(this.heartbeatConfig.interval<5e3&&(this.heartbeatConfig.interval=5e3),!0))},setupEventListeners:function(){this.heartbeatConfig.enableVisibilityControl&&document.addEventListener("visibilitychange",this.handleVisibilityChange),this.heartbeatConfig.enableNetworkDetection&&(window.addEventListener("online",this.handleNetworkOnline),window.addEventListener("offline",this.handleNetworkOffline)),window.addEventListener("focus",this.handlePageFocus),window.addEventListener("blur",this.handlePageBlur)},removeEventListeners:function(){document.removeEventListener("visibilitychange",this.handleVisibilityChange),window.removeEventListener("online",this.handleNetworkOnline),window.removeEventListener("offline",this.handleNetworkOffline),window.removeEventListener("focus",this.handlePageFocus),window.removeEventListener("blur",this.handlePageBlur)},startHeartbeat:function(){this.heartbeatState.isActive||(this.heartbeatState.isActive=!0,this.heartbeatState.currentInterval=this.heartbeatConfig.interval,this.sendHeartbeat(),this.scheduleNextHeartbeat())},stopHeartbeat:function(){this.heartbeatState.timer&&(clearTimeout(this.heartbeatState.timer),this.heartbeatState.timer=null),this.heartbeatState.isActive=!1},scheduleNextHeartbeat:function(){var e=this;if(this.heartbeatState.isActive){this.heartbeatState.timer&&clearTimeout(this.heartbeatState.timer);var t=this.calculateNextInterval();this.heartbeatState.timer=setTimeout((function(){e.sendHeartbeat(),e.scheduleNextHeartbeat()}),t)}},calculateNextInterval:function(){var e=this.heartbeatConfig.interval;return this.heartbeatConfig.enableSmartInterval&&(e=this.heartbeatState.consecutiveFailures>0?Math.min(e*Math.pow(1.5,this.heartbeatState.consecutiveFailures),this.heartbeatConfig.maxInterval):Math.max(.9*e,this.heartbeatConfig.minInterval)),this.heartbeatState.isPageVisible||(e=Math.min(2*e,this.heartbeatConfig.maxInterval)),this.heartbeatState.isOnline?(this.heartbeatState.currentInterval=e,e):this.heartbeatConfig.maxInterval},sendHeartbeat:function(){var e=d(i.a.mark((function e(){var t,a,r,n;return i.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.heartbeatState.isActive&&this.heartbeatState.isOnline){e.next=2;break}return e.abrupt("return");case 2:return t=Date.now(),this.heartbeatState.lastHeartbeatTime=t,this.heartbeatStats.totalSent++,e.prev=5,a=this.buildHeartbeatData(),r=Object(c["a"])(a.pageType||"default",{enableRetry:!1!==this.heartbeatConfig.enableRetry,enableCache:!1!==this.heartbeatConfig.enableCache}),e.next=10,h["a"].smart(a,{enableCache:r.enableCache,enableRetry:r.enableRetry,retryOptions:{maxRetries:this.heartbeatConfig.maxRetries||3,retryDelay:this.heartbeatConfig.retryDelay||1e3}});case 10:n=e.sent,n.success?this.handleHeartbeatSuccess(n,t):this.handleHeartbeatError(new Error(n.message),t),e.next=17;break;case 14:e.prev=14,e.t0=e["catch"](5),this.handleHeartbeatError(e.t0,t);case 17:case"end":return e.stop()}}),e,this,[[5,14]])})));function t(){return e.apply(this,arguments)}return t}(),buildHeartbeatData:function(){var e=s["a"].getters.userInfo||{},t=this.detectPageType();return{apiKey:this.heartbeatConfig.apiKey,pageType:t,timestamp:Date.now(),userId:e.id||e.username,clientInfo:{userAgent:navigator.userAgent,language:navigator.language,platform:navigator.platform,cookieEnabled:navigator.cookieEnabled,onLine:navigator.onLine},deviceInfo:{screenWidth:screen.width,screenHeight:screen.height,colorDepth:screen.colorDepth,pixelDepth:screen.pixelDepth},pageInfo:{url:window.location.href,title:document.title,referrer:document.referrer,visible:!document.hidden},sessionInfo:{sessionStart:this.heartbeatState.lastSuccessTime||Date.now(),heartbeatCount:this.heartbeatStats.totalSent,successRate:this.calculateSuccessRate()}}},detectPageType:function(){if(this.heartbeatConfig&&this.heartbeatConfig.pageType)return this.heartbeatConfig.pageType;var e=this.$route?this.$route.path:window.location.pathname;if(e.includes("/home")||"/"===e)return"home";if(e.includes("/market"))return"market";if(e.includes("/usercenter")||e.includes("/profile"))return"profile";if(e.includes("/dashboard")||e.includes("/admin"))return"admin";if(e.includes("/tutorial")||e.includes("/help"))return"tutorial";var t=this.$options.name;if(t){var a=t.toLowerCase();if(a.includes("home"))return"home";if(a.includes("market"))return"market";if(a.includes("user")||a.includes("profile"))return"profile";if(a.includes("admin")||a.includes("dashboard"))return"admin";if(a.includes("tutorial"))return"tutorial"}return"default"},handleHeartbeatSuccess:function(e,t){var a=Date.now()-t;this.heartbeatStats.totalSuccess++,this.heartbeatStats.lastResponseTime=a,this.heartbeatStats.avgResponseTime=this.calculateAverageResponseTime(a),this.heartbeatState.consecutiveFailures=0,this.heartbeatState.retryCount=0,this.heartbeatState.lastSuccessTime=Date.now(),this.$emit("heartbeat-success",{response:e,responseTime:a,stats:l({},this.heartbeatStats)})},handleHeartbeatError:function(e,t){var a=Date.now()-t;this.heartbeatStats.totalFailed++,this.heartbeatState.consecutiveFailures++,this.$emit("heartbeat-error",{error:e,responseTime:a,consecutiveFailures:this.heartbeatState.consecutiveFailures,stats:l({},this.heartbeatStats)}),this.handleRetryLogic(e)},handleRetryLogic:function(e){this.heartbeatState.consecutiveFailures>=this.heartbeatConfig.maxRetries?this.pauseHeartbeat():this.isNetworkError(e)&&(this.heartbeatState.currentInterval=this.heartbeatConfig.maxInterval)},pauseHeartbeat:function(){var e=this;this.stopHeartbeat(),setTimeout((function(){e.heartbeatConfig.enabled&&e.heartbeatState.isOnline&&(e.heartbeatState.consecutiveFailures=0,e.startHeartbeat())}),3*this.heartbeatConfig.retryDelay)},isNetworkError:function(e){if(!e)return!1;var t=["NETWORK_ERROR","TIMEOUT","ECONNREFUSED","ENOTFOUND"],a=(e.message||e.toString()).toLowerCase();return t.some((function(e){return a.includes(e.toLowerCase())}))||"NETWORK_ERROR"===e.code},calculateSuccessRate:function(){return 0===this.heartbeatStats.totalSent?100:Math.round(this.heartbeatStats.totalSuccess/this.heartbeatStats.totalSent*100)},calculateAverageResponseTime:function(e){if(1===this.heartbeatStats.totalSuccess)return e;var t=this.heartbeatStats.avgResponseTime*(this.heartbeatStats.totalSuccess-1)+e;return Math.round(t/this.heartbeatStats.totalSuccess)},handleVisibilityChange:function(){this.heartbeatState.isPageVisible=!document.hidden,document.hidden||this.heartbeatState.isActive&&this.sendHeartbeat()},handleNetworkOnline:function(){this.heartbeatState.isOnline=!0,this.heartbeatConfig.enabled&&!this.heartbeatState.isActive&&this.startHeartbeat()},handleNetworkOffline:function(){this.heartbeatState.isOnline=!1},handlePageFocus:function(){this.heartbeatState.isActive&&this.sendHeartbeat()},handlePageBlur:function(){},getHeartbeatStatus:function(){return{isActive:this.heartbeatState.isActive,currentInterval:this.heartbeatState.currentInterval,consecutiveFailures:this.heartbeatState.consecutiveFailures,lastHeartbeatTime:this.heartbeatState.lastHeartbeatTime,lastSuccessTime:this.heartbeatState.lastSuccessTime,isPageVisible:this.heartbeatState.isPageVisible,isOnline:this.heartbeatState.isOnline,stats:l({},this.heartbeatStats),successRate:this.calculateSuccessRate()}},triggerHeartbeat:function(){this.heartbeatState.isActive&&this.heartbeatState.isOnline&&this.sendHeartbeat()},resetHeartbeatStats:function(){this.heartbeatStats={totalSent:0,totalSuccess:0,totalFailed:0,avgResponseTime:0,lastResponseTime:0},this.heartbeatState.consecutiveFailures=0,this.heartbeatState.retryCount=0}}}}}]);