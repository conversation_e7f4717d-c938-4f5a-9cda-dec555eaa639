package org.jeecg.modules.jianyingpro.service.internal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超级剪映小助手数据箱服务 - 复制自JianyingDataboxService
 * 保持所有原有业务逻辑不变，仅修改包名和依赖注入
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class JianyingProDataboxService {

    @Autowired
    private JianyingProTosService tosService;

    // 注意：所有数据箱工具现在都是本地处理，不再需要调用外部Coze API
    
    /**
     * 关键帧数据生成器 - 按竞争对手格式实现
     */
    public JSONObject keyframesInfos(org.jeecg.modules.jianying.dto.KeyframesInfosRequest request) {
        try {
            log.info("开始生成关键帧数据: {}", request.getSummary());

            // 获取参数并去除前后空格
            String ctype = request.getZjCtype() != null ? request.getZjCtype().trim() : null;
            String offsets = request.getZjOffsets() != null ? request.getZjOffsets().trim() : null;
            java.util.List<JSONObject> segmentInfos = request.getZjSegmentInfos();
            String values = request.getZjValues() != null ? request.getZjValues().trim() : null;
            Integer width = request.getZjWidth() != null ? request.getZjWidth() : 1920;
            Integer height = request.getZjHeight() != null ? request.getZjHeight() : 1080;

            // 参数验证
            if (ctype == null || ctype.isEmpty()) {
                throw new RuntimeException("关键帧类型不能为空");
            }

            // 验证ctype是否为支持的枚举值
            java.util.Set<String> supportedCtypes = new java.util.HashSet<>();
            supportedCtypes.add("KFTypePositionX");
            supportedCtypes.add("KFTypePositionY");
            supportedCtypes.add("KFTypeRotation");
            supportedCtypes.add("KFTypeScaleX");
            supportedCtypes.add("KFTypeAlpha");

            if (!supportedCtypes.contains(ctype)) {
                throw new RuntimeException("不支持的关键帧类型: " + ctype + "，支持的类型: KFTypePositionX, KFTypePositionY, KFTypeRotation, KFTypeScaleX, KFTypeAlpha");
            }
            if (offsets == null || offsets.isEmpty()) {
                throw new RuntimeException("关键帧位置不能为空");
            }
            if (segmentInfos == null || segmentInfos.isEmpty()) {
                throw new RuntimeException("轨道数据不能为空");
            }
            if (values == null || values.isEmpty()) {
                throw new RuntimeException("关键帧值不能为空");
            }

            // 验证ctype特定的必需参数
            if ("KFTypePositionX".equals(ctype) && request.getZjWidth() == null) {
                throw new RuntimeException("KFTypePositionX需要提供width参数");
            }
            if ("KFTypePositionY".equals(ctype) && request.getZjHeight() == null) {
                throw new RuntimeException("KFTypePositionY需要提供height参数");
            }

            // 解析偏移量和值
            String[] offsetArray = offsets.split("\\|");
            String[] valueArray = values.split("\\|");

            // 验证数量一致性
            if (offsetArray.length != valueArray.length) {
                throw new RuntimeException("偏移量和值的数量必须一致");
            }

            // 生成关键帧列表
            java.util.List<JSONObject> keyframes = new java.util.ArrayList<>();

            // 错误诊断信息
            int validSegmentCount = 0;
            int invalidFieldCount = 0;
            int invalidDurationCount = 0;
            int invalidOffsetValueCount = 0;

            for (JSONObject segment : segmentInfos) {
                // 获取segment信息
                String segmentId = segment.getString("segment_id");
                Long startTime = segment.getLong("start");
                Long endTime = segment.getLong("end");

                if (segmentId == null || startTime == null || endTime == null) {
                    invalidFieldCount++;
                    log.warn("跳过无效的segment: {}", segment);
                    continue;
                }

                // 计算segment时长
                long segmentDuration = endTime - startTime;

                // 验证时长有效性
                if (segmentDuration <= 0) {
                    invalidDurationCount++;
                    log.warn("跳过无效时长的segment: {} (start={}, end={}, duration={})",
                            segmentId, startTime, endTime, segmentDuration);
                    continue;
                }

                validSegmentCount++;

                // 为每个offset/value对生成关键帧
                for (int i = 0; i < offsetArray.length; i++) {
                    try {
                        // 计算offset（微秒）
                        double offsetPercent = Double.parseDouble(offsetArray[i].trim());
                        long calculatedOffset = (long)(segmentDuration * (offsetPercent / 100.0));

                        // 计算value（根据ctype决定是否归一化）
                        double inputValue = Double.parseDouble(valueArray[i].trim());
                        double calculatedValue;

                        // 根据不同的ctype使用不同的计算逻辑和验证
                        if ("KFTypeAlpha".equals(ctype)) {
                            // 透明度：验证范围0-1，直接使用原始值
                            if (inputValue < 0 || inputValue > 1) {
                                throw new RuntimeException("KFTypeAlpha的值必须在0-1范围内");
                            }
                            calculatedValue = inputValue;
                        } else if ("KFTypeRotation".equals(ctype)) {
                            // 旋转：验证角度范围0-360度，直接使用原始值
                            if (inputValue < 0 || inputValue > 360) {
                                throw new RuntimeException("KFTypeRotation的值必须在0-360范围内");
                            }
                            calculatedValue = inputValue;

                        } else if ("KFTypeScaleX".equals(ctype)) {
                            // 均匀缩放：验证范围0.01-5.0，直接使用原始值
                            if (inputValue < 0.01 || inputValue > 5.0) {
                                throw new RuntimeException("KFTypeScaleX的值必须在0.01-5.0范围内");
                            }
                            calculatedValue = inputValue;
                        } else if ("KFTypePositionX".equals(ctype)) {
                            // X位置：除以width归一化
                            calculatedValue = inputValue / width;
                        } else if ("KFTypePositionY".equals(ctype)) {
                            // Y位置：除以height归一化
                            calculatedValue = inputValue / height;
                        } else {
                            // 默认：除以width归一化
                            calculatedValue = inputValue / width;
                        }

                        // 格式化value，避免科学计数法并去除尾随零（保持与竞争对手一致）
                        java.math.BigDecimal formattedValue = new java.math.BigDecimal(calculatedValue);
                        formattedValue = formattedValue.stripTrailingZeros();

                        // 创建关键帧对象
                        JSONObject keyframe = new JSONObject();
                        keyframe.put("offset", calculatedOffset);
                        keyframe.put("property", ctype);
                        keyframe.put("segment_id", segmentId);
                        keyframe.put("value", formattedValue);

                        keyframes.add(keyframe);
                    } catch (NumberFormatException e) {
                        invalidOffsetValueCount++;
                        log.warn("跳过无效的offset/value: {} / {}", offsetArray[i], valueArray[i]);
                    }
                }
            }

            // 检查是否生成了有效的关键帧，并提供具体的错误诊断
            if (keyframes.isEmpty()) {
                // 构建具体的错误信息
                if (segmentInfos.isEmpty()) {
                    throw new RuntimeException("segment数据为空，无法生成关键帧");
                } else if (invalidFieldCount > 0 && validSegmentCount == 0) {
                    throw new RuntimeException("所有segment数据都缺少必要字段（segment_id、start、end），无法生成关键帧");
                } else if (invalidDurationCount > 0 && validSegmentCount == 0) {
                    throw new RuntimeException("所有segment数据的时间范围无效（start必须小于end），无法生成关键帧");
                } else if (invalidOffsetValueCount > 0 && validSegmentCount > 0) {
                    throw new RuntimeException("offset和value参数格式错误，无法解析为数字");
                } else {
                    throw new RuntimeException("未知原因导致无法生成关键帧，请检查输入参数");
                }
            }

            // 返回竞争对手格式：{"keyframes_infos": "JSON字符串"}
            JSONObject result = new JSONObject();
            result.put("keyframes_infos", JSON.toJSONString(keyframes));

            log.info("关键帧数据生成成功，共生成{}个关键帧", keyframes.size());
            return result;

        } catch (Exception e) {
            log.error("关键帧数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "KEYFRAMES_INFOS_ERROR");
            return errorResult;
        }
    }
    
    /**
     * 按索引循环选择动画（修复随机选择导致的不稳定问题）
     */
    private String selectAnimationByIndex(String animationStr, int index) {
        if (animationStr == null || animationStr.trim().isEmpty()) {
            return null;
        }

        // 同时支持英文|和中文｜分隔符
        String[] animations;
        if (animationStr.contains("|")) {
            animations = animationStr.split("\\|");
        } else if (animationStr.contains("｜")) {
            animations = animationStr.split("｜");
        } else {
            // 没有分隔符，直接返回原字符串
            return animationStr.trim();
        }

        // 过滤空字符串
        java.util.List<String> validAnimations = java.util.Arrays.stream(animations)
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(java.util.stream.Collectors.toList());

        if (validAnimations.isEmpty()) {
            return null;
        }

        // 按索引循环选择动画（修复随机选择问题）
        int animationIndex = index % validAnimations.size();
        String selectedAnimation = validAnimations.get(animationIndex);

        log.debug("Pro版Databox按索引选择动画: 原始={}, 候选={}, 索引={}, 动画索引={}, 选中={}",
                 animationStr, validAnimations, index, animationIndex, selectedAnimation);
        return selectedAnimation;
    }

    /**
     * 图片数据生成器
     */
    public JSONObject imgsInfos(org.jeecg.modules.jianying.dto.ImgsInfosRequest request) {
        try {
            log.info("开始生成图片数据: {}", request.getSummary());

            // 本地处理图片数据生成（不需要调用外部API）
            java.util.List<String> imgs = request.getZjImgs();
            java.util.List<JSONObject> timelines = request.getZjTimelines();
            Integer height = request.getZjHeight() != null ? request.getZjHeight() : 1080;
            Integer width = request.getZjWidth() != null ? request.getZjWidth() : 1920;
            String transition = request.getZjTransition();
            Integer outAnimationDuration = request.getZjOutAnimationDuration();
            String loopAnimation = request.getZjGroupAnimation();
            String outAnimation = request.getZjOutAnimation();
            String inAnimation = request.getZjInAnimation();
            Integer inAnimationDuration = request.getZjInAnimationDuration();
            Integer loopAnimationDuration = request.getZjGroupAnimationDuration();
            Integer transitionDuration = request.getZjTransitionDuration();

            if (imgs == null || imgs.isEmpty()) {
                throw new RuntimeException("图片列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成图片信息JSON字符串（高性能优化，匹配竞争对手格式）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 图片分配逻辑：按索引分配，超出范围则为空字符串
                String imgUrl = (i < imgs.size()) ? imgs.get(i) : "";

                // 直接拼接JSON字符串，保证字段顺序：image_url → width → height → start → end → 其他字段
                jsonBuilder.append("{\"image_url\":\"")
                           .append(imgUrl)
                           .append("\",\"width\":")
                           .append(width)
                           .append(",\"height\":")
                           .append(height)
                           .append(",\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"));

                // 添加动画相关字段（按索引循环选择，修复随机选择问题）
                if (inAnimation != null) {
                    String selectedInAnimation = selectAnimationByIndex(inAnimation, i);
                    if (selectedInAnimation != null) {
                        jsonBuilder.append(",\"in_animation\":\"").append(selectedInAnimation).append("\"");
                    }
                }
                if (outAnimation != null) {
                    String selectedOutAnimation = selectAnimationByIndex(outAnimation, i);
                    if (selectedOutAnimation != null) {
                        jsonBuilder.append(",\"out_animation\":\"").append(selectedOutAnimation).append("\"");
                    }
                }
                if (loopAnimation != null) {
                    String selectedGroupAnimation = selectAnimationByIndex(loopAnimation, i);
                    if (selectedGroupAnimation != null) {
                        jsonBuilder.append(",\"group_animation\":\"").append(selectedGroupAnimation).append("\"");
                    }
                }
                if (inAnimationDuration != null) {
                    jsonBuilder.append(",\"in_animation_duration\":").append(inAnimationDuration);
                }
                if (outAnimationDuration != null) {
                    jsonBuilder.append(",\"out_animation_duration\":").append(outAnimationDuration);
                }
                if (loopAnimationDuration != null) {
                    jsonBuilder.append(",\"group_animation_duration\":").append(loopAnimationDuration);
                }
                if (transition != null) {
                    jsonBuilder.append(",\"transition\":\"").append(transition).append("\"");
                }
                if (transitionDuration != null) {
                    jsonBuilder.append(",\"transition_duration\":").append(transitionDuration);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "图片数据生成成功");

            JSONObject dataObj = new JSONObject();
            dataObj.put("infos", jsonBuilder.toString());
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("图片数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "IMGS_INFOS_ERROR");
            return errorResult;
        }
    }
    
    /**
     * 链接提取器（匹配竞争对手格式）
     */
    public JSONObject getUrl(org.jeecg.modules.jianying.dto.GetUrlRequest request) {
        try {
            log.info("开始提取链接: {}", request.getSummary());

            // 获取输入内容
            String output = request.getZjOutput();
            if (output == null || output.trim().isEmpty()) {
                throw new RuntimeException("输入内容不能为空");
            }

            // 使用正则表达式提取URL
            java.util.List<String> allUrls = new java.util.ArrayList<>();
            String urlPattern = "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(urlPattern);
            java.util.regex.Matcher matcher = pattern.matcher(output);

            while (matcher.find()) {
                allUrls.add(matcher.group());
            }

            // 如果找到URL，返回第一个URL；否则返回原文
            String resultOutput = allUrls.isEmpty() ? output : allUrls.get(0);

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "链接提取成功");

            JSONObject dataObj = new JSONObject();
            dataObj.put("output", resultOutput);
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("链接提取失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "GET_URL_ERROR");
            return errorResult;
        }
    }
    
    /**
     * 音频时间线生成器
     */
    public JSONObject audioTimelines(org.jeecg.modules.jianying.dto.AudioTimelinesRequest request) {
        try {
            log.info("开始生成音频时间线: {}", request.getSummary());

            // 本地处理音频时间线生成（不需要调用外部API）
            java.util.List<String> allLinks = request.getZjLinks();
            if (allLinks == null || allLinks.isEmpty()) {
                throw new RuntimeException("音频链接不能为空");
            }

            // 过滤掉空链接
            java.util.List<String> links = new java.util.ArrayList<>();
            for (String link : allLinks) {
                if (link != null && !link.trim().isEmpty()) {
                    links.add(link);
                }
            }

            if (links.isEmpty()) {
                throw new RuntimeException("没有有效的音频链接");
            }

            log.info("原始链接数: {}, 有效链接数: {}", allLinks.size(), links.size());

            // 生成音频时间线 - 分批并行优化
            java.util.List<JSONObject> timelineList = new java.util.ArrayList<>();
            long currentTime = 0;

            // 分批并行获取音频时长
            log.info("开始分批并行获取{}个音频的时长", links.size());
            long startTime = System.currentTimeMillis();

            java.util.List<Long> durations = getAudioDurationsInBatches(links);

            long endTime = System.currentTimeMillis();
            log.info("分批并行获取音频时长完成，耗时: {}ms", endTime - startTime);

            // 生成时间线
            for (int i = 0; i < links.size(); i++) {
                long audioDuration = durations.get(i);

                JSONObject timeline = new JSONObject();
                timeline.put("start", currentTime);
                timeline.put("end", currentTime + audioDuration);

                timelineList.add(timeline);

                currentTime += audioDuration;
            }

            final long totalDuration = currentTime;

            // 生成all_timelines（整体时间范围）
            java.util.List<JSONObject> allTimelinesList = new java.util.ArrayList<>();
            if (totalDuration > 0) {
                JSONObject allTimeline = new JSONObject();
                allTimeline.put("start", 0);
                allTimeline.put("end", totalDuration);
                allTimelinesList.add(allTimeline);
            }

            // 返回结果 - 完全按照竞争对手格式
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "音频时间线生成成功");
            result.put("data", new JSONObject() {{
                put("timelines", timelineList);
                put("all_timelines", allTimelinesList);
            }});

            return result;
            
        } catch (Exception e) {
            log.error("音频时间线生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "AUDIO_TIMELINES_ERROR");
            return errorResult;
        }
    }
    
    /**
     * 贴纸搜索器（接入剪映官方API）
     */
    public JSONObject searchSticker(org.jeecg.modules.jianying.dto.SearchStickerRequest request) {
        try {
            log.info("开始搜索贴纸: {}", request.getSummary());

            String keyword = request.getZjKeyword();
            if (keyword == null || keyword.trim().isEmpty()) {
                throw new RuntimeException("搜索关键词不能为空");
            }

            // 调用剪映官方贴纸搜索API
            String apiUrl = "https://lv-api-sinfonlinec.ulikecam.com/artist/v1/effect/search";

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("app_id", 3704);
            requestBody.put("count", 50);
            requestBody.put("query", keyword);
            requestBody.put("effect_type", 2);

            log.info("调用剪映官方API: {}, 参数: {}", apiUrl, requestBody.toJSONString());

            // 发送HTTP请求
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_JSON);

            org.springframework.http.HttpEntity<String> entity = new org.springframework.http.HttpEntity<>(
                requestBody.toJSONString(), headers);

            org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();

            // 设置超时时间
            org.springframework.http.client.SimpleClientHttpRequestFactory factory =
                new org.springframework.http.client.SimpleClientHttpRequestFactory();
            factory.setConnectTimeout(20000); // 20秒连接超时
            factory.setReadTimeout(30000);    // 30秒读取超时
            restTemplate.setRequestFactory(factory);

            org.springframework.http.ResponseEntity<String> response = restTemplate.postForEntity(
                apiUrl, entity, String.class);

            log.info("剪映API响应状态: {}", response.getStatusCode());

            // 检查响应内容
            String responseBody = response.getBody();
            if (responseBody == null || responseBody.trim().isEmpty()) {
                log.error("剪映API返回空响应");
                throw new RuntimeException("剪映API返回空响应");
            }

            if (response.getStatusCode() == org.springframework.http.HttpStatus.OK) {
                // 解析剪映API响应
                JSONObject apiResponse = JSONObject.parseObject(response.getBody());

                // 转换为我们的格式
                java.util.List<JSONObject> stickerList = convertJianyingResponse(apiResponse);

                // 返回结果（匹配竞争对手的简洁格式）
                JSONObject result = new JSONObject();
                result.put("success", true);
                result.put("message", "贴纸搜索成功");

                JSONObject dataObj = new JSONObject();
                dataObj.put("data", stickerList);
                dataObj.put("message", "搜索成功");
                result.put("data", dataObj);

                return result;
            } else {
                throw new RuntimeException("剪映API调用失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("贴纸搜索失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "SEARCH_STICKER_ERROR");
            return errorResult;
        }
    }

    /**
     * 转换剪映API响应为我们的格式
     */
    private java.util.List<JSONObject> convertJianyingResponse(JSONObject apiResponse) {
        // 预分配容量，减少内存重新分配
        java.util.List<JSONObject> stickerList = new java.util.ArrayList<>(50);

        try {
            // 检查响应结构
            if (!apiResponse.containsKey("data")) {
                log.error("剪映API响应中没有data字段");
                return stickerList;
            }

            JSONObject dataObj = apiResponse.getJSONObject("data");

            if (!dataObj.containsKey("effect_item_list")) {
                log.error("剪映API响应的data中没有effect_item_list字段");
                return stickerList;
            }

            com.alibaba.fastjson.JSONArray effects = dataObj.getJSONArray("effect_item_list");

            for (int i = 0; i < effects.size(); i++) {
                JSONObject effectItem = effects.getJSONObject(i);

                JSONObject stickerItem = new JSONObject();

                // 从common_attr中提取标题和ID
                JSONObject commonAttr = effectItem.getJSONObject("common_attr");
                String title = commonAttr.getString("title");
                if (title == null || title.trim().isEmpty()) {
                    title = "贴纸" + (i + 1);
                }
                stickerItem.put("title", title);

                // 提取贴纸ID
                String stickerId = commonAttr.getString("effect_id");
                if (stickerId == null) {
                    stickerId = "sticker_" + System.currentTimeMillis() + "_" + i;
                }
                stickerItem.put("sticker_id", stickerId);

                // 直接使用API返回的sticker对象，减少重构建开销
                JSONObject sticker = effectItem.getJSONObject("sticker");

                stickerItem.put("sticker", sticker);
                stickerList.add(stickerItem);
            }

            log.info("剪映API调用成功，返回 {} 个贴纸", stickerList.size());

        } catch (Exception e) {
            log.error("转换剪映API响应失败", e);
            // 如果转换失败，返回空列表
        }

        return stickerList;
    }
    
    /**
     * 时间线生成器
     */
    public JSONObject timelines(org.jeecg.modules.jianying.dto.TimelinesRequest request) {
        try {
            log.info("开始生成时间线: {}", request.getSummary());

            // 本地处理时间线生成（不需要调用外部API）
            Long duration = request.getZjDuration();
            Integer num = request.getZjNum();
            Long start = request.getZjStart() != null ? request.getZjStart().longValue() : 0L;
            Integer type = request.getZjType() != null ? request.getZjType() : 0;

            if (duration == null || duration <= 0) {
                throw new RuntimeException("时长必须大于0");
            }
            if (num == null || num <= 0) {
                throw new RuntimeException("数量必须大于0");
            }

            // 生成时间线数组
            java.util.List<JSONObject> timelineList = new java.util.ArrayList<>();

            if (type == 0) {
                // 0: 平均分
                long segmentDuration = duration / num;
                for (int i = 0; i < num; i++) {
                    JSONObject timeline = new JSONObject();
                    long segmentStart = start + i * segmentDuration;
                    long segmentEnd;

                    // 最后一段使用总的结束时间，避免整数除法余数丢失
                    if (i == num - 1) {
                        segmentEnd = start + duration;
                    } else {
                        segmentEnd = start + (i + 1) * segmentDuration;
                    }

                    // 字段顺序：end → start（匹配竞争对手）
                    timeline.put("end", segmentEnd);
                    timeline.put("start", segmentStart);
                    timelineList.add(timeline);
                }
            } else if (type == 1) {
                // 1: 随机分布
                java.util.Random random = new java.util.Random();
                long totalUsed = 0;
                for (int i = 0; i < num; i++) {
                    long segmentDuration;
                    if (i == num - 1) {
                        // 最后一个片段使用剩余时间
                        segmentDuration = duration - totalUsed;
                    } else {
                        // 随机分配时间，但确保不超过剩余时间
                        long remainingTime = duration - totalUsed;
                        long maxSegment = remainingTime / (num - i);
                        long minSegment = Math.max(maxSegment / 2, 1000000L); // 最少1秒
                        long maxSegmentLimit = Math.min(maxSegment * 2, remainingTime);

                        // 生成指定范围内的随机数
                        if (maxSegmentLimit > minSegment) {
                            segmentDuration = minSegment + (long)(random.nextDouble() * (maxSegmentLimit - minSegment));
                        } else {
                            segmentDuration = minSegment;
                        }
                        segmentDuration = Math.min(segmentDuration, remainingTime);
                    }

                    JSONObject timeline = new JSONObject();
                    long segmentStart = start + totalUsed;
                    long segmentEnd = start + totalUsed + segmentDuration;

                    // 字段顺序：end → start（匹配竞争对手）
                    timeline.put("end", segmentEnd);
                    timeline.put("start", segmentStart);
                    timelineList.add(timeline);

                    totalUsed += segmentDuration;
                }
            } else {
                // 默认平均分
                long segmentDuration = duration / num;
                for (int i = 0; i < num; i++) {
                    JSONObject timeline = new JSONObject();
                    long segmentStart = start + i * segmentDuration;
                    long segmentEnd;

                    // 最后一段使用总的结束时间，避免整数除法余数丢失
                    if (i == num - 1) {
                        segmentEnd = start + duration;
                    } else {
                        segmentEnd = start + (i + 1) * segmentDuration;
                    }

                    // 字段顺序：end → start（匹配竞争对手）
                    timeline.put("end", segmentEnd);
                    timeline.put("start", segmentStart);
                    timelineList.add(timeline);
                }
            }

            // 生成all_timelines（整体时间范围）
            java.util.List<JSONObject> allTimelinesList = new java.util.ArrayList<>();
            JSONObject allTimeline = new JSONObject();
            allTimeline.put("end", start + duration);
            allTimeline.put("start", start);
            allTimelinesList.add(allTimeline);

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "时间线生成成功");

            JSONObject dataObj = new JSONObject();
            dataObj.put("all_timelines", allTimelinesList);
            dataObj.put("timelines", timelineList);
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("时间线生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "TIMELINES_ERROR");
            return errorResult;
        }
    }

    /**
     * 音频数据生成器
     */
    public JSONObject audioInfos(org.jeecg.modules.jianying.dto.AudioInfosRequest request) {
        try {
            log.info("开始生成音频数据: {}", request.getSummary());

            // 本地处理音频数据生成（不需要调用外部API）
            java.util.List<String> mp3Urls = request.getZjMp3Urls();
            java.util.List<JSONObject> timelines = request.getZjTimelines();
            String audioEffect = request.getZjAudioEffect();
            Double volume = request.getZjVolume() != null ? request.getZjVolume() : 1.0;

            if (mp3Urls == null || mp3Urls.isEmpty()) {
                throw new RuntimeException("音频列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            log.info("音频数据生成 - 输入参数: mp3Urls数量={}, timelines数量={}, audioEffect={}, volume={}",
                    mp3Urls.size(), timelines.size(), audioEffect, volume);

            // 生成音频信息JSON字符串（修复：为每个时间线段生成音频对象）
            StringBuilder jsonBuilder = new StringBuilder("[");

            // 修复关键问题：循环所有时间线段，而不是min(mp3Urls.size(), timelines.size())
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                // 音频URL分配策略：循环使用可用的URL，如果没有URL则为空
                String mp3Url = null;
                if (mp3Urls != null && !mp3Urls.isEmpty()) {
                    // 循环使用音频URL：mp3Urls[i % mp3Urls.size()]
                    mp3Url = mp3Urls.get(i % mp3Urls.size());
                }

                if (i > 0) jsonBuilder.append(",");

                jsonBuilder.append("{");

                // 只有当有音频URL时才添加audio_url字段
                if (mp3Url != null && !mp3Url.trim().isEmpty()) {
                    jsonBuilder.append("\"audio_url\":\"").append(mp3Url).append("\",");
                }

                // 添加时间信息
                jsonBuilder.append("\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"));

                // 添加音效字段（在volume前面）
                if (audioEffect != null && !audioEffect.trim().isEmpty()) {
                    jsonBuilder.append(",\"audio_effect\":\"").append(audioEffect).append("\"");
                }

                // 添加音量字段（格式化为整数或小数）
                if (volume != null) {
                    if (volume == volume.intValue()) {
                        // 如果是整数，输出整数格式
                        jsonBuilder.append(",\"volume\":").append(volume.intValue());
                    } else {
                        // 如果是小数，输出小数格式
                        jsonBuilder.append(",\"volume\":").append(volume);
                    }
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            log.info("音频数据生成完成 - 生成了{}个音频对象", timelines.size());
            log.debug("生成的音频数据: {}", jsonBuilder.toString());

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "音频数据生成成功");
            result.put("data", new JSONObject() {{
                put("infos", jsonBuilder.toString());
            }});

            return result;

        } catch (Exception e) {
            log.error("音频数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "AUDIO_INFOS_ERROR");
            return errorResult;
        }
    }

    /**
     * 特效数据生成器
     */
    public JSONObject effectInfos(org.jeecg.modules.jianying.dto.EffectInfosRequest request) {
        try {
            log.info("开始生成特效数据: {}", request.getSummary());

            // 获取输入参数
            java.util.List<String> effects = request.getZjEffects();
            java.util.List<JSONObject> timelines = request.getZjTimelines();

            if (effects == null || effects.isEmpty()) {
                throw new RuntimeException("特效列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成特效信息JSON字符串（匹配竞争对手格式）
            StringBuilder jsonBuilder = new StringBuilder("[");

            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 构建特效信息对象
                jsonBuilder.append("{");

                // 如果有对应的特效，添加effect_title字段
                if (i < effects.size()) {
                    jsonBuilder.append("\"effect_title\":\"").append(effects.get(i)).append("\",");
                }

                // 添加时间信息（字段顺序：start → end）
                jsonBuilder.append("\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"));

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "特效数据生成成功");

            JSONObject dataObj = new JSONObject();
            dataObj.put("infos", jsonBuilder.toString());
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("特效数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "EFFECT_INFOS_ERROR");
            return errorResult;
        }
    }

    /**
     * 字幕数据生成器
     */
    public JSONObject captionInfos(org.jeecg.modules.jianying.dto.CaptionInfosRequest request) {
        try {
            log.info("开始生成字幕数据: {}", request.getSummary());

            // 本地处理字幕数据生成（不需要调用外部API）
            java.util.List<String> texts = request.getZjTexts();
            java.util.List<JSONObject> timelines = request.getZjTimelines();
            Integer fontSize = request.getZjFontSize();  // 不设置默认值
            Integer keywordFontSize = request.getZjKeywordFontSize();  // 不设置默认值
            String keywordColor = request.getZjKeywordColor();  // 不设置默认值
            java.util.List<String> keywords = request.getZjKeywords();
            String inAnimation = request.getZjInAnimation();
            String outAnimation = request.getZjOutAnimation();
            String loopAnimation = request.getZjLoopAnimation();
            Integer inAnimationDuration = request.getZjInAnimationDuration();
            Integer outAnimationDuration = request.getZjOutAnimationDuration();
            Integer loopAnimationDuration = request.getZjLoopAnimationDuration();

            if (texts == null || texts.isEmpty()) {
                throw new RuntimeException("文本列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            log.info("字幕数据生成 - 输入参数: texts数量={}, timelines数量={}", texts.size(), timelines.size());

            // 生成字幕信息JSON字符串（修复：为每个时间线段生成字幕对象）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                // 文本分配策略：按索引分配，超出范围则为空字符串（与imgs_infos、video_infos一致）
                String text = (i < texts.size()) ? texts.get(i) : "";

                if (i > 0) jsonBuilder.append(",");

                // 直接拼接JSON字符串，保证字段顺序：start → end → text → keyword → keyword_color → keyword_font_size → font_size → 动画字段
                jsonBuilder.append("{\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"))
                           .append(",\"text\":\"")
                           .append(text.replace("\"", "\\\""))
                           .append("\"");

                // 处理关键词（匹配竞争对手格式）
                String matchedKeyword = "";
                if (keywords != null && !keywords.isEmpty()) {
                    // 找到当前文本中匹配的第一个关键词
                    for (String keyword : keywords) {
                        if (text.contains(keyword)) {
                            matchedKeyword = keyword;
                            break;
                        }
                    }
                }

                // 只有当用户传入了关键词参数时，才添加关键词相关字段
                if (keywords != null && !keywords.isEmpty()) {
                    // 添加关键词字段（单数，字符串格式）
                    jsonBuilder.append(",\"keyword\":\"").append(matchedKeyword).append("\"");

                    // 添加关键词样式字段（只有用户传入时才显示）
                    if (keywordColor != null) {
                        jsonBuilder.append(",\"keyword_color\":\"").append(keywordColor).append("\"");
                    }
                    if (keywordFontSize != null) {
                        jsonBuilder.append(",\"keyword_font_size\":").append(keywordFontSize);
                    }
                }

                // 添加字体相关字段（只有用户传入时才显示）
                if (fontSize != null) {
                    jsonBuilder.append(",\"font_size\":").append(fontSize);
                }

                // 添加动画相关字段（按索引循环选择，修复随机选择问题）
                if (inAnimation != null) {
                    String selectedInAnimation = selectAnimationByIndex(inAnimation, i);
                    if (selectedInAnimation != null) {
                        jsonBuilder.append(",\"in_animation\":\"").append(selectedInAnimation).append("\"");
                    }
                }
                if (outAnimation != null) {
                    String selectedOutAnimation = selectAnimationByIndex(outAnimation, i);
                    if (selectedOutAnimation != null) {
                        jsonBuilder.append(",\"out_animation\":\"").append(selectedOutAnimation).append("\"");
                    }
                }
                if (loopAnimation != null) {
                    String selectedLoopAnimation = selectAnimationByIndex(loopAnimation, i);
                    if (selectedLoopAnimation != null) {
                        jsonBuilder.append(",\"loop_animation\":\"").append(selectedLoopAnimation).append("\"");
                    }
                }
                if (inAnimationDuration != null) {
                    jsonBuilder.append(",\"in_animation_duration\":").append(inAnimationDuration);
                }
                if (outAnimationDuration != null) {
                    jsonBuilder.append(",\"out_animation_duration\":").append(outAnimationDuration);
                }
                if (loopAnimationDuration != null) {
                    jsonBuilder.append(",\"loop_animation_duration\":").append(loopAnimationDuration);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            log.info("字幕数据生成完成 - 生成了{}个字幕对象", timelines.size());
            log.debug("生成的字幕数据: {}", jsonBuilder.toString());

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "字幕数据生成成功");
            result.put("data", new JSONObject() {{
                put("infos", jsonBuilder.toString());
            }});

            return result;

        } catch (Exception e) {
            log.error("字幕数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "CAPTION_INFOS_ERROR");
            return errorResult;
        }
    }

    /**
     * 视频数据生成器
     */
    public JSONObject videoInfos(org.jeecg.modules.jianying.dto.VideoInfosRequest request) {
        try {
            log.info("开始生成视频数据: {}", request.getSummary());

            // 本地处理视频数据生成（不需要调用外部API）
            java.util.List<String> videoUrls = request.getZjVideoUrls();
            java.util.List<JSONObject> timelines = request.getZjTimelines();
            String mask = request.getZjMask();
            Integer height = request.getZjHeight() != null ? request.getZjHeight() : 1080;
            Integer width = request.getZjWidth() != null ? request.getZjWidth() : 1920;
            String transition = request.getZjTransition();
            Integer transitionDuration = request.getZjTransitionDuration();
            Double volume = request.getZjVolume() != null ? request.getZjVolume() : 1.0;

            if (videoUrls == null || videoUrls.isEmpty()) {
                throw new RuntimeException("视频列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成视频信息JSON字符串（高性能优化，匹配竞争对手格式）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 视频分配逻辑：按索引分配，超出范围则为空字符串
                String videoUrl = (i < videoUrls.size()) ? videoUrls.get(i) : "";

                // 计算duration
                long duration = timeline.getLong("end") - timeline.getLong("start");

                // 直接拼接JSON字符串，保证字段顺序：video_url → width → height → start → end → duration → mask → volume → transition → transition_duration
                jsonBuilder.append("{\"video_url\":\"")
                           .append(videoUrl)
                           .append("\",\"width\":")
                           .append(width)
                           .append(",\"height\":")
                           .append(height)
                           .append(",\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"))
                           .append(",\"duration\":")
                           .append(duration);

                // 添加其他字段
                if (mask != null) {
                    jsonBuilder.append(",\"mask\":\"").append(mask).append("\"");
                }

                // 添加音量字段（格式化为整数或小数）
                if (volume != null) {
                    if (volume == volume.intValue()) {
                        jsonBuilder.append(",\"volume\":").append(volume.intValue());
                    } else {
                        jsonBuilder.append(",\"volume\":").append(volume);
                    }
                }

                if (transition != null) {
                    jsonBuilder.append(",\"transition\":\"").append(transition).append("\"");
                }
                if (transitionDuration != null) {
                    jsonBuilder.append(",\"transition_duration\":").append(transitionDuration);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            // 返回结果（匹配竞争对手的简洁格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "视频数据生成成功");

            JSONObject dataObj = new JSONObject();
            dataObj.put("infos", jsonBuilder.toString());
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("视频数据生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "VIDEO_INFOS_ERROR");
            return errorResult;
        }
    }

    /**
     * 字符串列表转对象
     */
    public JSONObject strListToObjs(org.jeecg.modules.jianying.dto.StrListToObjsRequest request) {
        try {
            log.info("开始字符串列表转对象: {}", request.getSummary());

            // 本地处理字符串列表转对象（不需要调用外部API）
            java.util.List<String> infosList = request.getZjInfos();
            log.info("接收到的zjInfos: {}", infosList);

            if (infosList == null || infosList.isEmpty()) {
                log.error("输入信息为空: {}", infosList);
                throw new RuntimeException("输入信息不能为空");
            }

            // 将字符串列表转为对象列表
            java.util.List<JSONObject> objectList = new java.util.ArrayList<>();
            for (String item : infosList) {
                if (item != null && !item.trim().isEmpty()) {
                    JSONObject obj = new JSONObject();
                    obj.put("output", item.trim());
                    objectList.add(obj);
                }
            }

            log.info("转换后的对象列表大小: {}", objectList.size());
            log.info("转换后的对象列表: {}", objectList);

            // 返回结果
            JSONObject dataObj = new JSONObject();
            dataObj.put("infos", objectList);  // 使用 "infos"

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "字符串列表转对象成功");
            result.put("data", dataObj);

            log.info("最终返回结果: {}", result.toJSONString());

            return result;

        } catch (Exception e) {
            log.error("字符串列表转对象失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "STR_LIST_TO_OBJS_ERROR");
            return errorResult;
        }
    }

    /**
     * 对象转字符串列表
     */
    public JSONObject objsToStrList(org.jeecg.modules.jianying.dto.ObjsToStrListRequest request) {
        try {
            log.info("开始对象转字符串列表: {}", request.getSummary());

            // 本地处理对象转字符串列表（不需要调用外部API）
            java.util.List<Object> outputsList = request.getZjOutputs();
            if (outputsList == null || outputsList.isEmpty()) {
                throw new RuntimeException("输入对象不能为空");
            }

            // 将对象列表转为字符串列表（提取zj_outputs字段值）
            java.util.List<String> strList = new java.util.ArrayList<>();
            for (Object obj : outputsList) {
                if (obj != null) {
                    if (obj instanceof JSONObject) {
                        JSONObject jsonObj = (JSONObject) obj;
                        // 提取zj_outputs字段的值
                        if (jsonObj.containsKey("zj_outputs")) {
                            Object zjOutputsValue = jsonObj.get("zj_outputs");
                            if (zjOutputsValue != null) {
                                strList.add(zjOutputsValue.toString());
                            }
                        } else {
                            // 如果没有zj_outputs字段，转为JSON字符串
                            strList.add(jsonObj.toJSONString());
                        }
                    } else if (obj instanceof java.util.Map) {
                        @SuppressWarnings("unchecked")
                        java.util.Map<String, Object> map = (java.util.Map<String, Object>) obj;
                        // 提取zj_outputs字段的值
                        if (map.containsKey("zj_outputs")) {
                            Object zjOutputsValue = map.get("zj_outputs");
                            if (zjOutputsValue != null) {
                                strList.add(zjOutputsValue.toString());
                            }
                        } else {
                            // 如果没有zj_outputs字段，转为字符串
                            strList.add(obj.toString());
                        }
                    } else {
                        // 其他类型直接转为字符串
                        strList.add(obj.toString());
                    }
                }
            }

            // 返回结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "对象转字符串列表成功");
            result.put("data", new JSONObject() {{
                put("str_list", strList);
            }});

            return result;

        } catch (Exception e) {
            log.error("对象转字符串列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "OBJS_TO_STR_LIST_ERROR");
            return errorResult;
        }
    }

    /**
     * 字符串转列表
     */
    public JSONObject strToList(org.jeecg.modules.jianying.dto.StrToListRequest request) {
        try {
            log.info("开始字符串转列表: {}", request.getSummary());

            // 本地处理字符串转列表（不需要调用外部API）
            String inputStr = request.getZjObj();
            if (inputStr == null || inputStr.trim().isEmpty()) {
                throw new RuntimeException("输入字符串不能为空");
            }

            // 按逗号分割字符串转为列表
            String[] items = inputStr.split(",");
            java.util.List<String> resultList = new java.util.ArrayList<>();
            for (String item : items) {
                String trimmedItem = item.trim();
                if (!trimmedItem.isEmpty()) {
                    resultList.add(trimmedItem);
                }
            }

            // 返回结果
            JSONObject dataObj = new JSONObject();
            dataObj.put("list", resultList);

            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "字符串转列表成功");
            result.put("data", dataObj);

            return result;

        } catch (Exception e) {
            log.error("字符串转列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "STR_TO_LIST_ERROR");
            return errorResult;
        }
    }

    /**
     * 语音识别时间线
     */
    public JSONObject asrTimelines(org.jeecg.modules.jianying.dto.AsrTimelinesRequest request) {
        try {
            log.info("开始语音识别时间线: {}", request.getSummary());

            // 本地处理语音识别时间线（不需要调用外部API）
            java.util.List<JSONObject> contentChunks = request.getZjContentChunks();
            if (contentChunks == null || contentChunks.isEmpty()) {
                throw new RuntimeException("语音识别内容不能为空");
            }

            // 处理语音识别时间线
            java.util.List<JSONObject> timelineList = new java.util.ArrayList<>();
            long maxDuration = 0;

            for (int i = 0; i < contentChunks.size(); i++) {
                JSONObject chunk = contentChunks.get(i);
                JSONObject timeline = new JSONObject();

                // 从chunk中提取时间信息
                Long startTime = chunk.getLong("start_time");
                Long endTime = chunk.getLong("end_time");
                String text = chunk.getString("text");

                if (startTime == null) startTime = 0L;
                if (endTime == null) endTime = startTime + 2000000L; // 默认2秒

                timeline.put("start", startTime);
                timeline.put("end", endTime);
                timeline.put("duration", endTime - startTime);
                timeline.put("text", text != null ? text : "");
                timeline.put("index", i);

                timelineList.add(timeline);

                // 计算总时长
                if (endTime > maxDuration) {
                    maxDuration = endTime;
                }
            }

            final long totalDuration = maxDuration;

            // 返回结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "语音识别时间线生成成功");
            result.put("data", new JSONObject() {{
                put("timelines", timelineList);
                put("total_duration", totalDuration);
            }});

            return result;

        } catch (Exception e) {
            log.error("语音识别时间线生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", e.getMessage());
            errorResult.put("code", "ASR_TIMELINES_ERROR");
            return errorResult;
        }
    }

    /**
     * 分批并行获取多个音频的时长
     */
    private java.util.List<Long> getAudioDurationsInBatches(java.util.List<String> audioUrls) {
        final int BATCH_SIZE = 4; // 每批4个音频并行处理（更安全）
        java.util.List<Long> allDurations = new java.util.ArrayList<>();

        // 分批处理
        for (int batchStart = 0; batchStart < audioUrls.size(); batchStart += BATCH_SIZE) {
            int batchEnd = Math.min(batchStart + BATCH_SIZE, audioUrls.size());
            java.util.List<String> batch = audioUrls.subList(batchStart, batchEnd);

            log.info("处理第{}批音频，数量: {} (并发数: {})", (batchStart / BATCH_SIZE + 1), batch.size(), BATCH_SIZE);

            // 并行处理当前批次
            long batchStartTime = System.currentTimeMillis();
            java.util.List<Long> batchDurations = processBatchParallel(batch);
            allDurations.addAll(batchDurations);
            long batchEndTime = System.currentTimeMillis();

            log.info("第{}批处理完成，耗时: {}ms", (batchStart / BATCH_SIZE + 1), batchEndTime - batchStartTime);

            // 批次间添加小延迟，避免过于频繁的请求
            if (batchEnd < audioUrls.size()) {
                try {
                    Thread.sleep(50); // 50ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        return allDurations;
    }

    /**
     * 并行处理一批音频
     */
    private java.util.List<Long> processBatchParallel(java.util.List<String> batch) {
        java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(batch.size());
        java.util.List<java.util.concurrent.Future<Long>> futures = new java.util.ArrayList<>();

        try {
            // 提交所有任务
            for (String audioUrl : batch) {
                java.util.concurrent.Future<Long> future = executor.submit(() -> {
                    return getRealAudioDurationOptimized(audioUrl);
                });
                futures.add(future);
            }

            // 收集结果（保持顺序）
            java.util.List<Long> durations = new java.util.ArrayList<>();
            for (int i = 0; i < futures.size(); i++) {
                try {
                    Long duration = futures.get(i).get(8, java.util.concurrent.TimeUnit.SECONDS); // 每个音频最多8秒（更宽松）
                    durations.add(duration);
                    log.debug("批次内音频 {} 解析完成: {} 微秒", i + 1, duration);
                } catch (java.util.concurrent.TimeoutException e) {
                    log.warn("批次内音频 {} 解析超时，使用默认值", i + 1);
                    durations.add(5000000L); // 默认5秒
                } catch (Exception e) {
                    log.warn("批次内音频 {} 解析失败，使用默认值: {}", i + 1, e.getMessage());
                    durations.add(5000000L); // 默认5秒
                }
            }

            return durations;

        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, java.util.concurrent.TimeUnit.SECONDS)) {
                    log.warn("批次处理线程池关闭超时，强制关闭");
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("批次处理线程池关闭被中断");
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 优化版音频时长获取（TOS文件使用SDK，非TOS文件使用HTTP）
     */
    public long getRealAudioDurationOptimized(String audioUrl) {
        try {
            log.debug("开始获取音频时长: {}", audioUrl);

            // 判断是否为TOS文件（支持多种字节跳动TOS域名）
            if (isTosUrl(audioUrl)) {
                return getRealAudioDurationWithTosSDK(audioUrl);
            } else {
                return getRealAudioDurationWithHttp(audioUrl);
            }

        } catch (Exception e) {
            log.debug("获取音频时长失败: {}", audioUrl, e);
            // 兜底：5秒
            log.debug("使用默认时长5秒: {}", audioUrl);
            return 5000000L;
        }
    }

    /**
     * 使用TOS SDK获取音频时长（Range请求前8KB）
     */
    private long getRealAudioDurationWithTosSDK(String audioUrl) {
        try {
            log.debug("使用TOS SDK获取音频时长: {}", audioUrl);

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(audioUrl);

            // 使用TOS SDK下载前8KB数据
            // 注意：这里需要使用TOS SDK的Range请求功能
            // 由于当前TOS SDK可能不支持Range，暂时使用HTTP方式作为降级
            return getRealAudioDurationWithHttp(audioUrl);

        } catch (Exception e) {
            log.debug("TOS SDK获取音频时长失败: {}", audioUrl, e);
            return 5000000L; // 兜底：5秒
        }
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String url) {
        try {
            int pathStart = url.indexOf("/jianying-assistant/");
            if (pathStart == -1) {
                throw new RuntimeException("无法从URL中提取文件路径: " + url);
            }
            return url.substring(pathStart);
        } catch (Exception e) {
            throw new RuntimeException("提取文件路径失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用HTTP方式获取音频时长（保留原有逻辑）
     */
    private long getRealAudioDurationWithHttp(String audioUrl) {
        try {
            log.debug("使用HTTP方式获取音频时长: {}", audioUrl);

            // 使用连接池和Keep-Alive优化
            java.net.URL url = new java.net.URL(audioUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(3000); // 减少连接超时
            connection.setReadTimeout(5000);    // 减少读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Connection", "keep-alive"); // 启用Keep-Alive
            connection.setRequestProperty("Range", "bytes=0-8192"); // 只下载前8KB

            int responseCode = connection.getResponseCode();
            if (responseCode == 206 || responseCode == 200) {

                try (java.io.InputStream inputStream = connection.getInputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead = inputStream.read(buffer);

                    if (bytesRead > 0) {
                        // 解析MP3头部信息
                        long duration = parseMP3Duration(buffer, bytesRead, audioUrl);
                        if (duration > 0) {
                            log.debug("成功解析音频时长: {} 微秒", duration);
                            return duration;
                        }
                    }
                }
            }

            connection.disconnect();

        } catch (Exception e) {
            log.debug("HTTP获取音频时长失败: {}", audioUrl, e);
        }

        // 兜底：5秒
        log.debug("使用默认时长5秒: {}", audioUrl);
        return 5000000L;
    }



    /**
     * 解析MP3文件时长
     */
    private long parseMP3Duration(byte[] buffer, int length, String audioUrl) {
        try {
            // 查找MP3帧头 (11111111 111xxxxx)
            for (int i = 0; i < length - 4; i++) {
                if ((buffer[i] & 0xFF) == 0xFF && (buffer[i + 1] & 0xE0) == 0xE0) {
                    // 找到MP3帧头，解析帧信息
                    int header = ((buffer[i] & 0xFF) << 24) |
                                ((buffer[i + 1] & 0xFF) << 16) |
                                ((buffer[i + 2] & 0xFF) << 8) |
                                (buffer[i + 3] & 0xFF);

                    // 解析比特率和采样率
                    int version = (header >> 19) & 3;
                    int layer = (header >> 17) & 3;
                    int bitrateIndex = (header >> 12) & 15;
                    int sampleRateIndex = (header >> 10) & 3;

                    if (bitrateIndex != 0 && bitrateIndex != 15 && sampleRateIndex != 3) {
                        // 获取比特率和采样率
                        int bitrate = getBitrate(version, layer, bitrateIndex);
                        int sampleRate = getSampleRate(version, sampleRateIndex);

                        if (bitrate > 0 && sampleRate > 0) {
                            // 通过HTTP HEAD获取文件总大小
                            long fileSize = getFileSize(audioUrl);
                            if (fileSize > 0) {
                                // 计算时长：文件大小 / (比特率 / 8) = 秒数
                                double durationSeconds = (double) fileSize / ((double) bitrate * 1000 / 8);
                                return (long) (durationSeconds * 1000000); // 转换为微秒
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析MP3时长失败: {}", audioUrl, e);
        }
        return 0;
    }

    /**
     * 获取文件大小（TOS文件使用headObject，非TOS文件使用HTTP HEAD）
     */
    private long getFileSize(String audioUrl) {
        try {
            // 判断是否为TOS文件
            if (audioUrl.contains("tos-cn-guangzhou.volces.com")) {
                return getFileSizeWithTosSDK(audioUrl);
            } else {
                return getFileSizeWithHttp(audioUrl);
            }
        } catch (Exception e) {
            log.debug("获取文件大小失败: {}", audioUrl, e);
            return 0;
        }
    }

    /**
     * 使用TOS SDK获取文件大小
     */
    private long getFileSizeWithTosSDK(String audioUrl) {
        try {
            log.debug("使用TOS SDK获取文件大小: {}", audioUrl);

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(audioUrl);

            // 使用TOS SDK的headObject方法
            // 注意：这里需要使用TOS SDK的headObject功能
            // 由于当前TOS SDK可能不支持headObject，暂时使用HTTP方式作为降级
            return getFileSizeWithHttp(audioUrl);

        } catch (Exception e) {
            log.debug("TOS SDK获取文件大小失败: {}", audioUrl, e);
            return 0;
        }
    }

    /**
     * 使用HTTP HEAD请求获取文件大小（保留原有逻辑）
     */
    private long getFileSizeWithHttp(String audioUrl) {
        try {
            log.debug("使用HTTP HEAD获取文件大小: {}", audioUrl);

            java.net.URL url = new java.net.URL(audioUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);

            long size = connection.getContentLengthLong();
            connection.disconnect();
            return size;
        } catch (Exception e) {
            log.debug("HTTP HEAD获取文件大小失败: {}", audioUrl, e);
            return 0;
        }
    }

    /**
     * 获取MP3比特率
     */
    private int getBitrate(int version, int layer, int bitrateIndex) {
        // MP3比特率表（简化版）
        int[][][] bitrateTable = {
            // MPEG 1
            {
                {0, 32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448}, // Layer I
                {0, 32, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320, 384}, // Layer II
                {0, 32, 40, 48, 56, 64, 80, 96, 112, 128, 160, 192, 224, 256, 320}  // Layer III
            },
            // MPEG 2
            {
                {0, 32, 48, 56, 64, 80, 96, 112, 128, 144, 160, 176, 192, 224, 256}, // Layer I
                {0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160}, // Layer II
                {0, 8, 16, 24, 32, 40, 48, 56, 64, 80, 96, 112, 128, 144, 160}  // Layer III
            }
        };

        try {
            int versionIndex = (version == 3) ? 0 : 1; // MPEG 1 or MPEG 2
            int layerIndex = 3 - layer; // Layer I=0, II=1, III=2
            return bitrateTable[versionIndex][layerIndex][bitrateIndex];
        } catch (Exception e) {
            return 128; // 默认128kbps
        }
    }

    /**
     * 获取MP3采样率
     */
    private int getSampleRate(int version, int sampleRateIndex) {
        int[][] sampleRateTable = {
            {44100, 48000, 32000}, // MPEG 1
            {22050, 24000, 16000}  // MPEG 2
        };

        try {
            int versionIndex = (version == 3) ? 0 : 1;
            return sampleRateTable[versionIndex][sampleRateIndex];
        } catch (Exception e) {
            return 44100; // 默认44.1kHz
        }
    }



}
