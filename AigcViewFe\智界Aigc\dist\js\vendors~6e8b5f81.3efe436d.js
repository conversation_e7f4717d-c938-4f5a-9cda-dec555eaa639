(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["vendors~6e8b5f81"],{"1d73":function(t,e,n){"use strict";var i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var r=i(n("7746"));e.generate=r.default;var o={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1890FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"};e.presetPrimaryColors=o;var a={};e.presetPalettes=a,Object.keys(o).forEach((function(t){a[t]=r.default(o[t]),a[t].primary=a[t][5]}));var s=a.red;e.red=s;var h=a.volcano;e.volcano=h;var l=a.gold;e.gold=l;var u=a.orange;e.orange=u;var c=a.yellow;e.yellow=c;var d=a.lime;e.lime=d;var f=a.green;e.green=f;var p=a.cyan;e.cyan=p;var g=a.blue;e.blue=g;var v=a.geekblue;e.geekblue=v;var y=a.purple;e.purple=y;var m=a.magenta;e.magenta=m;var x=a.grey;e.grey=x},"1f38":function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return r}));var i=function(){return"undefined"!==typeof window?window:t},r=function(){var t=i();return t&&t.tinymce?t.tinymce:null}}).call(this,n("c8ba"))},"2adb":function(t,e,n){"use strict";(function(t){n.d(e,"e",(function(){return u})),n.d(e,"d",(function(){return c})),n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return p})),n.d(e,"c",(function(){return g})),n.d(e,"f",(function(){return v}));var i=n("41b2"),r=n.n(i),o=n("8827"),a=n.n(o),s=n("57ba"),h=n.n(s),l=n("1d73");function u(e){!t||Object({NODE_ENV:"production",VUE_APP_API_BASE_URL:"https://www.aigcview.cn/jeecg-boot",VUE_APP_CAS_BASE_URL:"https://www.aigcview.cn/jeecg-boot/cas",VUE_APP_ONLINE_BASE_URL:"https://www.aigcview.cn/jeecg-boot/onlinePreview",VUE_APP_PLATFORM_NAME:"JeecgBoot 企业级低代码平台",VUE_APP_SSO:"false",BASE_URL:"/"})}function c(t){return"object"===typeof t&&"string"===typeof t.name&&"string"===typeof t.theme&&("object"===typeof t.icon||"function"===typeof t.icon)}function d(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(t).reduce((function(e,n){var i=t[n];switch(n){case"class":e.className=i,delete e["class"];break;default:e[n]=i}return e}),{})}var f=function(){function t(){a()(this,t),this.collection={}}return h()(t,[{key:"clear",value:function(){this.collection={}}},{key:"delete",value:function(t){return delete this.collection[t]}},{key:"get",value:function(t){return this.collection[t]}},{key:"has",value:function(t){return Boolean(this.collection[t])}},{key:"set",value:function(t,e){return this.collection[t]=e,this}},{key:"size",get:function(){return Object.keys(this.collection).length}}]),t}();function p(t,e,n,i){return t(e.tag,i?r()({key:n},i,{attrs:r()({},d(e.attrs),i.attrs)}):{key:n,attrs:r()({},d(e.attrs))},(e.children||[]).map((function(i,r){return p(t,i,n+"-"+e.tag+"-"+r)})))}function g(t){return Object(l["generate"])(t)[0]}function v(t,e){switch(e){case"fill":return t+"-fill";case"outline":return t+"-o";case"twotone":return t+"-twotone";default:throw new TypeError("Unknown theme type: "+e+", name: "+t)}}}).call(this,n("f28c"))},7746:function(t,e,n){"use strict";var i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var r=i(n("66cb")),o=2,a=16,s=5,h=5,l=15,u=5,c=4;function d(t,e,n){var i;return i=Math.round(t.h)>=60&&Math.round(t.h)<=240?n?Math.round(t.h)-o*e:Math.round(t.h)+o*e:n?Math.round(t.h)+o*e:Math.round(t.h)-o*e,i<0?i+=360:i>=360&&(i-=360),i}function f(t,e,n){return 0===t.h&&0===t.s?t.s:(i=n?Math.round(100*t.s)-a*e:e===c?Math.round(100*t.s)+a:Math.round(100*t.s)+s*e,i>100&&(i=100),n&&e===u&&i>10&&(i=10),i<6&&(i=6),i);var i}function p(t,e,n){return n?Math.round(100*t.v)+h*e:Math.round(100*t.v)-l*e}function g(t){for(var e=[],n=r.default(t),i=u;i>0;i-=1){var o=n.toHsv(),a=r.default({h:d(o,i,!0),s:f(o,i,!0),v:p(o,i,!0)}).toHexString();e.push(a)}e.push(n.toHexString());for(i=1;i<=c;i+=1){o=n.toHsv(),a=r.default({h:d(o,i),s:f(o,i),v:p(o,i)}).toHexString();e.push(a)}return e}e.default=g},8520:function(t,e,n){"use strict";var i=n("41b2"),r=n.n(i),o=n("2adb"),a={primaryColor:"#333",secondaryColor:"#E6E6E6"},s={name:"AntdIcon",props:["type","primaryColor","secondaryColor"],displayName:"IconVue",definitions:new o["a"],data:function(){return{twoToneColorPalette:a}},add:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];e.forEach((function(t){s.definitions.set(Object(o["f"])(t.name,t.theme),t)}))},clear:function(){s.definitions.clear()},get:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a;if(t){var n=s.definitions.get(t);return n&&"function"===typeof n.icon&&(n=r()({},n,{icon:n.icon(e.primaryColor,e.secondaryColor)})),n}},setTwoToneColors:function(t){var e=t.primaryColor,n=t.secondaryColor;a.primaryColor=e,a.secondaryColor=n||Object(o["c"])(e)},getTwoToneColors:function(){return r()({},a)},render:function(t){var e=this.$props,n=e.type,i=e.primaryColor,h=e.secondaryColor,l=void 0,u=a;if(i&&(u={primaryColor:i,secondaryColor:h||Object(o["c"])(i)}),Object(o["d"])(n))l=n;else if("string"===typeof n&&(l=s.get(n,u),!l))return null;return l?(l&&"function"===typeof l.icon&&(l=r()({},l,{icon:l.icon(u.primaryColor,u.secondaryColor)})),Object(o["b"])(t,l.icon,"svg-"+l.name,{attrs:{"data-icon":l.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},on:this.$listeners})):(Object(o["e"])("type should be string or icon definiton, but got "+n),null)},install:function(t){t.component(s.name,s)}},h=s;e["a"]=h},"98b8":function(t,e,n){var i=function(t){"use strict";var e,n=Object.prototype,i=n.hasOwnProperty,r="function"===typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function h(t,e,n,i){var r=e&&e.prototype instanceof g?e:g,o=Object.create(r.prototype),a=new L(i||[]);return o._invoke=C(t,n,a),o}function l(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(i){return{type:"throw",arg:i}}}t.wrap=h;var u="suspendedStart",c="suspendedYield",d="executing",f="completed",p={};function g(){}function v(){}function y(){}var m={};m[o]=function(){return this};var x=Object.getPrototypeOf,w=x&&x(x(P([])));w&&w!==n&&i.call(w,o)&&(m=w);var _=y.prototype=g.prototype=Object.create(m);function b(t){["next","throw","return"].forEach((function(e){t[e]=function(t){return this._invoke(e,t)}}))}function E(t,e){function n(r,o,a,s){var h=l(t[r],t,o);if("throw"!==h.type){var u=h.arg,c=u.value;return c&&"object"===typeof c&&i.call(c,"__await")?e.resolve(c.__await).then((function(t){n("next",t,a,s)}),(function(t){n("throw",t,a,s)})):e.resolve(c).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,s)}))}s(h.arg)}var r;function o(t,i){function o(){return new e((function(e,r){n(t,i,e,r)}))}return r=r?r.then(o,o):o()}this._invoke=o}function C(t,e,n){var i=u;return function(r,o){if(i===d)throw new Error("Generator is already running");if(i===f){if("throw"===r)throw o;return D()}n.method=r,n.arg=o;while(1){var a=n.delegate;if(a){var s=S(a,n);if(s){if(s===p)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===u)throw i=f,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=d;var h=l(t,e,n);if("normal"===h.type){if(i=n.done?f:c,h.arg===p)continue;return{value:h.arg,done:n.done}}"throw"===h.type&&(i=f,n.method="throw",n.arg=h.arg)}}}function S(t,n){var i=t.iterator[n.method];if(i===e){if(n.delegate=null,"throw"===n.method){if(t.iterator["return"]&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method))return p;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=l(i,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,p;var o=r.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,p):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,p)}function M(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function P(t){if(t){var n=t[o];if(n)return n.call(t);if("function"===typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function n(){while(++r<t.length)if(i.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}return{next:D}}function D(){return{value:e,done:!0}}return v.prototype=_.constructor=y,y.constructor=v,y[s]=v.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var e="function"===typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,s in t||(t[s]="GeneratorFunction")),t.prototype=Object.create(_),t},t.awrap=function(t){return{__await:t}},b(E.prototype),E.prototype[a]=function(){return this},t.AsyncIterator=E,t.async=function(e,n,i,r,o){void 0===o&&(o=Promise);var a=new E(h(e,n,i,r),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(_),_[s]="Generator",_[o]=function(){return this},_.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var n in t)e.push(n);return e.reverse(),function n(){while(e.length){var i=e.pop();if(i in t)return n.value=i,n.done=!1,n}return n.done=!0,n}},t.values=P,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0],e=t.completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(i,r){return s.type="throw",s.arg=t,n.next=i,r&&(n.method="next",n.arg=e),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],s=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var h=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(h&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(h){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var i=n.completion;if("throw"===i.type){var r=i.arg;O(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,i){return this.delegate={iterator:P(t),resultName:n,nextLoc:i},"next"===this.method&&(this.arg=e),p}},t}(t.exports);try{regeneratorRuntime=i}catch(r){Function("r","regeneratorRuntime = r")(i)}},9917:function(t,e,n){"use strict";var i=["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"],r=function(t){return-1!==i.indexOf(t)},o=function(t,e,n){Object.keys(e).filter(r).forEach((function(i){var r=e[i];"function"===typeof r&&("onInit"===i?r(t,n):n.on(i.substring(2),(function(t){return r(t,n)})))}))},a=function(t,e){var n,i=t.$props.modelEvents?t.$props.modelEvents:null,r=Array.isArray(i)?i.join(" "):i;t.$watch("value",(function(t,i){e&&"string"===typeof t&&t!==n&&t!==i&&(e.setContent(t),n=t)})),e.on(r||"change keyup undo redo",(function(){n=e.getContent(),t.$emit("input",n)}))},s=function(t,e,n){var i=e.$props.value?e.$props.value:"",r=e.$props.initialValue?e.$props.initialValue:"";n.setContent(i||r),e.$listeners.input&&a(e,n),o(t,e.$listeners,n)},h=0,l=function(t){var e=Date.now(),n=Math.floor(1e9*Math.random());return h++,t+"_"+n+h+String(e)},u=function(t){return null!==t&&"textarea"===t.tagName.toLowerCase()},c=function(t){return"undefined"===typeof t||""===t?[]:Array.isArray(t)?t:t.split(" ")},d=function(t,e){return c(t).concat(c(e))},f=function(t,e,n,i){var r=e.createElement("script");r.type="application/javascript",r.id=t,r.addEventListener("load",i),r.src=n,e.head&&e.head.appendChild(r)},p=function(){return{listeners:[],scriptId:l("tiny-script"),scriptLoaded:!1}},g=function(t,e,n,i){t.scriptLoaded?i():(t.listeners.push(i),e.getElementById(t.scriptId)||f(t.scriptId,e,n,(function(){t.listeners.forEach((function(t){return t()})),t.scriptLoaded=!0})))},v=n("1f38"),y={apiKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],value:String,disabled:Boolean},m=function(){return m=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},m.apply(this,arguments)},x=p(),w=function(t,e,n){return t(n||"div",{attrs:{id:e}})},_=function(t,e){return t("textarea",{attrs:{id:e},style:{visibility:"hidden"}})},b=function(t){return function(){var e=m({},t.$props.init,{readonly:t.$props.disabled,selector:"#"+t.elementId,plugins:d(t.$props.init&&t.$props.init.plugins,t.$props.plugins),toolbar:t.$props.toolbar||t.$props.init&&t.$props.init.toolbar,inline:t.inlineEditor,setup:function(e){t.editor=e,e.on("init",(function(n){return s(n,t,e)})),t.$props.init&&"function"===typeof t.$props.init.setup&&t.$props.init.setup(e)}});u(t.element)&&(t.element.style.visibility=""),Object(v["a"])().init(e)}},E={props:y,created:function(){this.elementId=this.$props.id||l("tiny-vue"),this.inlineEditor=this.$props.init&&this.$props.init.inline||this.$props.inline},watch:{disabled:function(){this.editor.setMode(this.disabled?"readonly":"design")}},mounted:function(){if(this.element=this.$el,null!==Object(v["a"])())b(this)();else if(this.element&&this.element.ownerDocument){var t=this.element.ownerDocument,e=this.$props.cloudChannel?this.$props.cloudChannel:"5",n=this.$props.apiKey?this.$props.apiKey:"no-api-key";g(x,t,"https://cdn.tiny.cloud/1/"+n+"/tinymce/"+e+"/tinymce.min.js",b(this))}},beforeDestroy:function(){null!==Object(v["a"])()&&Object(v["a"])().remove(this.editor)},render:function(t){return this.inlineEditor?w(t,this.elementId,this.$props.tagName):_(t,this.elementId)}};e["a"]=E},9940:function(t,e,n){},a34a:function(t,e,n){t.exports=n("98b8")},b44f:function(t,e,n){(function(e,n){t.exports=n()})(0,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){var i=n(1);t.exports=i},function(t,e,n){function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var r=n(2),o=["X","Y","XY","POLYGON"],a=function(){function t(e){i(this,t),this.startPoint=null,this.brushing=!1,this.dragging=!1,this.brushShape=null,this.container=null,this.polygonPath=null,this.style={fill:"#C5D4EB",opacity:.3,lineWidth:1,stroke:"#82A6DD"},this.type="XY",this.dragable=!1,this.dragoffX=0,this.dragoffY=0,this.inPlot=!0,this.xField=null,this.yField=null,this.filter=!e.dragable,this.onBrushstart=null,this.onBrushmove=null,this.onBrushend=null,this.onDragstart=null,this.onDragmove=null,this.onDragend=null,this._init(e)}return t.prototype._init=function(t){r.mix(this,t),this.type=this.type.toUpperCase(),-1===o.indexOf(this.type)&&(this.type="XY");var e=this.canvas;if(e){var n=void 0;e.get("children").map((function(t){return"plotBack"===t.get("type")?(n=t.get("plotRange"),!1):t})),this.plot={start:n.bl,end:n.tr},this.bindCanvasEvent()}if(this.chart){var i=this.chart,a=i.get("coord");this.plot={start:a.start,end:a.end};var s=i._getScales("x"),h=i._getScales("y");this.xScale=this.xField?s[this.xField]:i.getXScale(),this.yScale=this.yField?h[this.yField]:i.getYScales()[0]}},t.prototype.clearEvents=function(){this.onMouseDownListener&&this.onMouseDownListener.remove(),this.onMouseMoveListener&&this.onMouseMoveListener.remove(),this.onMouseupListener&&this.onMouseupListener.remove()},t.prototype.bindCanvasEvent=function(){var t=this.canvas,e=t.get("canvasDOM");this.clearEvents(),this.onMouseDownListener=r.addEventListener(e,"mousedown",r.wrapBehavior(this,"_onCanvasMouseDown")),this.onMouseMoveListener=r.addEventListener(e,"mousemove",r.wrapBehavior(this,"_onCanvasMouseMove")),this.onMouseUpListener=r.addEventListener(e,"mouseup",r.wrapBehavior(this,"_onCanvasMouseUp"))},t.prototype._onCanvasMouseDown=function(t){var e=this,n=e.canvas,i=e.type,r=e.brushShape;if(i){var o={x:t.offsetX,y:t.offsetY},a=e.plot&&e.inPlot,s=n.get("canvasDOM"),h=n.get("pixelRatio");if(e.selection&&(e.selection=null),e.dragable&&r&&!r.get("destroyed")){if(r.isHit(o.x*h,o.y*h)){if(s.style.cursor="move",e.selection=r,e.dragging=!0,"X"===i)e.dragoffX=o.x-r.attr("x"),e.dragoffY=0;else if("Y"===i)e.dragoffX=0,e.dragoffY=o.y-r.attr("y");else if("XY"===i)e.dragoffX=o.x-r.attr("x"),e.dragoffY=o.y-r.attr("y");else if("POLYGON"===i){var l=r.getBBox();e.dragoffX=o.x-l.minX,e.dragoffY=o.y-l.minY}a&&e.selection.attr("clip",n.addShape("rect",{attrs:{x:this.plot.start.x,y:this.plot.end.y,width:this.plot.end.x-this.plot.start.x,height:this.plot.start.y-this.plot.end.y,fill:"#fff",fillOpacity:0}})),e.onDragstart&&e.onDragstart(t)}e.prePoint=o}if(!e.dragging){e.onBrushstart&&e.onBrushstart(o);var u=e.container;if(a){var c=e.plot,d=c.start,f=c.end;if(o.x<d.x||o.x>f.x||o.y<f.y||o.y>d.y)return}s.style.cursor="crosshair",e.startPoint=o,e.brushShape=null,e.brushing=!0,u?u.clear():(u=n.addGroup({zIndex:5}),u.initTransform()),e.container=u,"POLYGON"===i&&(e.polygonPath="M "+o.x+" "+o.y)}}},t.prototype._onCanvasMouseMove=function(t){var e=this,n=e.brushing,i=e.dragging,o=e.type,a=e.plot,s=e.startPoint,h=e.xScale,l=e.yScale,u=e.canvas;if(n||i){var c={x:t.offsetX,y:t.offsetY},d=u.get("canvasDOM");if(n){d.style.cursor="crosshair";var f=a.start,p=a.end,g=e.polygonPath,v=e.brushShape,y=e.container;e.plot&&e.inPlot&&(c=e._limitCoordScope(c));var m=void 0,x=void 0,w=void 0,_=void 0;"Y"===o?(m=f.x,x=c.y>=s.y?s.y:c.y,w=Math.abs(f.x-p.x),_=Math.abs(s.y-c.y)):"X"===o?(m=c.x>=s.x?s.x:c.x,x=p.y,w=Math.abs(s.x-c.x),_=Math.abs(p.y-f.y)):"XY"===o?(c.x>=s.x?(m=s.x,x=c.y>=s.y?s.y:c.y):(m=c.x,x=c.y>=s.y?s.y:c.y),w=Math.abs(s.x-c.x),_=Math.abs(s.y-c.y)):"POLYGON"===o&&(g+="L "+c.x+" "+c.y,e.polygonPath=g,v?!v.get("destroyed")&&v.attr(r.mix({},v.__attrs,{path:g})):v=y.addShape("path",{attrs:r.mix(e.style,{path:g})})),"POLYGON"!==o&&(v?!v.get("destroyed")&&v.attr(r.mix({},v.__attrs,{x:m,y:x,width:w,height:_})):v=y.addShape("rect",{attrs:r.mix(e.style,{x:m,y:x,width:w,height:_})})),e.brushShape=v}else if(i){d.style.cursor="move";var b=e.selection;if(b&&!b.get("destroyed"))if("POLYGON"===o){var E=e.prePoint;e.selection.translate(c.x-E.x,c.y-E.y)}else e.dragoffX&&b.attr("x",c.x-e.dragoffX),e.dragoffY&&b.attr("y",c.y-e.dragoffY)}e.prePoint=c,u.draw();var C=e._getSelected(),S=C.data,M=C.shapes,O=C.xValues,L=C.yValues,P={data:S,shapes:M,x:c.x,y:c.y};h&&(P[h.field]=O),l&&(P[l.field]=L),e.onDragmove&&e.onDragmove(P),e.onBrushmove&&e.onBrushmove(P)}},t.prototype._onCanvasMouseUp=function(t){var e=this,n=e.data,i=e.shapes,o=e.xValues,a=e.yValues,s=e.canvas,h=e.type,l=e.startPoint,u=e.chart,c=e.container,d=e.xScale,f=e.yScale,p=t.offsetX,g=t.offsetY,v=s.get("canvasDOM");if(v.style.cursor="default",Math.abs(l.x-p)<=1&&Math.abs(l.y-g)<=1)return e.brushing=!1,void(e.dragging=!1);var y={data:n,shapes:i,x:p,y:g};if(d&&(y[d.field]=o),f&&(y[f.field]=a),e.dragging)e.dragging=!1,e.onDragend&&e.onDragend(y);else if(e.brushing){e.brushing=!1;var m=e.brushShape,x=e.polygonPath;"POLYGON"===h&&(x+="z",m&&!m.get("destroyed")&&m.attr(r.mix({},m.__attrs,{path:x})),e.polygonPath=x,s.draw()),e.onBrushend?e.onBrushend(y):u&&e.filter&&(c.clear(),"X"===h?d&&u.filter(d.field,(function(t){return o.indexOf(t)>-1})):("Y"===h||d&&u.filter(d.field,(function(t){return o.indexOf(t)>-1})),f&&u.filter(f.field,(function(t){return a.indexOf(t)>-1}))),u.repaint())}},t.prototype.setType=function(t){t&&(this.type=t.toUpperCase())},t.prototype.destroy=function(){this.clearEvents()},t.prototype._limitCoordScope=function(t){var e=this.plot,n=e.start,i=e.end;return t.x<n.x&&(t.x=n.x),t.x>i.x&&(t.x=i.x),t.y<i.y&&(t.y=i.y),t.y>n.y&&(t.y=n.y),t},t.prototype._getSelected=function(){var t=this.chart,e=this.xScale,n=this.yScale,i=this.brushShape,r=this.canvas,o=r.get("pixelRatio"),a=[],s=[],h=[],l=[];if(t){var u=t.get("geoms");u.map((function(t){var r=t.getShapes();return r.map((function(t){var r=t.get("origin");return Array.isArray(r)||(r=[r]),r.map((function(r){if(i.isHit(r.x*o,r.y*o)){a.push(t);var u=r._origin;l.push(u),e&&s.push(u[e.field]),n&&h.push(u[n.field])}return r})),t})),t}))}return this.shapes=a,this.xValues=s,this.yValues=h,this.data=l,{data:l,xValues:s,yValues:h,shapes:a}},t}();t.exports=a},function(t,e){function n(t,e){for(var n in e)e.hasOwnProperty(n)&&"constructor"!==n&&void 0!==e[n]&&(t[n]=e[n])}var i={mix:function(t,e,i,r){return e&&n(t,e),i&&n(t,i),r&&n(t,r),t},addEventListener:function(t,e,n){return t.addEventListener?(t.addEventListener(e,n,!1),{remove:function(){t.removeEventListener(e,n,!1)}}):t.attachEvent?(t.attachEvent("on"+e,n),{remove:function(){t.detachEvent("on"+e,n)}}):void 0},wrapBehavior:function(t,e){if(t["_wrap_"+e])return t["_wrap_"+e];var n=function(n){t[e](n)};return t["_wrap_"+e]=n,n},getWrapBehavior:function(t,e){return t["_wrap_"+e]}};t.exports=i}])}))},dcb1:function(t,e,n){(function(e,n){t.exports=n()})(0,(function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){var i=n(1);window&&window.G2,t.exports=i},function(t,e,n){var i=n(2),r=window&&window.G2,o=r.Chart,a=r.Util,s=r.G,h=r.Global,l=s.Canvas,u=a.DomUtil,c=function(t){return"number"===typeof t},d=function(){var t=e.prototype;function e(t){this._initProps(),a.deepMix(this,t);var e=this.container;if(!e)throw new Error("Please specify the container for the Slider!");a.isString(e)?this.domContainer=document.getElementById(e):this.domContainer=e,this.handleStyle=a.mix({width:this.height,height:this.height},this.handleStyle),"auto"===this.width&&window.addEventListener("resize",a.wrapBehavior(this,"_initForceFitEvent"))}return t._initProps=function(){this.height=26,this.width="auto",this.padding=h.plotCfg.padding,this.container=null,this.xAxis=null,this.yAxis=null,this.fillerStyle={fill:"#BDCCED",fillOpacity:.3},this.backgroundStyle={stroke:"#CCD6EC",fill:"#CCD6EC",fillOpacity:.3,lineWidth:1},this.range=[0,100],this.layout="horizontal",this.textStyle={fill:"#545454"},this.handleStyle={img:"https://gw.alipayobjects.com/zos/rmsportal/QXtfhORGlDuRvLXFzpsQ.png",width:5},this.backgroundChart={type:["area"],color:"#CCD6EC"}},t._initForceFitEvent=function(){var t=setTimeout(a.wrapBehavior(this,"forceFit"),200);clearTimeout(this.resizeTimer),this.resizeTimer=t},t.forceFit=function(){if(this&&!this.destroyed){var t=u.getWidth(this.domContainer),e=this.height;if(t!==this.domWidth){var n=this.canvas;n.changeSize(t,e),this.bgChart&&this.bgChart.changeWidth(t),n.clear(),this._initWidth(),this._initSlider(),this._bindEvent(),n.draw()}}},t._initWidth=function(){var t;t="auto"===this.width?u.getWidth(this.domContainer):this.width,this.domWidth=t;var e=a.toAllPadding(this.padding);"horizontal"===this.layout?(this.plotWidth=t-e[1]-e[3],this.plotPadding=e[3],this.plotHeight=this.height):"vertical"===this.layout&&(this.plotWidth=this.width,this.plotHeight=this.height-e[0]-e[2],this.plotPadding=e[0])},t.render=function(){this._initWidth(),this._initCanvas(),this._initBackground(),this._initSlider(),this._bindEvent(),this.canvas.draw()},t.changeData=function(t){this.data=t,this.repaint()},t.destroy=function(){clearTimeout(this.resizeTimer);var t=this.rangeElement;t.off("sliderchange"),this.bgChart&&this.bgChart.destroy(),this.canvas.destroy();var e=this.domContainer;while(e.hasChildNodes())e.removeChild(e.firstChild);window.removeEventListener("resize",a.getWrapBehavior(this,"_initForceFitEvent")),this.destroyed=!0},t.clear=function(){this.canvas.clear(),this.bgChart&&this.bgChart.destroy(),this.bgChart=null,this.scale=null,this.canvas.draw()},t.repaint=function(){this.clear(),this.render()},t._initCanvas=function(){var t=this.domWidth,e=this.height,n=new l({width:t,height:e,containerDOM:this.domContainer,capture:!1}),i=n.get("el");i.style.position="absolute",i.style.top=0,i.style.left=0,i.style.zIndex=3,this.canvas=n},t._initBackground=function(){var t,e=this.data,n=this.xAxis,i=this.yAxis,r=a.deepMix((t={},t[""+n]={range:[0,1]},t),this.scales);if(!e)throw new Error("Please specify the data!");if(!n)throw new Error("Please specify the xAxis!");if(!i)throw new Error("Please specify the yAxis!");var s=this.backgroundChart,h=s.type,l=s.color;a.isArray(h)||(h=[h]);var u=a.toAllPadding(this.padding),c=new o({container:this.container,width:this.domWidth,height:this.height,padding:[0,u[1],0,u[3]],animate:!1});c.source(e),c.scale(r),c.axis(!1),c.tooltip(!1),c.legend(!1),a.each(h,(function(t){c[t]().position(n+"*"+i).color(l).opacity(1)})),c.render(),this.bgChart=c,this.scale="horizontal"===this.layout?c.getXScale():c.getYScales()[0],"vertical"===this.layout&&c.destroy()},t._initRange=function(){var t=this.startRadio,e=this.endRadio,n=this.start,i=this.end,r=this.scale,o=0,a=1;c(t)?o=t:n&&(o=r.scale(r.translate(n))),c(e)?a=e:i&&(a=r.scale(r.translate(i)));var s=this.minSpan,h=this.maxSpan,l=0;if("time"===r.type||"timeCat"===r.type){var u=r.values,d=u[0],f=u[u.length-1];l=f-d}else r.isLinear&&(l=r.max-r.min);l&&s&&(this.minRange=s/l*100),l&&h&&(this.maxRange=h/l*100);var p=[100*o,100*a];return this.range=p,p},t._getHandleValue=function(t){var e,n=this.range,i=n[0]/100,r=n[1]/100,o=this.scale;return e="min"===t?this.start?this.start:o.invert(i):this.end?this.end:o.invert(r),e},t._initSlider=function(){var t=this.canvas,e=this._initRange(),n=this.scale,r=t.addGroup(i,{middleAttr:this.fillerStyle,range:e,minRange:this.minRange,maxRange:this.maxRange,layout:this.layout,width:this.plotWidth,height:this.plotHeight,backgroundStyle:this.backgroundStyle,textStyle:this.textStyle,handleStyle:this.handleStyle,minText:n.getText(this._getHandleValue("min")),maxText:n.getText(this._getHandleValue("max"))});"horizontal"===this.layout?r.translate(this.plotPadding,0):"vertical"===this.layout&&r.translate(0,this.plotPadding),this.rangeElement=r},t._bindEvent=function(){var t=this,e=t.rangeElement;e.on("sliderchange",(function(e){var n=e.range,i=n[0]/100,r=n[1]/100;t._updateElement(i,r)}))},t._updateElement=function(t,e){var n=this.scale,i=this.rangeElement,r=i.get("minTextElement"),o=i.get("maxTextElement"),a=n.invert(t),s=n.invert(e),h=n.getText(a),l=n.getText(s);r.attr("text",h),o.attr("text",l),this.start=a,this.end=s,this.onChange&&this.onChange({startText:h,endText:l,startValue:a,endValue:s,startRadio:t,endRadio:e})},e}();t.exports=d},function(t,e){function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i=window&&window.G2,r=i.Util,o=i.G,a=o.Group,s=r.DomUtil,h=5,l=function(t){function e(){return t.apply(this,arguments)||this}n(e,t);var i=e.prototype;return i.getDefaultCfg=function(){return{range:null,middleAttr:null,backgroundElement:null,minHandleElement:null,maxHandleElement:null,middleHandleElement:null,currentTarget:null,layout:"vertical",width:null,height:null,pageX:null,pageY:null}},i._initHandle=function(t){var e,n,i,o=this.addGroup(),a=this.get("layout"),s=this.get("handleStyle"),l=s.img,u=s.width,c=s.height;if("horizontal"===a){var d=s.width;i="ew-resize",n=o.addShape("Image",{attrs:{x:-d/2,y:0,width:d,height:c,img:l,cursor:i}}),e=o.addShape("Text",{attrs:r.mix({x:"min"===t?-(d/2+h):d/2+h,y:c/2,textAlign:"min"===t?"end":"start",textBaseline:"middle",text:"min"===t?this.get("minText"):this.get("maxText"),cursor:i},this.get("textStyle"))})}else i="ns-resize",n=o.addShape("Image",{attrs:{x:0,y:-c/2,width:u,height:c,img:l,cursor:i}}),e=o.addShape("Text",{attrs:r.mix({x:u/2,y:"min"===t?c/2+h:-(c/2+h),textAlign:"center",textBaseline:"middle",text:"min"===t?this.get("minText"):this.get("maxText"),cursor:i},this.get("textStyle"))});return this.set(t+"TextElement",e),this.set(t+"IconElement",n),o},i._initSliderBackground=function(){var t=this.addGroup();return t.initTransform(),t.translate(0,0),t.addShape("Rect",{attrs:r.mix({x:0,y:0,width:this.get("width"),height:this.get("height")},this.get("backgroundStyle"))}),t},i._beforeRenderUI=function(){var t=this._initSliderBackground(),e=this._initHandle("min"),n=this._initHandle("max"),i=this.addShape("rect",{attrs:this.get("middleAttr")});this.set("middleHandleElement",i),this.set("minHandleElement",e),this.set("maxHandleElement",n),this.set("backgroundElement",t),t.set("zIndex",0),i.set("zIndex",1),e.set("zIndex",2),n.set("zIndex",2),i.attr("cursor","move"),this.sort()},i._renderUI=function(){"horizontal"===this.get("layout")?this._renderHorizontal():this._renderVertical()},i._transform=function(t){var e=this.get("range"),n=e[0]/100,i=e[1]/100,r=this.get("width"),o=this.get("height"),a=this.get("minHandleElement"),s=this.get("maxHandleElement"),h=this.get("middleHandleElement");a.resetMatrix?(a.resetMatrix(),s.resetMatrix()):(a.initTransform(),s.initTransform()),"horizontal"===t?(h.attr({x:r*n,y:0,width:(i-n)*r,height:o}),a.translate(n*r,0),s.translate(i*r,0)):(h.attr({x:0,y:o*(1-i),width:r,height:(i-n)*o}),a.translate(0,(1-n)*o),s.translate(0,(1-i)*o))},i._renderHorizontal=function(){this._transform("horizontal")},i._renderVertical=function(){this._transform("vertical")},i._bindUI=function(){this.on("mousedown",r.wrapBehavior(this,"_onMouseDown"))},i._isElement=function(t,e){var n=this.get(e);if(t===n)return!0;if(n.isGroup){var i=n.get("children");return i.indexOf(t)>-1}return!1},i._getRange=function(t,e){var n=t+e;return n=n>100?100:n,n=n<0?0:n,n},i._limitRange=function(t,e,n){n[0]=this._getRange(t,n[0]),n[1]=n[0]+e,n[1]>100&&(n[1]=100,n[0]=n[1]-e)},i._updateStatus=function(t,e){var n="x"===t?this.get("width"):this.get("height");t=r.upperFirst(t);var i,o=this.get("range"),a=this.get("page"+t),s=this.get("currentTarget"),h=this.get("rangeStash"),l=this.get("layout"),u="vertical"===l?-1:1,c=e["page"+t],d=c-a,f=d/n*100*u,p=this.get("minRange"),g=this.get("maxRange");o[1]<=o[0]?(this._isElement(s,"minHandleElement")||this._isElement(s,"maxHandleElement"))&&(o[0]=this._getRange(f,o[0]),o[1]=this._getRange(f,o[0])):(this._isElement(s,"minHandleElement")&&(o[0]=this._getRange(f,o[0]),p&&o[1]-o[0]<=p&&this._limitRange(f,p,o),g&&o[1]-o[0]>=g&&this._limitRange(f,g,o)),this._isElement(s,"maxHandleElement")&&(o[1]=this._getRange(f,o[1]),p&&o[1]-o[0]<=p&&this._limitRange(f,p,o),g&&o[1]-o[0]>=g&&this._limitRange(f,g,o))),this._isElement(s,"middleHandleElement")&&(i=h[1]-h[0],this._limitRange(f,i,o)),this.emit("sliderchange",{range:o}),this.set("page"+t,c),this._renderUI(),this.get("canvas").draw()},i._onMouseDown=function(t){var e=t.currentTarget,n=t.event,i=this.get("range");n.stopPropagation(),n.preventDefault(),this.set("pageX",n.pageX),this.set("pageY",n.pageY),this.set("currentTarget",e),this.set("rangeStash",[i[0],i[1]]),this._bindCanvasEvents()},i._bindCanvasEvents=function(){var t=this.get("canvas").get("containerDOM");this.onMouseMoveListener=s.addEventListener(t,"mousemove",r.wrapBehavior(this,"_onCanvasMouseMove")),this.onMouseUpListener=s.addEventListener(t,"mouseup",r.wrapBehavior(this,"_onCanvasMouseUp")),this.onMouseLeaveListener=s.addEventListener(t,"mouseleave",r.wrapBehavior(this,"_onCanvasMouseUp"))},i._onCanvasMouseMove=function(t){var e=this.get("layout");"horizontal"===e?this._updateStatus("x",t):this._updateStatus("y",t)},i._onCanvasMouseUp=function(){this._removeDocumentEvents()},i._removeDocumentEvents=function(){this.onMouseMoveListener.remove(),this.onMouseUpListener.remove(),this.onMouseLeaveListener.remove()},e}(a);t.exports=l}])}))}}]);