(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~437fae42"],{"00ea":function(t,e,a){"use strict";var r=a("81eb"),s=a.n(r);s.a},1323:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"agent-market"},[a("div",{staticClass:"sticky-filters"},[a("div",{staticClass:"market-filters"},[a("div",{staticClass:"filter-row"},[a("div",{staticClass:"search-box"},[a("div",{staticClass:"search-wrapper"},[a("a-icon",{staticClass:"search-icon",attrs:{type:"search"}}),a("a-input",{staticClass:"search-input",attrs:{placeholder:"搜索智能体名称、描述或标签...",size:"large"},on:{pressEnter:t.handleSearch,input:t.handleSearch},model:{value:t.searchQuery,callback:function(e){t.searchQuery=e},expression:"searchQuery"}}),t.searchQuery?a("a-icon",{staticClass:"clear-icon",attrs:{type:"close-circle"},on:{click:t.clearSearch}}):t._e()],1)]),a("div",{staticClass:"filter-controls"},[a("div",{staticClass:"filter-group"},[a("div",{staticClass:"filter-buttons"},[a("button",{staticClass:"filter-btn",class:{active:""===t.authorTypeFilter},on:{click:function(e){return t.setAuthorTypeFilter("")}}},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n                全部\n              ")],1),a("button",{staticClass:"filter-btn official",class:{active:"1"===t.authorTypeFilter},on:{click:function(e){return t.setAuthorTypeFilter("1")}}},[a("a-icon",{attrs:{type:"crown"}}),t._v("\n                官方\n              ")],1),a("button",{staticClass:"filter-btn creator",class:{active:"2"===t.authorTypeFilter},on:{click:function(e){return t.setAuthorTypeFilter("2")}}},[a("a-icon",{attrs:{type:"user"}}),t._v("\n                创作者\n              ")],1)])]),a("div",{staticClass:"filter-group"},[a("div",{staticClass:"filter-buttons"},[a("button",{staticClass:"filter-btn",class:{active:""===t.purchaseStatusFilter},on:{click:function(e){return t.setPurchaseStatusFilter("")}}},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n                全部\n              ")],1),a("button",{staticClass:"filter-btn purchased",class:{active:"purchased"===t.purchaseStatusFilter},on:{click:function(e){return t.setPurchaseStatusFilter("purchased")}}},[a("a-icon",{attrs:{type:"check-circle"}}),t._v("\n                "+t._s(t.getPurchaseFilterText())+"\n              ")],1),a("button",{staticClass:"filter-btn unpurchased",class:{active:"unpurchased"===t.purchaseStatusFilter},on:{click:function(e){return t.setPurchaseStatusFilter("unpurchased")}}},[a("a-icon",{attrs:{type:"shopping"}}),t._v("\n                未购\n              ")],1)])]),a("div",{staticClass:"filter-group"},[a("div",{staticClass:"filter-buttons"},[a("button",{staticClass:"filter-btn reset-btn",attrs:{disabled:!t.hasActiveFilters},on:{click:t.resetAllFilters}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n                重置\n              ")],1)])])])])])]),t.loading?a("div",{staticClass:"loading-state"},[a("a-spin",{attrs:{size:"large",tip:"正在加载智能体数据..."}},[a("div",{staticClass:"loading-placeholder"})])],1):a("div",{staticClass:"agent-list"},[a("div",{staticClass:"list-header"},[a("h3",{staticClass:"list-title"},[t._v("\n        智能体列表\n        "),a("span",{staticClass:"list-count"},[t._v("("+t._s(t.totalCount)+"个)")])])]),t.agentList.length>0?a("div",{staticClass:"agent-grid"},t._l(t.agentList,(function(e){return a("AgentCard",{key:e.id,attrs:{agent:e},on:{"view-detail":t.handleViewDetail}})})),1):a("div",{staticClass:"empty-state"},[a("a-empty",{attrs:{description:"暂无智能体数据"}},[a("a-button",{attrs:{type:"primary"},on:{click:t.handleRefresh}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n          刷新数据\n        ")],1)],1)],1),t.agentList.length>0?a("div",{staticClass:"load-more-wrapper"},[t.loadingMore?a("div",{staticClass:"loading-more"},[a("a-spin",{attrs:{size:"small"}}),a("span",[t._v("正在加载更多...")])],1):t.hasMore?a("div",{ref:"loadMoreTrigger",staticClass:"load-more-trigger"}):a("div",{staticClass:"no-more-data"},[a("a-icon",{attrs:{type:"check-circle"}}),a("span",[t._v("已加载全部数据 (共"+t._s(t.totalCount)+"个)")])],1)]):t._e()]),a("AgentDetailModal",{attrs:{visible:t.detailModalVisible,agentId:t.selectedAgentId,isPurchased:t.isSelectedAgentPurchased},on:{close:t.handleCloseDetailModal,"update:visible":t.handleUpdateVisible,purchase:t.handlePurchaseFromModal,"purchase-success":t.handlePurchaseSuccess}})],1)},s=[],i=a("a34a"),n=a.n(i),o=a("fd93"),c=a("a763"),l=a("77ea"),u=a("9fb0");function d(t){return v(t)||p(t)||f(t)||h()}function h(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){if(t){if("string"===typeof t)return g(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?g(t,e):void 0}}function p(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function v(t){if(Array.isArray(t))return g(t)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function m(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function w(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?m(Object(a),!0).forEach((function(e){k(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function k(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function b(t,e,a,r,s,i,n){try{var o=t[i](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(r,s)}function C(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){b(i,r,s,n,o,"next",t)}function o(t){b(i,r,s,n,o,"throw",t)}n(void 0)}))}}var y={name:"AgentMarket",components:{AgentCard:o["default"],AgentDetailModal:c["default"]},data:function(){return{loading:!1,loadingMore:!1,searchQuery:"",authorTypeFilter:"",purchaseStatusFilter:"",agentList:[],userRole:"user",currentPage:1,pageSize:16,totalCount:0,hasMore:!0,detailModalVisible:!1,selectedAgentId:"",selectedAgent:null,purchasedAgents:[],debounceSearch:null}},computed:{isSelectedAgentPurchased:function(){return this.selectedAgent&&this.selectedAgent.isPurchased||!1},hasActiveFilters:function(){return""!==this.searchQuery||""!==this.authorTypeFilter||""!==this.purchaseStatusFilter}},mounted:function(){var t=C(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadUserRole();case 2:return t.next=4,this.loadAgentList();case 4:this.setupIntersectionObserver(),this.initDebounceSearch();case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){this.observer&&this.observer.disconnect()},methods:{loadUserRole:function(){var t=C(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.$ls.get(u["a"]),e){t.next=5;break}return this.userRole="user",t.abrupt("return");case 5:return t.prev=5,t.next=8,Object(l["w"])();case 8:a=t.sent,a&&a.success?this.userRole=a.result.role_code||"user":this.userRole="user",t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](5),this.userRole="user";case 16:case"end":return t.stop()}}),t,this,[[5,12]])})));function e(){return t.apply(this,arguments)}return e}(),loadAgentList:function(){var t=C(n.a.mark((function t(){var e,a,r,s,i,o,c,l=this,u=arguments;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=!(u.length>0&&void 0!==u[0])||u[0],e?(this.loading=!0,this.currentPage=1,this.agentList=[],this.hasMore=!0):this.loadingMore=!0,t.prev=2,a={pageNo:this.currentPage,pageSize:this.pageSize,agentName:this.searchQuery||void 0,authorType:this.authorTypeFilter||void 0,auditStatus:"2",purchaseStatus:this.purchaseStatusFilter||void 0},t.next=6,this.$http.get("/api/agent/market/list",{params:a});case 6:r=t.sent,s=r.data||r,s&&s.success?(i=s.result.records||[],o=i.map((function(t){return w({},t)})),e?this.agentList=o:(c=this.agentList).push.apply(c,d(o)),this.totalCount=s.result.total||0,this.hasMore=this.agentList.length<this.totalCount,i.length>0&&this.currentPage++):this.$message.error(s&&s.message||"获取智能体列表失败"),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](2),this.$message.error("加载智能体列表失败，请稍后重试");case 15:return t.prev=15,this.loading=!1,this.loadingMore=!1,e&&this.$nextTick((function(){l.setupIntersectionObserver()})),t.finish(15);case 20:case"end":return t.stop()}}),t,this,[[2,11,15,20]])})));function e(){return t.apply(this,arguments)}return e}(),loadMockData:function(){var t=C(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,new Promise((function(t){return setTimeout(t,500)}));case 2:this.agentList=[],this.totalCount=0,this.hasMore=!1;case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getPurchaseFilterText:function(){return"SVIP"===this.userRole?"已购/SVIP免费":(this.userRole,"已购买")},loadMore:function(){var t=C(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.hasMore&&!this.loadingMore){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,this.loadAgentList(!1);case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),calculatePrice:function(t){var e=!1,a=!1,r=1,s=!1;return t.isPurchased?{showSvipPromo:!1,showDiscountPrice:!1,discountRate:1,isFree:!1,isPurchased:!0}:(null===this.userRole||"user"===this.userRole||"admin"===this.userRole?(e=!0,a=!1):"VIP"===this.userRole?(e=!1,a=!0,r=.7):"SVIP"===this.userRole&&(e=!1,!t||1!==t.authorType&&"1"!==t.authorType?!t||2!==t.authorType&&"2"!==t.authorType||(s=!1,a=!0,r=.5):(s=!0,r=0,a=!1)),{showSvipPromo:e,showDiscountPrice:a,discountRate:r,isFree:s,isPurchased:!1})},handleSearch:function(){this.scrollToTop(),this.loadAgentList(!0)},handleFilterChange:function(){this.scrollToTop(),this.loadAgentList(!0)},setAuthorTypeFilter:function(t){this.authorTypeFilter=t,this.handleFilterChange()},setPurchaseStatusFilter:function(t){this.purchaseStatusFilter=t,this.handleFilterChange()},resetAllFilters:function(){this.searchQuery="",this.authorTypeFilter="",this.purchaseStatusFilter="",this.handleFilterChange()},scrollToTop:function(){window.scrollTo({top:0,behavior:"smooth"})},setupIntersectionObserver:function(){var t=this;this.observer&&(this.observer.disconnect(),this.observer=null),this.$nextTick((function(){var e=t.$refs.loadMoreTrigger;e&&(t.observer=new IntersectionObserver((function(e){e.forEach((function(e){e.isIntersecting&&t.hasMore&&!t.loadingMore&&t.loadMore()}))}),{rootMargin:"100px"}),t.observer.observe(e))}))},handleViewDetail:function(){var t=C(n.a.mark((function t(e){var a,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/api/agent/market/purchase/check/".concat(e.id));case 3:a=t.sent,a&&a.success&&(r=a.result.isPurchased,"SVIP"===this.userRole&&"1"===e.authorType&&e.isFree,e.isPurchased=r),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),e.isPurchased=!1;case 11:this.selectedAgent=e,this.selectedAgentId=e.id,this.detailModalVisible=!0;case 14:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(e){return t.apply(this,arguments)}return e}(),handleCloseDetailModal:function(){this.detailModalVisible=!1,this.selectedAgent=null,this.selectedAgentId=""},handleUpdateVisible:function(t){this.detailModalVisible=t,t||(this.selectedAgent=null,this.selectedAgentId="")},handlePurchaseFromModal:function(t){},handlePurchaseSuccess:function(t){this.onPurchaseSuccess(t),this.$message.success("购买成功！您现在可以下载该智能体的所有工作流了")},loadPurchasedAgents:function(){var t=C(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})));function e(){return t.apply(this,arguments)}return e}(),isAgentPurchased:function(t){var e=this.agentList.find((function(e){return e.id===t}));return!!e&&e.isPurchased},onPurchaseSuccess:function(){var t=C(n.a.mark((function t(e){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loadAgentList(!0),t.next=3,this.loadUserRole();case 3:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),initDebounceSearch:function(){var t=this;this.debounceSearch=this.debounce((function(){t.handleSearch()}),300)},debounce:function(t,e){var a;return function(){for(var r=arguments.length,s=new Array(r),i=0;i<r;i++)s[i]=arguments[i];var n=function(){clearTimeout(a),t.apply(void 0,s)};clearTimeout(a),a=setTimeout(n,e)}},handleRefresh:function(){this.loadAgentList()},getCreatorCount:function(){return this.agentList.filter((function(t){return"2"===t.authorType})).length},clearSearch:function(){this.searchQuery="",this.handleSearch()},clearAllFilters:function(){this.searchQuery="",this.authorTypeFilter="",this.purchaseStatusFilter="",this.handleFilterChange()}}},x=y,_=(a("53af"),a("2877")),D=Object(_["a"])(x,r,s,!1,null,"25a53aaf",null);e["default"]=D.exports},"1d5d":function(t,e,a){"use strict";var r=a("45aa"),s=a.n(r);s.a},"2b95":function(t,e,a){},"2bd1":function(t,e,a){},"2c90":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"revenue-stats-cards"},[a("div",{staticClass:"stats-cards"},[a("div",{staticClass:"stats-row"},[a("div",{staticClass:"stat-card revenue-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"dollar"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatMoney(t.data.totalRevenue)))]),a("div",{staticClass:"stat-label"},[t._v("总收益")]),a("div",{staticClass:"card-actions"},[a("a-button",{staticClass:"withdraw-btn",attrs:{type:"primary",size:"small",disabled:!t.canWithdraw},on:{click:t.handleWithdraw}},[a("a-icon",{attrs:{type:"wallet"}}),t._v("\n                立即提现\n              ")],1)],1)])])]),a("div",{staticClass:"stat-card sales-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"shopping-cart"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.totalSales||0))]),a("div",{staticClass:"stat-label"},[t._v("总销售次数")])])])]),a("div",{staticClass:"stat-card agent-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"robot"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.agentCount||0))]),a("div",{staticClass:"stat-label"},[t._v("智能体总数")])])])]),a("div",{staticClass:"stat-card approved-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"check-circle"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.approvedAgentCount||0))]),a("div",{staticClass:"stat-label"},[t._v("已通过")])])])]),a("div",{staticClass:"refresh-action"},[a("a-button",{attrs:{loading:t.loading,size:"large"},on:{click:t.handleRefresh}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n          刷新数据\n        ")],1)],1)])])])},s=[],i={name:"RevenueStatsCards",props:{loading:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},computed:{canWithdraw:function(){return this.data.totalRevenue>=50}},methods:{formatMoney:function(t){return t?Number(t).toFixed(2):"0.00"},handleRefresh:function(){this.$emit("refresh")},handleWithdraw:function(){this.canWithdraw?this.$emit("withdraw",{availableAmount:this.data.totalRevenue,revenueType:"agent"}):this.$message.warning("最低提现金额为50元")}}},n=i,o=(a("4cee"),a("2877")),c=Object(o["a"])(n,r,s,!1,null,"1cf34f4b",null);e["default"]=c.exports},3261:function(t,e,a){"use strict";var r=a("2bd1"),s=a.n(r);s.a},"349e":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"creator-center"},[a("div",{staticClass:"creator-header"},[a("div",{staticClass:"header-content"},[a("div",{staticClass:"header-left"},[a("h1",{staticClass:"page-title"},[a("a-icon",{attrs:{type:"user"}}),t._v("\n          创作者中心\n        ")],1),a("p",{staticClass:"page-subtitle"},[t._v("管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）")])]),a("div",{staticClass:"header-right"},[a("a-button",{attrs:{type:"primary",size:"large",loading:t.creating},on:{click:t.handleCreateAgent}},[a("a-icon",{attrs:{type:"plus"}}),t._v("\n          新增智能体\n        ")],1)],1)])]),a("div",{staticClass:"stats-cards-section"},[a("div",{staticClass:"section-header"},[a("h2",{staticClass:"section-title"},[t._v("收益统计")]),a("div",{staticClass:"section-actions"},[a("a-button",{staticClass:"records-btn",attrs:{type:"default"},on:{click:t.openWithdrawRecordsModal}},[a("a-icon",{attrs:{type:"history"}}),t._v("\n          查看提现记录\n        ")],1)],1)]),a("RevenueStatsCards",{attrs:{loading:t.statsLoading,data:t.revenueStats},on:{refresh:t.loadRevenueStats,withdraw:t.handleWithdraw}})],1),a("div",{staticClass:"agents-section"},[t._m(0),a("AgentManagement",{attrs:{loading:t.agentsLoading,agents:t.agentList,pagination:t.pagination},on:{refresh:t.loadAgentList,edit:t.handleEditAgent,delete:t.handleDeleteAgent,"page-change":t.handlePageChange,search:t.handleAgentSearch,"filter-change":t.handleAgentFilter,"sort-change":t.handleSortChange}})],1),a("div",{staticClass:"ranking-section"},[a("RevenueRanking",{attrs:{loading:t.statsLoading,data:t.revenueStats}})],1),a("CreatorAgentForm",{ref:"agentForm",attrs:{visible:t.formVisible,loading:t.formLoading,agent:t.currentAgent,mode:t.formMode},on:{close:t.handleCloseForm,submit:t.handleSubmitForm,complete:t.handleAgentComplete,"delete-workflow":t.handleDeleteWorkflowFromForm}}),a("WithdrawModal",{attrs:{visible:t.showWithdrawModal,"available-amount":t.withdrawAvailableAmount,"revenue-type":t.withdrawRevenueType,loading:t.withdrawLoading},on:{submit:t.handleWithdrawSubmit,cancel:t.handleWithdrawCancel}}),a("WithdrawRecordsModal",{attrs:{visible:t.showWithdrawRecordsModal,"revenue-type":"agent"},on:{cancel:t.handleWithdrawRecordsCancel,refresh:t.loadRevenueStats}})],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"section-header"},[a("h2",{staticClass:"section-title"},[t._v("我的智能体")])])}],i=a("a34a"),n=a.n(i),o=a("2c90"),c=a("bb00"),l=a("3add"),u=a("5a2a"),d=a("24ec"),h=a("ebfe"),f=a("bc9a"),p=a("9fb0");function v(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function g(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?v(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):v(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function w(t,e,a,r,s,i,n){try{var o=t[i](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(r,s)}function k(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){w(i,r,s,n,o,"next",t)}function o(t){w(i,r,s,n,o,"throw",t)}n(void 0)}))}}var b={name:"CreatorCenter",components:{RevenueStatsCards:o["default"],RevenueRanking:c["default"],AgentManagement:l["default"],CreatorAgentForm:u["default"],WithdrawModal:d["default"],WithdrawRecordsModal:h["default"]},data:function(){return{creating:!1,statsLoading:!1,agentsLoading:!1,formLoading:!1,revenueStats:{},agentList:[],pagination:{current:1,pageSize:12,total:0},searchQuery:"",auditStatusFilter:"",sortField:"totalRevenue",sortOrder:"desc",formVisible:!1,formMode:"create",currentAgent:null,showWithdrawModal:!1,showWithdrawRecordsModal:!1,withdrawLoading:!1,withdrawAvailableAmount:0,withdrawRevenueType:"agent"}},created:function(){var t=k(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.$ls.get(p["a"]),e){t.next=4;break}return t.abrupt("return");case 4:return t.next=6,this.initData();case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{initData:function(){var t=k(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Promise.all([this.loadRevenueStats(),this.loadAgentList()]);case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),loadRevenueStats:function(){var t=k(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.$ls.get(p["a"]),e){t.next=4;break}return t.abrupt("return");case 4:return this.statsLoading=!0,t.prev=5,t.next=8,Object(f["d"])();case 8:a=t.sent,a.success?this.revenueStats=a.result:this.$message.error(a.message||"获取收益统计失败"),t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](5),this.$message.error("获取收益统计失败");case 16:return t.prev=16,this.statsLoading=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,this,[[5,12,16,19]])})));function e(){return t.apply(this,arguments)}return e}(),loadAgentList:function(){var t=k(n.a.mark((function t(){var e,a,r,s,i=arguments;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=i.length>0&&void 0!==i[0]&&i[0],a=this.$ls.get(p["a"]),a){t.next=5;break}return t.abrupt("return");case 5:return e&&(this.pagination.current=1),this.agentsLoading=!0,t.prev=7,r={pageNo:this.pagination.current,pageSize:this.pagination.pageSize,agentName:this.searchQuery||void 0,auditStatus:this.auditStatusFilter||void 0,sortField:this.sortField,sortOrder:this.sortOrder},t.next=11,Object(f["c"])(r);case 11:s=t.sent,s.success?(this.agentList=s.result.records||[],this.pagination.total=s.result.total||0):this.$message.error(s.message||"获取智能体列表失败"),t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](7),this.$message.error("获取智能体列表失败");case 19:return t.prev=19,this.agentsLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[7,15,19,22]])})));function e(){return t.apply(this,arguments)}return e}(),handleCreateAgent:function(){this.formMode="create",this.currentAgent=null,this.formVisible=!0},handleEditAgent:function(t){this.formMode="edit",this.currentAgent=g({},t),this.formVisible=!0},handleDeleteAgent:function(){var t=k(n.a.mark((function t(e){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=this,this.$confirm({title:"确认删除",content:'确定要删除智能体"'.concat(e.agentName,'"吗？此操作不可恢复。'),okText:"确定",cancelText:"取消",onOk:function(){var t=k(n.a.mark((function t(){var r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(f["b"])(e.id);case 3:if(r=t.sent,!r.success){t.next=12;break}return a.$message.success("删除成功"),t.next=8,a.loadAgentList();case 8:return t.next=10,a.loadRevenueStats();case 10:t.next=13;break;case 12:a.$message.error(r.message||"删除失败");case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](0),a.$message.error("删除失败");case 19:case"end":return t.stop()}}),t,null,[[0,15]])})));function r(){return t.apply(this,arguments)}return r}()});case 2:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleAgentSearch:function(t){this.searchQuery=t,this.loadAgentList(!0)},handleAgentFilter:function(t){this.auditStatusFilter=t.auditStatus,this.loadAgentList(!0)},handleSortChange:function(t){this.sortField=t.sortField,this.sortOrder=t.sortOrder,this.loadAgentList(!0)},handlePageChange:function(t,e){this.pagination.current=t,this.pagination.pageSize=e,this.loadAgentList()},handleCloseForm:function(){this.formVisible=!1,this.currentAgent=null},handleSubmitForm:function(){var t=k(n.a.mark((function t(e){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.formLoading=!0,t.prev=1,"create"!==this.formMode){t.next=8;break}return t.next=5,Object(f["a"])(e);case 5:a=t.sent,t.next=11;break;case 8:return t.next=10,Object(f["e"])(this.currentAgent.id,e);case 10:a=t.sent;case 11:if(!a.success){t.next=21;break}return this.$refs&&this.$refs.agentForm&&this.$refs.agentForm.confirmDeleteOriginalFiles&&this.$refs.agentForm.confirmDeleteOriginalFiles(),this.$message.success("create"===this.formMode?"创建成功":"更新成功"),this.handleCloseForm(),t.next=17,this.loadAgentList();case 17:return t.next=19,this.loadRevenueStats();case 19:t.next=23;break;case 21:this.$message.error(a.message||"操作失败");case 23:t.next=29;break;case 25:t.prev=25,t.t0=t["catch"](1),this.$message.error("操作失败: "+(t.t0.message||"未知错误"));case 29:return t.prev=29,this.formLoading=!1,t.finish(29);case 32:case"end":return t.stop()}}),t,this,[[1,25,29,32]])})));function e(e){return t.apply(this,arguments)}return e}(),refreshAllData:function(){var t=k(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.$ls.get(p["a"]),e){t.next=4;break}return t.abrupt("return");case 4:return t.prev=4,t.next=7,Promise.all([this.loadRevenueStats(),this.loadAgentList()]);case 7:t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](4);case 12:case"end":return t.stop()}}),t,this,[[4,9]])})));function e(){return t.apply(this,arguments)}return e}(),handleAgentComplete:function(t){this.handleCloseForm(),this.loadAgentList(),this.loadRevenueStats()},handleDeleteWorkflowFromForm:function(){var t=k(n.a.mark((function t(e){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{this.$message.success("工作流删除成功"),this.$refs.agentForm&&this.$refs.agentForm.loadWorkflowList&&this.$refs.agentForm.loadWorkflowList(e.agentId)}catch(a){this.$message.error("删除工作流失败")}case 2:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleWithdraw:function(t){this.withdrawAvailableAmount=t.availableAmount,this.withdrawRevenueType=t.revenueType,this.showWithdrawModal=!0},openWithdrawRecordsModal:function(){this.showWithdrawRecordsModal=!0},handleWithdrawSubmit:function(){var t=k(n.a.mark((function t(e){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.withdrawLoading=!0,t.prev=1,t.next=4,this.$http.post("/api/usercenter/applyWithdrawal",e);case 4:if(a=t.sent,!a.success){t.next=12;break}return this.$notification.success({message:"提现申请成功",description:"您的提现申请已提交，预计1-3个工作日到账"}),this.showWithdrawModal=!1,t.next=10,this.loadRevenueStats();case 10:t.next=13;break;case 12:this.$notification.error({message:"提现申请失败",description:a.message||"申请失败，请重试"});case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](1),this.$notification.error({message:"提现申请失败",description:t.t0.message||"申请失败，请重试"});case 19:return t.prev=19,this.withdrawLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[1,15,19,22]])})));function e(e){return t.apply(this,arguments)}return e}(),handleWithdrawCancel:function(){this.showWithdrawModal=!1},handleWithdrawRecordsCancel:function(){this.showWithdrawRecordsModal=!1}}},C=b,y=(a("cc1b"),a("2877")),x=Object(y["a"])(C,r,s,!1,null,"55cee220",null);e["default"]=x.exports},"3a07":function(t,e,a){},"3add":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"agent-management"},[a("div",{staticClass:"market-filters"},[a("div",{staticClass:"filter-row"},[a("div",{staticClass:"search-box"},[a("div",{staticClass:"search-wrapper"},[a("a-icon",{staticClass:"search-icon",attrs:{type:"search"}}),a("a-input",{staticClass:"search-input",attrs:{placeholder:"搜索智能体名称、描述...",size:"large"},on:{pressEnter:t.handleSearch,input:t.handleSearch},model:{value:t.searchQuery,callback:function(e){t.searchQuery=e},expression:"searchQuery"}}),t.searchQuery?a("a-icon",{staticClass:"clear-icon",attrs:{type:"close-circle"},on:{click:t.clearSearch}}):t._e()],1)]),a("div",{staticClass:"filter-controls"},[a("div",{staticClass:"filter-group"},[a("div",{staticClass:"filter-buttons"},[a("button",{staticClass:"filter-btn",class:{active:""===t.auditStatusFilter},on:{click:function(e){return t.setAuditStatusFilter("")}}},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n              全部\n            ")],1),a("button",{staticClass:"filter-btn pending",class:{active:"1"===t.auditStatusFilter},on:{click:function(e){return t.setAuditStatusFilter("1")}}},[a("a-icon",{attrs:{type:"clock-circle"}}),t._v("\n              待审核\n            ")],1),a("button",{staticClass:"filter-btn approved",class:{active:"2"===t.auditStatusFilter},on:{click:function(e){return t.setAuditStatusFilter("2")}}},[a("a-icon",{attrs:{type:"check-circle"}}),t._v("\n              已通过\n            ")],1),a("button",{staticClass:"filter-btn rejected",class:{active:"3"===t.auditStatusFilter},on:{click:function(e){return t.setAuditStatusFilter("3")}}},[a("a-icon",{attrs:{type:"close-circle"}}),t._v("\n              已拒绝\n            ")],1)])]),a("div",{staticClass:"filter-group"},[a("div",{staticClass:"sort-controls"},[a("span",{staticClass:"sort-label"},[t._v("排序：")]),a("a-select",{staticStyle:{width:"120px","margin-right":"8px"},attrs:{size:"default"},on:{change:t.handleSortChange},model:{value:t.sortField,callback:function(e){t.sortField=e},expression:"sortField"}},[a("a-select-option",{attrs:{value:"totalRevenue"}},[t._v("总收益")]),a("a-select-option",{attrs:{value:"salesCount"}},[t._v("销售次数")]),a("a-select-option",{attrs:{value:"createTime"}},[t._v("创建时间")])],1),a("a-button",{staticClass:"sort-order-btn",attrs:{icon:"desc"===t.sortOrder?"sort-descending":"sort-ascending",title:"desc"===t.sortOrder?"降序":"升序"},on:{click:t.toggleSortOrder}},[t._v("\n              "+t._s("desc"===t.sortOrder?"降序":"升序")+"\n            ")])],1)])])])]),!t.loading&&t.agents.length>0?a("div",{staticClass:"agent-grid"},t._l(t.agents,(function(e){return a("div",{key:e.id,staticClass:"agent-card",class:{pending:"1"===e.auditStatus,approved:"2"===e.auditStatus,rejected:"3"===e.auditStatus}},[a("div",{staticClass:"card-header"},[a("div",{staticClass:"agent-avatar"},[e.agentAvatar?a("img",{attrs:{src:e.agentAvatar,alt:e.agentName},on:{error:t.handleImageError}}):a("div",{staticClass:"avatar-placeholder"},[a("a-icon",{attrs:{type:"robot"}})],1)]),a("div",{staticClass:"agent-info"},[a("h3",{staticClass:"agent-name",attrs:{title:e.agentName}},[t._v(t._s(e.agentName))]),a("div",{staticClass:"agent-meta"},[a("a-tag",{staticClass:"status-tag",attrs:{color:t.getStatusColor(e.auditStatus)}},[t._v("\n              "+t._s(e.auditStatusText)+"\n            ")])],1)]),a("div",{staticClass:"card-actions"},[a("a-dropdown",{attrs:{trigger:["click"]}},[a("a-button",{attrs:{type:"text",size:"small"}},[a("a-icon",{attrs:{type:"more"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",{key:"edit",attrs:{disabled:!e.editable},on:{click:function(a){return t.handleEdit(e)}}},[a("a-icon",{attrs:{type:"edit"}}),t._v("\n                编辑\n              ")],1),a("a-menu-item",{key:"delete",attrs:{disabled:!e.deletable},on:{click:function(a){return t.handleDelete(e)}}},[a("a-icon",{attrs:{type:"delete"}}),t._v("\n                删除\n              ")],1)],1)],1)],1)]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"agent-description",attrs:{title:e.agentDescription}},[a("p",[t._v(t._s(e.agentDescription||"暂无描述"))])]),a("div",{staticClass:"agent-stats"},[a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-label"},[t._v("价格")]),a("span",{staticClass:"stat-value price"},[t._v("¥"+t._s(e.price||0))])]),a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-label"},[t._v("工作流")]),a("span",{staticClass:"stat-value"},[t._v(t._s(e.workflowCount||0))])]),"2"===e.auditStatus?a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-label"},[t._v("销售")]),a("span",{staticClass:"stat-value"},[t._v(t._s(e.salesCount||0))])]):t._e()]),"2"===e.auditStatus?a("div",{staticClass:"stat-item revenue"},[a("div",{staticClass:"stat-content"},[a("span",{staticClass:"stat-label"},[t._v("总收益")]),a("span",{staticClass:"stat-value revenue"},[t._v("¥"+t._s(t.formatMoney(e.totalRevenue)))])])]):t._e(),"3"===e.auditStatus&&e.auditRemark?a("div",{staticClass:"audit-remark"},[a("div",{staticClass:"remark-label"},[t._v("拒绝原因：")]),a("div",{staticClass:"remark-content"},[t._v(t._s(e.auditRemark))])]):t._e()]),a("div",{staticClass:"card-footer"},[a("div",{staticClass:"create-time"},[t._v("\n          创建时间："+t._s(t.formatDate(e.createTime))+"\n        ")]),e.experienceLink?a("div",{staticClass:"experience-link"},[a("a",{staticClass:"link-button",attrs:{href:e.experienceLink,target:"_blank"}},[a("a-icon",{attrs:{type:"link"}}),t._v("\n            体验链接\n          ")],1)]):t._e()])])})),0):t._e(),t.loading?a("div",{staticClass:"loading-container"},[a("a-spin",{attrs:{size:"large"}},[a("div",{staticClass:"loading-text"},[t._v("加载中...")])])],1):t._e(),t.loading||0!==t.agents.length?t._e():a("div",{staticClass:"empty-container"},[a("a-empty",{attrs:{description:"暂无智能体",image:t.emptyImage}},[a("a-button",{attrs:{type:"primary"},on:{click:t.handleCreate}},[a("a-icon",{attrs:{type:"plus"}}),t._v("\n        创建第一个智能体\n      ")],1)],1)],1),!t.loading&&t.agents.length>0?a("div",{staticClass:"pagination-container"},[a("div",{staticClass:"pagination-wrapper"},[a("a-pagination",{staticClass:"custom-pagination",attrs:{current:t.pagination.current,total:t.pagination.total,"page-size":t.pagination.pageSize,"page-size-options":["12","24","36","48"],"show-size-changer":!0,"show-quick-jumper":!0,"show-total":function(t,e){return"第 "+e[0]+"-"+e[1]+" 条，共 "+t+" 条"}},on:{change:t.handlePageChange,showSizeChange:t.handlePageSizeChange},scopedSlots:t._u([{key:"buildOptionText",fn:function(e){return[a("span",[t._v(t._s(e.value)+"条/页")])]}}],null,!1,198454938)})],1)]):t._e()])},s=[],i=a("fc25"),n={name:"AgentManagement",props:{loading:{type:Boolean,default:!1},agents:{type:Array,default:function(){return[]}},pagination:{type:Object,default:function(){return{current:1,pageSize:12,total:0}}}},data:function(){return{emptyImage:i["a"].PRESENTED_IMAGE_SIMPLE,searchQuery:"",auditStatusFilter:"",sortField:"totalRevenue",sortOrder:"desc"}},methods:{getStatusColor:function(t){var e={1:"orange",2:"green",3:"red"};return e[t]||"default"},getStatusIcon:function(t){var e={1:"clock-circle",2:"check-circle",3:"close-circle"};return e[t]||"question-circle"},formatMoney:function(t){return t?Number(t).toFixed(2):"0.00"},formatDate:function(t){if(!t)return"";var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},handleSearch:function(){this.$emit("search",this.searchQuery)},clearSearch:function(){this.searchQuery="",this.handleSearch()},handleSortChange:function(){this.emitSortChange()},toggleSortOrder:function(){this.sortOrder="desc"===this.sortOrder?"asc":"desc",this.emitSortChange()},emitSortChange:function(){this.$emit("sort-change",{sortField:this.sortField,sortOrder:this.sortOrder})},setAuditStatusFilter:function(t){this.auditStatusFilter=t,this.$emit("filter-change",{auditStatus:this.auditStatusFilter})},handleImageError:function(t){t.target.style.display="none",t.target.nextElementSibling.style.display="flex"},handleEdit:function(t){this.$emit("edit",t)},handleDelete:function(t){this.$emit("delete",t)},showWorkflowPublishedModal:function(){var t=this;this.$confirm({title:"",icon:function(){return t.$createElement("span")},content:function(){return t.$createElement("div",{class:"custom-modal-content"},[t.$createElement("div",{style:{height:"4px",background:"linear-gradient(90deg, #ffa726 0%, #ff9800 100%)",borderRadius:"2px 2px 0 0",marginBottom:"32px"}}),t.$createElement("div",{style:{textAlign:"center",marginBottom:"24px"}},[t.$createElement("div",{style:{width:"64px",height:"64px",background:"linear-gradient(135deg, #ffa726 0%, #ff9800 100%)",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto",boxShadow:"0 8px 32px rgba(255, 167, 38, 0.3)"}},[t.$createElement("a-icon",{props:{type:"lock",style:{fontSize:"28px",color:"#ffffff"}}})])]),t.$createElement("div",{style:{textAlign:"center",fontSize:"24px",fontWeight:"700",color:"#1a202c",marginBottom:"16px",letterSpacing:"-0.5px"}},"工作流已锁定"),t.$createElement("div",{style:{textAlign:"center",fontSize:"16px",color:"#4a5568",lineHeight:"1.6",marginBottom:"8px"}},"您的工作流已成功发布并锁定"),t.$createElement("div",{style:{textAlign:"center",fontSize:"14px",color:"#718096",marginBottom:"32px"}},"如需修改请联系管理员解锁")])},centered:!0,width:480,maskClosable:!0,cancelButtonProps:{style:{display:"none"}},okText:"我知道了",okButtonProps:{style:{background:"linear-gradient(135deg, #ffa726 0%, #ff9800 100%)",border:"none",borderRadius:"12px",height:"48px",fontSize:"16px",fontWeight:"600",padding:"0 40px",boxShadow:"0 4px 20px rgba(255, 167, 38, 0.4)",transition:"all 0.3s ease",color:"#ffffff"}},onOk:function(){},class:"workflow-published-modal"})},handleCreate:function(){this.$emit("create")},handlePageChange:function(t,e){this.$emit("page-change",t,e),this.scrollToCreatorCenter()},handlePageSizeChange:function(t,e){this.$emit("page-change",t,e),this.scrollToCreatorCenter()},scrollToCreatorCenter:function(){this.$nextTick((function(){var t=document.querySelector(".creator-center");t?t.scrollIntoView({behavior:"smooth",block:"start"}):window.scrollTo({top:0,behavior:"smooth"})}))},handleRefresh:function(){this.$emit("refresh")}}},o=n,c=(a("de2d"),a("2877")),l=Object(c["a"])(o,r,s,!1,null,"e1bae21e",null);e["default"]=l.exports},"45aa":function(t,e,a){},"490a":function(t,e,a){},"4cee":function(t,e,a){"use strict";var r=a("adc7"),s=a.n(r);s.a},"53af":function(t,e,a){"use strict";var r=a("3a07"),s=a.n(r);s.a},"5a2a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-modal",{staticClass:"creator-agent-modal",attrs:{title:t.modalTitle,visible:t.visible,width:900,"confirm-loading":t.stepLoading,"mask-closable":!1,centered:!0,"body-style":{padding:"0"},footer:null},on:{cancel:t.handleCancel}},[a("div",{staticClass:"step-header"},[a("a-steps",{staticClass:"custom-steps",attrs:{current:t.currentStep}},[a("a-step",{attrs:{title:"智能体信息",description:"填写基本信息"}}),a("a-step",{attrs:{title:"工作流配置",description:"配置工作流"}}),a("a-step",{attrs:{title:"完成创建",description:"创建完成"}})],1)],1),a("div",{staticClass:"step-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0===t.currentStep,expression:"currentStep === 0"}],staticClass:"step-panel"},[a("div",{staticClass:"panel-header"},[a("h3",{staticClass:"panel-title"},[a("a-icon",{attrs:{type:"robot"}}),t._v("\n          智能体基本信息\n        ")],1),a("p",{staticClass:"panel-desc"},[t._v("请填写智能体的基本信息，这些信息将展示给用户")])]),a("a-form-model",{ref:"form",staticClass:"modern-form",attrs:{model:t.formData,rules:t.rules}},[a("div",{staticClass:"form-card"},[a("a-form-model-item",{attrs:{prop:"agentName"}},[a("div",{staticClass:"field-label"},[a("a-icon",{attrs:{type:"robot"}}),t._v("\n              智能体名称\n              "),a("span",{staticClass:"required-star"},[t._v("*")])],1),a("a-input",{staticClass:"modern-input",attrs:{placeholder:"请输入智能体名称",size:"large","max-length":100,"show-count":""},model:{value:t.formData.agentName,callback:function(e){t.$set(t.formData,"agentName",e)},expression:"formData.agentName"}}),a("div",{staticClass:"field-tips"},[t._v("\n              为您的智能体起一个吸引人的名称\n            ")])],1)],1),a("div",{staticClass:"form-card"},[a("a-form-model-item",{attrs:{prop:"agentDescription"}},[a("div",{staticClass:"field-label"},[a("a-icon",{attrs:{type:"file-text"}}),t._v("\n              智能体描述\n              "),a("span",{staticClass:"required-star"},[t._v("*")])],1),a("a-textarea",{staticClass:"modern-textarea",attrs:{placeholder:"请详细描述您的智能体功能和特点",rows:4,"max-length":1e3,"show-count":""},model:{value:t.formData.agentDescription,callback:function(e){t.$set(t.formData,"agentDescription",e)},expression:"formData.agentDescription"}}),a("div",{staticClass:"field-tips"},[t._v("\n              详细描述有助于用户了解您的智能体功能\n            ")])],1)],1),a("div",{staticClass:"form-card"},[a("a-form-model-item",{attrs:{prop:"agentAvatar"}},[a("div",{staticClass:"field-label"},[a("a-icon",{attrs:{type:"picture"}}),t._v("\n              智能体头像\n              "),a("span",{staticClass:"required-star"},[t._v("*")])],1),a("j-image-upload-deferred",{ref:"avatarUpload",attrs:{isMultiple:!1,bizPath:"agent-avatar",text:"上传头像"},model:{value:t.formData.agentAvatar,callback:function(e){t.$set(t.formData,"agentAvatar",e)},expression:"formData.agentAvatar"}}),a("div",{staticClass:"field-tips"},[t._v("\n              支持 JPG、PNG 格式，文件大小不超过 5MB\n            ")])],1)],1),a("div",{staticClass:"form-card"},[a("a-form-model-item",{attrs:{prop:"experienceLink"}},[a("div",{staticClass:"field-label"},[a("a-icon",{attrs:{type:"link"}}),t._v("\n              体验链接\n              "),a("span",{staticClass:"optional"},[t._v("（可选）")])],1),a("a-input",{staticClass:"modern-input",attrs:{placeholder:"请输入体验链接",size:"large","max-length":500},model:{value:t.formData.experienceLink,callback:function(e){t.$set(t.formData,"experienceLink",e)},expression:"formData.experienceLink"}}),a("div",{staticClass:"field-tips"},[t._v("\n              用户可以通过此链接体验您的智能体功能\n            ")])],1)],1),a("div",{staticClass:"form-card"},[a("a-form-model-item",{attrs:{prop:"price"}},[a("div",{staticClass:"field-label"},[a("a-icon",{attrs:{type:"dollar"}}),t._v("\n              价格设置\n              "),a("span",{staticClass:"required-star"},[t._v("*")])],1),a("a-input-number",{staticClass:"modern-input-number",attrs:{placeholder:"请输入价格",min:0,max:99999,precision:2,step:.01,size:"large"},model:{value:t.formData.price,callback:function(e){t.$set(t.formData,"price",e)},expression:"formData.price"}},[a("template",{slot:"addonBefore"},[t._v("¥")])],2),a("div",{staticClass:"field-tips"},[t._v("\n              设置智能体的使用价格，用户购买后可以使用您的智能体\n            ")])],1)],1)]),a("div",{staticClass:"step-footer"},[a("a-button",{attrs:{disabled:t.stepLoading},on:{click:t.handleCancel}},[t._v("\n          取消\n        ")]),a("a-button",{staticClass:"next-btn",attrs:{type:"primary",loading:t.stepLoading},on:{click:t.handleNext}},[t._v("\n          下一步：配置工作流\n          "),a("a-icon",{attrs:{type:"arrow-right"}})],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1===t.currentStep,expression:"currentStep === 1"}],staticClass:"step-panel"},[a("div",{staticClass:"panel-header"},[a("h3",{staticClass:"panel-title"},[a("a-icon",{attrs:{type:"apartment"}}),t._v("\n          工作流配置\n        ")],1),a("p",{staticClass:"panel-desc"},[t._v("为您的智能体配置工作流，提升智能体的功能和效率")])]),t.createdAgent?a("div",{staticClass:"created-agent-info"},[a("div",{staticClass:"agent-summary"},[a("div",{staticClass:"agent-avatar"},[t.createdAgent.agentAvatar?a("img",{attrs:{src:t.getFullAvatarUrl(t.createdAgent.agentAvatar),alt:t.createdAgent.agentName}}):a("a-icon",{attrs:{type:"robot"}})],1),a("div",{staticClass:"agent-details"},[a("h4",[t._v(t._s(t.createdAgent.agentName))]),a("p",[t._v(t._s(t.createdAgent.agentDescription))]),a("span",{staticClass:"agent-price"},[t._v("¥"+t._s(t.createdAgent.price))])])])]):t._e(),a("div",{staticClass:"workflow-section"},[a("div",{staticClass:"workflow-form"},[a("div",{staticClass:"form-header"},[a("h3",[a("a-icon",{attrs:{type:"apartment"}}),t._v("\n              新增工作流\n            ")],1),a("p",[t._v("为您的智能体添加工作流，让它更加智能和实用")])]),a("a-form-model",{ref:"workflowFormRef",attrs:{model:t.workflowFormData,rules:t.workflowRules,layout:"vertical"}},[a("a-form-model-item",{attrs:{label:"工作流名称",prop:"workflowName"}},[a("a-input",{attrs:{placeholder:"请输入工作流名称，如：文档生成助手",maxLength:30,"show-count":""},model:{value:t.workflowFormData.workflowName,callback:function(e){t.$set(t.workflowFormData,"workflowName",e)},expression:"workflowFormData.workflowName"}})],1),a("a-form-model-item",{attrs:{label:"工作流描述",prop:"workflowDescription"}},[a("a-textarea",{attrs:{placeholder:"请描述工作流的功能和用途，帮助用户了解其作用",rows:3,maxLength:200,"show-count":""},model:{value:t.workflowFormData.workflowDescription,callback:function(e){t.$set(t.workflowFormData,"workflowDescription",e)},expression:"workflowFormData.workflowDescription"}})],1),a("a-form-model-item",{attrs:{label:"输入参数说明",prop:"inputParamsDesc"}},[a("a-textarea",{attrs:{placeholder:"格式：参数:值 或 参数:\"值\" 或 参数:'值'（例如：name:\"张三\",age:25,city:'北京'）",rows:4,maxLength:1e4},on:{blur:t.handleInputParamsBlur},model:{value:t.workflowFormData.inputParamsDesc,callback:function(e){t.$set(t.workflowFormData,"inputParamsDesc",e)},expression:"workflowFormData.inputParamsDesc"}}),a("div",{staticStyle:{color:"#666","font-size":"12px","margin-top":"4px"}},[t._v("\n                * 必填项，例如：name:\"张三\",age:25,city:'北京' 支持中英文冒号逗号\n              ")])],1),a("a-form-model-item",{attrs:{label:"工作流文件",prop:"workflowPackage"}},[a("div",{staticClass:"workflow-file-upload"},[t.workflowFileInfo?a("div",{staticClass:"uploaded-file-info"},[a("div",{staticClass:"file-item",class:{"saved-file":t.workflowFileInfo.isSaved}},[a("a-icon",{staticClass:"file-icon",attrs:{type:"file-zip"}}),a("span",{staticClass:"file-name"},[t._v(t._s(t.workflowFileInfo.originalName||t.workflowFileInfo.name))]),t.workflowFileInfo.isSaved?a("a-tag",{staticStyle:{"margin-left":"8px"},attrs:{color:"green",size:"small"}},[t._v("\n                      已保存\n                    ")]):t._e(),a("a-button",{staticClass:"remove-btn",attrs:{type:"link",size:"small",title:t.workflowFileInfo.isSaved?"重新选择文件":"删除文件"},on:{click:t.handleRemoveWorkflowFile}},[a("a-icon",{attrs:{type:t.workflowFileInfo.isSaved?"edit":"delete"}}),t._v("\n                      "+t._s(t.workflowFileInfo.isSaved?"重选":"删除")+"\n                    ")],1)],1)]):a("div",{staticClass:"upload-area"},[a("a-upload",{ref:"workflowUpload",attrs:{name:"file",multiple:!1,"before-upload":t.beforeWorkflowUpload,"show-upload-list":!1,accept:".zip",customRequest:function(){}},on:{change:t.handleWorkflowFileSelect}},[a("a-button",{attrs:{loading:t.workflowUploading}},[a("a-icon",{attrs:{type:"upload"}}),t._v("\n                      选择工作流压缩包\n                    ")],1)],1)],1)]),a("div",{staticClass:"upload-tip"},[a("a-icon",{staticStyle:{color:"#faad14"},attrs:{type:"exclamation-circle"}}),a("strong",[t._v("温馨提示：")]),t._v("只支持 .zip 格式，文件大小不超过 5MB\n              ")],1)])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1===t.currentStep,expression:"currentStep === 1"}],staticClass:"add-next-workflow"},[a("a-button",{staticClass:"add-next-btn",attrs:{type:"dashed",block:""},on:{click:t.addNextWorkflow}},[a("a-icon",{attrs:{type:"plus"}}),t._v("\n              新增下一个工作流\n            ")],1)],1)],1),t.tempWorkflowList.length>0?a("div",{staticClass:"temp-workflows"},[a("a-divider",[t._v("工作流列表 ("+t._s(t.tempWorkflowList.length)+")")]),a("div",{staticClass:"workflow-list"},t._l(t.tempWorkflowList,(function(e,r){return a("div",{key:e.id,staticClass:"workflow-item",class:{editing:t.currentWorkflowIndex===r,"has-error":t.workflowValidationErrors[e.id]&&!t.workflowValidationErrors[e.id].isValid}},[a("div",{staticClass:"workflow-info"},[a("h4",{staticClass:"workflow-name"},[t._v("\n                  "+t._s(e.workflowName)+"\n                  "),"saved"===e.status?a("a-tag",{attrs:{color:"green",size:"small"}},[t._v("已保存")]):"draft"===e.status?a("a-tag",{attrs:{color:"orange",size:"small"}},[t._v("新增")]):t._e(),t.currentWorkflowIndex===r?a("a-tag",{attrs:{color:"blue",size:"small"}},[t._v("编辑中")]):t._e(),t.workflowValidationErrors[e.id]&&!t.workflowValidationErrors[e.id].isValid?a("a-tag",{attrs:{color:"red",size:"small"}},[a("a-icon",{attrs:{type:"exclamation-circle"}}),t._v("\n                    有错误\n                  ")],1):t._e()],1),a("p",{staticClass:"workflow-desc"},[t._v(t._s(e.workflowDescription||"暂无描述"))]),t.workflowValidationErrors[e.id]&&!t.workflowValidationErrors[e.id].isValid?a("div",{staticClass:"workflow-errors"},[a("a-alert",{attrs:{type:"error",size:"small","show-icon":"",message:"请补充："+t.workflowValidationErrors[e.id].errors.join("、")}})],1):t._e()]),a("div",{staticClass:"workflow-actions"},[a("a-button",{attrs:{size:"small",disabled:t.currentWorkflowIndex===r},on:{click:function(e){return t.loadWorkflowFromTemp(r)}}},[a("a-icon",{attrs:{type:"edit"}}),t._v("\n                  编辑\n                ")],1),a("a-button",{attrs:{size:"small",type:"danger"},on:{click:function(e){return t.deleteWorkflowFromTemp(r)}}},[a("a-icon",{attrs:{type:"delete"}}),t._v("\n                  删除\n                ")],1)],1)])})),0)],1):t._e()]),a("div",{staticClass:"step-footer"},[a("a-button",{attrs:{disabled:t.stepLoading},on:{click:t.handlePrev}},[a("a-icon",{attrs:{type:"arrow-left"}}),t._v("\n          上一步\n        ")],1),a("a-button",{staticClass:"complete-btn",attrs:{type:"primary",loading:t.stepLoading},on:{click:t.handleComplete}},[t._v("\n          完成创建\n          "),a("a-icon",{attrs:{type:"check"}})],1)],1)]),2===t.currentStep?a("div",{staticClass:"step-content"},[a("div",{staticClass:"step-header"},[a("h3",[a("a-icon",{staticStyle:{color:"#52c41a","margin-right":"8px"},attrs:{type:"check-circle"}}),t._v("\n          创建完成\n        ")],1),a("p",[t._v("智能体创建流程已完成")])]),a("div",{staticClass:"success-content"},[a("div",{staticClass:"success-icon"},[a("a-icon",{staticStyle:{"font-size":"64px",color:"#52c41a"},attrs:{type:"check-circle"}})],1),a("div",{staticClass:"success-message"},[a("h2",[t._v("恭喜您，已成功提交智能体，请耐心等待审核！")]),a("p",[t._v("您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。")]),a("p",[t._v("审核结果将通过站内消息通知您，请注意查收。")])])]),a("div",{staticClass:"step-footer"},[a("a-button",{attrs:{type:"primary",size:"large"},on:{click:t.handleCloseModal}},[t._v("\n          关闭\n        ")])],1)]):t._e()])])},s=[],i=a("a34a"),n=a.n(i),o=a("6745"),c=a("bc9a"),l=a("6f1b");function u(t,e){var a;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(a=d(t))||e&&t&&"number"===typeof t.length){a&&(t=a);var r=0,s=function(){};return{s:s,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,n=!0,o=!1;return{s:function(){a=t[Symbol.iterator]()},n:function(){var t=a.next();return n=t.done,t},e:function(t){o=!0,i=t},f:function(){try{n||null==a.return||a.return()}finally{if(o)throw i}}}}function d(t,e){if(t){if("string"===typeof t)return h(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}function f(t){return f="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e,a,r,s,i,n){try{var o=t[i](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(r,s)}function v(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){p(i,r,s,n,o,"next",t)}function o(t){p(i,r,s,n,o,"throw",t)}n(void 0)}))}}function g(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function m(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?g(Object(a),!0).forEach((function(e){w(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):g(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function w(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var k={name:"CreatorAgentForm",components:{JImageUploadDeferred:o["default"]},props:{visible:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},agent:{type:Object,default:null},mode:{type:String,default:"create"}},data:function(){return{currentStep:0,stepLoading:!1,createdAgent:null,formData:{agentId:"",agentName:"",agentDescription:"",agentAvatar:"",experienceLink:"",price:0},workflowFormData:{workflowName:"",workflowDescription:"",inputParamsDesc:"",workflowPackage:""},tempWorkflowList:[],currentWorkflowIndex:-1,workflowValidationErrors:{},workflowFileList:[],workflowFileInfo:null,workflowUploading:!1,workflowList:[],workflowLoading:!1,rules:{agentName:[{required:!0,message:"请输入智能体名称",trigger:"blur"},{min:2,max:100,message:"智能体名称长度在 2 到 100 个字符",trigger:"blur"}],agentDescription:[{required:!0,message:"请输入智能体描述",trigger:"blur"},{min:2,max:1e3,message:"智能体描述长度在 2 到 1000 个字符",trigger:"blur"}],agentAvatar:[{required:!0,message:"请上传智能体头像",trigger:"change"}],experienceLink:[{type:"url",message:"请输入正确的URL格式",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"},{type:"number",min:0,max:99999,message:"价格范围在 0 到 99999 元",trigger:"blur"}]},workflowRules:{workflowName:[{required:!0,message:"工作流名称为必填项",trigger:"blur"},{min:2,max:30,message:"工作流名称长度在 2 到 30 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/,message:"工作流名称只能包含中英文、数字、空格、横线和下划线",trigger:"blur"}],workflowDescription:[{required:!0,message:"工作流描述为必填项",trigger:"blur"},{min:2,max:200,message:"工作流描述长度在 2 到 200 个字符",trigger:"blur"}],inputParamsDesc:[{required:!0,message:"请输入参数说明",trigger:"blur"},{min:2,message:"参数说明至少需要2个字符",trigger:"blur"},{max:1e4,message:"参数说明长度不能超过10000个字符",trigger:"blur"},{pattern:/^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/,message:"请按照格式填写：参数:值 或 参数:\"值\" 或 参数:'值'",trigger:"blur"}],workflowPackage:[]}}},computed:{modalTitle:function(){return"create"===this.mode?"新增智能体":"编辑智能体"},uploadAction:function(){return"".concat(window._CONFIG["domianURL"],"/sys/common/upload")},uploadHeaders:function(){return{"X-Access-Token":this.$ls.get("Access-Token")}},uploadData:function(){return{isup:1,biz:""}}},watch:{visible:function(t){var e=this;t?(this.initForm(),this.currentStep=0,this.scrollToTop(),"edit"===this.mode&&this.agent&&(this.createdAgent=m({},this.agent),this.$nextTick((function(){e.loadWorkflowList(e.agent.id)})))):(this.resetForm(),this.clearAllWorkflowData())},agent:{handler:function(t){var e=this;t&&this.visible&&(this.initForm(),"edit"===this.mode&&(this.createdAgent=m({},t),this.$nextTick((function(){e.loadWorkflowList(t.id)}))))},deep:!0}},methods:{initForm:function(){var t=this;"edit"===this.mode&&this.agent?this.formData={agentName:this.agent.agentName||"",agentDescription:this.agent.agentDescription||"",agentAvatar:this.agent.agentAvatar||"",experienceLink:this.agent.experienceLink||"",price:this.agent.price||0}:(this.formData={agentName:"",agentDescription:"",agentAvatar:"",experienceLink:"",price:0},this.clearAllWorkflowData()),this.$nextTick((function(){t.$refs.form&&t.$refs.form.clearValidate()}))},resetForm:function(){this.currentStep=0,this.stepLoading=!1,this.createdAgent=null,this.formData={agentId:"",agentName:"",agentDescription:"",agentAvatar:"",experienceLink:"",price:0},this.workflowList=[],this.$refs.form&&this.$refs.form.clearValidate()},handleNext:function(){var t=v(n.a.mark((function t(){var e,a,r=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=this.formData.agentAvatar,a=this.$refs.avatarUpload&&this.$refs.avatarUpload.hasPendingFiles(),!this.formData.agentAvatar&&a&&(this.formData.agentAvatar="pending_upload"),this.$refs.form.validate(function(){var t=v(n.a.mark((function t(a){var s,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r.formData.agentAvatar=e,!a){t.next=33;break}return r.stepLoading=!0,t.prev=3,t.next=6,r.uploadPendingImages();case 6:if(s={agentName:r.formData.agentName.trim(),agentDescription:r.formData.agentDescription.trim(),agentAvatar:r.processAvatarValue(r.formData.agentAvatar),experienceLink:r.formData.experienceLink.trim(),price:r.formData.price},"create"!==r.mode){t.next=14;break}return t.next=10,r.createAgentStep(s);case 10:i=t.sent,r.$message.success("智能体创建成功，请配置工作流"),t.next=18;break;case 14:return t.next=16,r.updateAgentStep(s);case 16:i=t.sent,r.$message.success("智能体更新成功，请配置工作流");case 18:r.createdAgent=i,r.currentStep=1,r.loadWorkflowList(i.id),r.scrollToTop(),t.next=28;break;case 24:t.prev=24,t.t0=t["catch"](3),r.$message.error("智能体创建失败: "+(t.t0.message||"未知错误"));case 28:return t.prev=28,r.stepLoading=!1,t.finish(28);case 31:t.next=34;break;case 33:r.$message.error("请检查表单信息");case 34:case"end":return t.stop()}}),t,null,[[3,24,28,31]])})));return function(e){return t.apply(this,arguments)}}());case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handlePrev:function(){this.currentStep=0,this.scrollToTop()},handleComplete:function(){var t=v(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.stepLoading=!0,this.autoSaveCurrentWorkflow(),0!==this.tempWorkflowList.length){t.next=15;break}return this.$message.success("智能体创建完成！"),this.$emit("complete",this.createdAgent),this.handleCancel(),t.abrupt("return");case 15:if(e=this.validateAllWorkflows(),e.isValid){t.next=22;break}return this.handleValidationErrors(e),this.stepLoading=!1,t.abrupt("return");case 22:return t.next=25,this.batchSaveWorkflows();case 25:a=t.sent,this.$message.success("智能体和 ".concat(a.length," 个工作流创建完成！")),this.currentStep=2,this.scrollToTop(),t.next=36;break;case 32:t.prev=32,t.t0=t["catch"](0),this.$message.error("工作流保存失败: "+(t.t0.message||"未知错误"));case 36:return t.prev=36,this.stepLoading=!1,t.finish(36);case 39:case"end":return t.stop()}}),t,this,[[0,32,36,39]])})));function e(){return t.apply(this,arguments)}return e}(),handleCloseModal:function(){this.$emit("complete",this.createdAgent),this.handleCancel()},batchSaveWorkflows:function(){var t=v(n.a.mark((function t(){var e,a,r,s,i,o,c,u,d,h,f,p,v,g;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=[],this.tempWorkflowList.length,a=0;case 3:if(!(a<this.tempWorkflowList.length)){t.next=69;break}if(r=this.tempWorkflowList[a],t.prev=6,"saved"!==r.status){t.next=44;break}if(s=r.originalWorkflow,i=s&&(s.workflowName!==r.workflowName||s.workflowDescription!==r.workflowDescription||s.inputParamsDesc!==r.inputParamsDesc),o=null!==r.workflowFile,c=i||o,!c){t.next=40;break}if(u=s.workflowPackage,!o){t.next=22;break}return t.next=20,this.uploadWorkflowFile(r.workflowFile,r.workflowName);case 20:u=t.sent;case 22:return d={agentId:this.createdAgent.id,workflowName:r.workflowName,workflowDescription:r.workflowDescription,inputParamsDesc:r.inputParamsDesc,workflowPackage:u},t.next=27,Object(l["c"])(s.id,d);case 27:if(h=t.sent,!h.success){t.next=37;break}f=h.result||m(m({},s),d),e.push(f),this.tempWorkflowList[a]=m(m({},r),{},{workflowName:f.workflowName||r.workflowName,workflowDescription:f.workflowDescription||r.workflowDescription,inputParamsDesc:f.inputParamsDesc||r.inputParamsDesc,originalWorkflow:f}),t.next=38;break;case 37:throw new Error(h.message||"工作流更新API调用失败");case 38:t.next=42;break;case 40:e.push(s);case 42:t.next=59;break;case 44:return t.next=47,this.uploadWorkflowFile(r.workflowFile,r.workflowName);case 47:return p=t.sent,v={agentId:this.createdAgent.id,workflowName:r.workflowName,workflowDescription:r.workflowDescription,inputParamsDesc:r.inputParamsDesc,workflowPackage:p},t.next=52,Object(l["a"])(v);case 52:if(g=t.sent,!g.success){t.next=58;break}e.push(g.result),t.next=59;break;case 58:throw new Error(g.message||"工作流创建API调用失败");case 59:t.next=66;break;case 62:throw t.prev=62,t.t0=t["catch"](6),new Error('工作流"'.concat(r.workflowName,'"保存失败: ').concat(t.t0.message));case 66:a++,t.next=3;break;case 69:return t.abrupt("return",e);case 70:case"end":return t.stop()}}),t,this,[[6,62]])})));function e(){return t.apply(this,arguments)}return e}(),uploadWorkflowFile:function(){var t=v(n.a.mark((function t(e,a){var r=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,s){var i=new FormData;i.append("file",e),i.append("isup","1"),i.append("biz","");r.generateWorkflowFileName(e.name,a);fetch(r.uploadAction,{method:"POST",headers:r.uploadHeaders,body:i}).then((function(t){return t.json()})).then((function(e){e.success?t(e.message):s(new Error(e.message||"文件上传失败"))})).catch((function(t){s(t)}))})));case 1:case"end":return t.stop()}}),t)})));function e(e,a){return t.apply(this,arguments)}return e}(),createAgentStep:function(){var t=v(n.a.mark((function t(e){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,Object(c["a"])(e);case 4:if(a=t.sent,!a.success){t.next=11;break}return this.confirmDeleteOriginalFiles(),t.abrupt("return",a.result);case 11:throw new Error(a.message||"创建智能体失败");case 12:t.next=18;break;case 14:throw t.prev=14,t.t0=t["catch"](0),t.t0;case 18:case"end":return t.stop()}}),t,this,[[0,14]])})));function e(e){return t.apply(this,arguments)}return e}(),updateAgentStep:function(){var t=v(n.a.mark((function t(e){var a,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,a=this.agent.id||this.agent.agentId,a){t.next=5;break}throw new Error("智能体ID不存在");case 5:return t.next=7,Object(c["e"])(a,e);case 7:if(r=t.sent,!r.success){t.next=14;break}return this.confirmDeleteOriginalFiles(),t.abrupt("return",r.result);case 14:throw new Error(r.message||"更新智能体失败");case 15:t.next=21;break;case 17:throw t.prev=17,t.t0=t["catch"](0),t.t0;case 21:case"end":return t.stop()}}),t,this,[[0,17]])})));function e(e){return t.apply(this,arguments)}return e}(),processAvatarValue:function(t){return t?Array.isArray(t)?this.processAvatarValue(t[0]):"object"===f(t)&&t.url?this.processAvatarValue(t.url):"string"===typeof t?this.extractRelativePath(t):String(t):""},extractRelativePath:function(t){if(t.includes("aigcview-tos.tos-cn-shanghai.volces.com/")){var e=t.match(/uploads\/[^?]+/);if(e){var a=e[0];return a}}if(t.includes("cdn.aigcview.cn/")){var r=t.match(/uploads\/[^?]+/);if(r){var s=r[0];return s}}return t.startsWith("uploads/"),t},getFullAvatarUrl:function(t){if(!t)return"";if(t.startsWith("http"))return t;if(t.startsWith("uploads/")){var e="https://cdn.aigcview.cn/".concat(t);return e}var a="https://cdn.aigcview.cn/".concat(t);return a},handleSubmit:function(){var t=v(n.a.mark((function t(){var e,a,r=this;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=this.formData.agentAvatar,a=this.$refs.avatarUpload&&this.$refs.avatarUpload.hasPendingFiles(),!this.formData.agentAvatar&&a&&(this.formData.agentAvatar="pending_upload"),this.$refs.form.validate(function(){var t=v(n.a.mark((function t(a){var s;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r.formData.agentAvatar=e,!a){t.next=17;break}return t.prev=3,t.next=6,r.uploadPendingImages();case 6:s={agentName:r.formData.agentName.trim(),agentDescription:r.formData.agentDescription.trim(),agentAvatar:r.processAvatarValue(r.formData.agentAvatar),experienceLink:r.formData.experienceLink.trim(),price:r.formData.price},r.$emit("submit",s),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](3),r.$message.error("头像上传失败: "+(t.t0.message||"未知错误"));case 15:t.next=19;break;case 17:r.$message.error("请检查表单信息");case 19:case"end":return t.stop()}}),t,null,[[3,11]])})));return function(e){return t.apply(this,arguments)}}());case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleCancel:function(){this.rollbackChanges(),this.clearAllWorkflowData(),this.resetForm(),this.$emit("close")},uploadPendingImages:function(){var t=v(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.$refs.avatarUpload||!this.$refs.avatarUpload.hasPendingFiles()){t.next=18;break}return t.prev=3,t.next=6,this.$refs.avatarUpload.performUpload();case 6:return e=t.sent,this.formData.agentAvatar=e,t.abrupt("return",e);case 12:throw t.prev=12,t.t0=t["catch"](3),new Error("头像上传失败: "+(t.t0.message||"未知错误"));case 16:t.next=20;break;case 18:return t.abrupt("return",this.formData.agentAvatar);case 20:case"end":return t.stop()}}),t,this,[[3,12]])})));function e(){return t.apply(this,arguments)}return e}(),confirmDeleteOriginalFiles:function(){this.$refs.avatarUpload&&this.$refs.avatarUpload.confirmDeleteOriginalFiles&&this.$refs.avatarUpload.confirmDeleteOriginalFiles()},rollbackChanges:function(){this.$refs.avatarUpload&&this.$refs.avatarUpload.rollbackChanges&&this.$refs.avatarUpload.rollbackChanges()},loadWorkflowList:function(){var t=v(n.a.mark((function t(e){var a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return");case 2:return this.workflowLoading=!0,t.prev=3,t.next=7,Object(l["b"])(e);case 7:a=t.sent,a.success?(this.workflowList=a.result||[],"edit"===this.mode&&this.workflowList.length>0?this.convertWorkflowsToTempList(this.workflowList):this.mode):(this.$message.error(a.message||"获取工作流列表失败"),this.workflowList=[]),t.next=17;break;case 12:t.prev=12,t.t0=t["catch"](3),this.$message.error("加载工作流列表失败: "+(t.t0.message||"网络错误")),this.workflowList=[];case 17:return t.prev=17,this.workflowLoading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[3,12,17,20]])})));function e(e){return t.apply(this,arguments)}return e}(),convertWorkflowsToTempList:function(t){var e=this;this.tempWorkflowList=[],t.forEach((function(t,a){var r=t.workflowPackage||"",s=r?r.includes("/")?r.split("/").pop():r:"已上传文件",i={id:t.id||Date.now()+a,workflowName:t.workflowName||"",workflowDescription:t.workflowDescription||"",inputParamsDesc:t.inputParamsDesc||"",workflowFile:null,fileName:s,fileSize:0,workflowPackage:t.workflowPackage||"",status:"saved",createTime:new Date(t.createTime||Date.now()),originalWorkflow:t};e.tempWorkflowList.push(i)}))},beforeWorkflowUpload:function(t){if(!this.isSecureFileName(t.name))return this.$message.error("文件名包含不安全字符，请重命名后重试!"),!1;var e=t.name.toLowerCase();if(!e.endsWith(".zip"))return this.$message.error("只能上传 .zip 格式的文件!"),!1;var a=["application/zip","application/x-zip-compressed","application/x-zip"];if(!a.includes(t.type))return this.$message.error("文件类型不正确，只允许上传ZIP压缩包!"),!1;var r=5242880;return t.size>r?(this.$message.error("文件大小不能超过 5MB!"),!1):0===t.size&&(this.$message.error("文件不能为空!"),!1)},isSecureFileName:function(t){var e=/[<>:"|?*\x00-\x1f]/;if(e.test(t))return!1;if(t.includes("..")||t.includes("./")||t.includes(".\\"))return!1;var a=/^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;return!a.test(t)&&!(t.length>255)},validateWorkflowData:function(){var t=this.workflowFormData,e=t.workflowName,a=t.workflowDescription,r=t.inputParamsDesc;if(!e||0===e.trim().length)return this.$message.error("工作流名称不能为空"),!1;var s=/^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/;if(!s.test(e))return this.$message.error("工作流名称包含不允许的字符"),!1;if(!a||0===a.trim().length)return this.$message.error("工作流描述不能为空"),!1;if(a.length<10||a.length>200)return this.$message.error("工作流描述长度必须在10-200字符之间"),!1;var i=/<script|javascript:|on\w+\s*=/i;return!(i.test(e)||i.test(a)||i.test(r))||(this.$message.error("输入内容包含不安全的脚本代码"),!1)},handleWorkflowFileSelect:function(t){var e=this;if(t.fileList&&t.fileList.length>0){var a=t.fileList[0].originFileObj||t.fileList[0],r=5242880;if(a.size>r)return this.$message.error("文件大小不能超过 5MB!"),this.workflowFileList=[],this.workflowFileInfo=null,void this.$nextTick((function(){var t=e.$refs.workflowUpload;t&&(t.fileList=[])}));if(0===a.size)return this.$message.error("文件不能为空!"),this.workflowFileList=[],void(this.workflowFileInfo=null);if(!this.isSecureFileName(a.name))return this.$message.error("文件名包含不安全字符，请重命名后重试!"),this.workflowFileList=[],void(this.workflowFileInfo=null);var s=a.name.toLowerCase();if(!s.endsWith(".zip"))return this.$message.error("只能上传 .zip 格式的文件!"),this.workflowFileList=[],void(this.workflowFileInfo=null);var i=["application/zip","application/x-zip-compressed","application/x-zip"];if(!i.includes(a.type))return this.$message.error("文件类型不正确，只允许上传ZIP压缩包!"),this.workflowFileList=[],void(this.workflowFileInfo=null);this.workflowFileList=[a],this.workflowFileInfo={name:a.name,originalName:a.name,size:a.size,file:a,isSaved:!1},this.$message.success("工作流文件已选择，将在完成创建时上传")}else t.fileList&&0===t.fileList.length&&(this.workflowFileList=[],this.workflowFileInfo=null)},generateWorkflowFileName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=t.lastIndexOf("."),r=a>-1?t.substring(a):"",s=(new Date).getTime(),i=Math.floor(1e4*Math.random()).toString().padStart(4,"0"),n=this.formData.agentName?this.formData.agentName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g,"_").substring(0,20):"workflow",o=e||this.workflowFormData.workflowName?(e||this.workflowFormData.workflowName).replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g,"_").substring(0,20):"default",c="".concat(n,"_").concat(o,"_").concat(s,"_").concat(i).concat(r);return c},handleWorkflowRemove:function(t){this.workflowFileList=[],this.workflowFormData.workflowPackage="",this.workflowFileInfo=null},handleRemoveWorkflowFile:function(){this.workflowFileList=[],this.workflowFormData.workflowPackage="",this.workflowFileInfo=null,this.$message.success("工作流文件已移除")},formatFileSize:function(t){if(!t)return"0 B";var e=1024,a=["B","KB","MB","GB"],r=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,r)).toFixed(2))+" "+a[r]},handleWorkflowSubmit:function(){var t=this;return new Promise((function(e,a){t.$refs.workflowFormRef.validate((function(r){if(!r)return t.$message.error("请完善表单信息"),void a(new Error("表单验证失败"));t.performWorkflowSubmit().then(e).catch(a)}))}))},performWorkflowSubmit:function(){var t=v(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,0!==this.workflowFileList.length){t.next=4;break}throw this.$message.error("请选择工作流文件"),new Error("未选择工作流文件");case 4:if(e=this.workflowFileList[0],this.isSecureFileName(e.name)){t.next=8;break}throw this.$message.error("文件名不安全，请重新选择文件"),new Error("文件名不安全");case 8:if(e.name.toLowerCase().endsWith(".zip")){t.next=11;break}throw this.$message.error("只能提交ZIP格式的文件"),new Error("文件格式不正确");case 11:if(!(e.size>5242880)){t.next=14;break}throw this.$message.error("文件大小超过5MB限制"),new Error("文件大小超限");case 14:if(this.validateWorkflowData()){t.next=16;break}throw new Error("表单数据验证失败");case 16:return this.stepLoading=!0,t.next=20,new Promise((function(t){return setTimeout(t,1e3)}));case 20:return this.$message.success("工作流创建成功!"),this.loadWorkflowList(this.createdAgent.id),this.resetWorkflowForm(),t.abrupt("return",!0);case 26:throw t.prev=26,t.t0=t["catch"](0),this.$message.error("工作流创建失败: "+(t.t0.message||"未知错误")),t.t0;case 31:return t.prev=31,this.stepLoading=!1,t.finish(31);case 34:case"end":return t.stop()}}),t,this,[[0,26,31,34]])})));function e(){return t.apply(this,arguments)}return e}(),resetWorkflowForm:function(){var t=this;this.workflowFormData={workflowName:"",workflowDescription:"",inputParamsDesc:"",workflowPackage:""},this.workflowFileList=[],this.workflowFileInfo=null,this.currentWorkflowIndex=-1,this.$nextTick((function(){t.$refs.workflowFormRef&&t.$refs.workflowFormRef.resetFields()}))},clearAllWorkflowData:function(){this.tempWorkflowList=[],this.resetWorkflowForm(),this.workflowFileList=[],this.workflowFileInfo=null,this.currentWorkflowIndex=-1,this.workflowValidationErrors={},this.workflowUploading=!1,this.workflowList=[],this.workflowLoading=!1},handleInputParamsBlur:function(){var t=this;this.$nextTick((function(){t.$refs.workflowFormRef&&t.$refs.workflowFormRef.validateField("inputParamsDesc",(function(t){}))}))},scrollToTop:function(){this.$nextTick((function(){var t=document.querySelector(".step-content");if(t){var e=t.scrollTop;t.scrollTop=0;var a=t.scrollTop;if(e!==a||0===e)return}for(var r=[".creator-agent-modal .ant-modal-body",".ant-modal-body",".creator-agent-modal .ant-modal-content",".ant-modal-content",".creator-agent-modal",".ant-modal-wrap"],s=null,i=0,n=r;i<n.length;i++){var o=n[i],c=document.querySelector(o);if(c&&(c.scrollHeight>c.clientHeight||c.scrollTop>0)){s=c,o;break}}if(s){var l=s.scrollTop;s.scrollTop=0;var d=s.scrollTop;l===d&&l>0&&s.scrollTo({top:0,behavior:"smooth"})}else setTimeout((function(){var t,e=document.querySelectorAll('[class*="modal"]'),a=u(e);try{for(a.s();!(t=a.n()).done;){var r=t.value;if(r.scrollHeight>r.clientHeight||r.scrollTop>0)return void(r.scrollTop=0)}}catch(s){a.e(s)}finally{a.f()}}),200)}))},addNextWorkflow:function(){this.autoSaveCurrentWorkflow(),this.resetWorkflowForm(),this.currentWorkflowIndex=-1,this.scrollToTop(),this.$message.success("可以继续添加新工作流")},validateCurrentWorkflowData:function(){if(!this.workflowFormData.workflowName.trim())return this.$message.error("请输入工作流名称"),!1;if(this.workflowFormData.workflowName.trim().length>30)return this.$message.error("工作流名称不能超过30个字符"),!1;if(!this.workflowFormData.workflowDescription.trim())return this.$message.error("请输入工作流描述"),!1;if(this.workflowFormData.workflowDescription.trim().length>200)return this.$message.error("工作流描述不能超过200个字符"),!1;if(!this.workflowFormData.inputParamsDesc.trim())return this.$message.error("请输入参数说明"),!1;var t=/^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/;if(!t.test(this.workflowFormData.inputParamsDesc.trim()))return this.$message.error("请按照格式填写：参数:值 或 参数:\"值\" 或 参数:'值'"),!1;var e=this.workflowFileList.length>0,a=this.workflowFileInfo&&this.workflowFileInfo.isSaved;return!(!e&&!a)||(this.$message.error("工作流文件为必填项，请上传ZIP压缩包"),!1)},saveCurrentWorkflowToTemp:function(){var t=this.workflowFormData.workflowName.trim();if(!t){var e=(new Date).toLocaleString("zh-CN");t="工作流_".concat(e)}var a=this.currentWorkflowIndex>=0,r=a?this.tempWorkflowList[this.currentWorkflowIndex]:null,s={id:a?r.id:Date.now(),workflowName:t,workflowDescription:this.workflowFormData.workflowDescription.trim(),inputParamsDesc:this.workflowFormData.inputParamsDesc.trim(),workflowFile:this.workflowFileList.length>0?this.workflowFileList[0]:r?r.workflowFile:null,fileName:this.workflowFileInfo?this.workflowFileInfo.name:r?r.fileName:"",fileSize:this.workflowFileInfo?this.workflowFileInfo.size:r?r.fileSize:0,status:r&&"saved"===r.status?"saved":"draft",createTime:a?r.createTime:new Date,workflowPackage:r?r.workflowPackage:"",originalWorkflow:r?r.originalWorkflow:null};this.currentWorkflowIndex>=0?this.tempWorkflowList.splice(this.currentWorkflowIndex,1,s):this.tempWorkflowList.push(s),this.updateWorkflowValidation(s)},loadWorkflowFromTemp:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!(t<0||t>=this.tempWorkflowList.length)){e||this.autoSaveCurrentWorkflow();var a=this.tempWorkflowList[t];this.workflowFormData={workflowName:a.workflowName,workflowDescription:a.workflowDescription,inputParamsDesc:a.inputParamsDesc||"",workflowPackage:""},"saved"===a.status&&a.fileName?(this.workflowFileList=[],this.workflowFileInfo={name:a.fileName,originalName:a.fileName,size:a.fileSize||0,file:null,isSaved:!0,packagePath:a.workflowPackage||""}):a.workflowFile&&a.fileName?(this.workflowFileList=[a.workflowFile],this.workflowFileInfo={name:a.fileName,originalName:a.fileName,size:a.fileSize||0,file:a.workflowFile,isSaved:!1}):(this.workflowFileList=[],this.workflowFileInfo=null),this.currentWorkflowIndex=t,e||(this.$message.info("已加载工作流: ".concat(a.workflowName)),this.scrollToTop())}},autoSaveCurrentWorkflow:function(){var t=this.workflowFormData.workflowName.trim()||this.workflowFormData.workflowDescription.trim()||this.workflowFileList.length>0||this.workflowFileInfo&&this.workflowFileInfo.isSaved;return!!t&&(this.currentWorkflowIndex>=0?(this.saveCurrentWorkflowToTemp(),!0):(this.saveCurrentWorkflowToTemp(),this.stepLoading||this.$message.info("当前工作流数据已自动暂存"),!0))},validateAllWorkflows:function(){this.workflowValidationErrors={};for(var t=!0,e=0,a=[],r=0;r<this.tempWorkflowList.length;r++){var s=this.tempWorkflowList[r],i=[];if(s.workflowName&&s.workflowName.trim()||i.push("缺少工作流名称"),s.workflowDescription&&s.workflowDescription.trim()||i.push("缺少工作流描述"),s.inputParamsDesc&&s.inputParamsDesc.trim()){var n=/^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/;n.test(s.inputParamsDesc.trim())||i.push("参数说明格式不正确")}else i.push("缺少参数说明");"saved"===s.status?s.fileName||s.workflowPackage||i.push("缺少压缩包文件"):s.workflowFile&&s.fileName||i.push("缺少压缩包文件");var o=0===i.length;this.workflowValidationErrors[s.id]={errors:i,isValid:o,workflowName:s.workflowName||"工作流".concat(r+1)},o||(t=!1,e++,a.push("".concat(s.workflowName||"工作流".concat(r+1),": ").concat(i.join("、"))))}var c={isValid:t,invalidCount:e,errors:a,validationDetails:this.workflowValidationErrors};return c},updateWorkflowValidation:function(t){var e=[];if(t.workflowName&&t.workflowName.trim()||e.push("缺少工作流名称"),t.workflowDescription&&t.workflowDescription.trim()||e.push("缺少工作流描述"),t.inputParamsDesc&&t.inputParamsDesc.trim()){var a=/^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/;a.test(t.inputParamsDesc.trim())||e.push("参数说明格式不正确")}else e.push("缺少参数说明");"saved"===t.status?t.fileName||t.workflowPackage||e.push("缺少压缩包文件"):t.workflowFile&&t.fileName||e.push("缺少压缩包文件");var r=0===e.length;this.$set(this.workflowValidationErrors,t.id,{errors:e,isValid:r,workflowName:t.workflowName||"未命名工作流"})},handleValidationErrors:function(t){for(var e=this,a=-1,r=null,s=0;s<this.tempWorkflowList.length;s++){var i=this.tempWorkflowList[s],n=t.validationDetails[i.id];if(n&&!n.isValid){a=s,r=i;break}}if(a>=0&&r){this.loadWorkflowFromTemp(a,!0);var o=t.validationDetails[r.id],c='工作流"'.concat(r.workflowName,'"缺少必填信息：').concat(o.errors.join("、"));this.$message.error(c),setTimeout((function(){e.$message.warning("共有 ".concat(t.invalidCount," 个工作流存在问题，请逐一完善后再提交"))}),1e3)}else this.$message.error("请完善工作流信息后再提交，共有 ".concat(t.invalidCount," 个工作流存在问题"))},deleteWorkflowFromTemp:function(t){var e=this;if(!(t<0||t>=this.tempWorkflowList.length)){var a=this.tempWorkflowList[t];this.$confirm({title:"删除工作流",content:'确定要删除工作流"'.concat(a.workflowName,'"吗？'),onOk:function(){e.tempWorkflowList.splice(t,1),e.currentWorkflowIndex===t?(e.resetWorkflowForm(),e.currentWorkflowIndex=-1,e.scrollToTop()):e.currentWorkflowIndex>t&&e.currentWorkflowIndex--,e.$message.success("工作流已删除")}})}},formatDate:function(t){return t?new Date(t).toLocaleString("zh-CN"):""}}},b=k,C=(a("b339"),a("2877")),y=Object(C["a"])(b,r,s,!1,null,"2a253ea7",null);e["default"]=y.exports},"5e26":function(t,e,a){},"677ea":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"revenue-stats"},[a("div",{staticClass:"stats-cards"},[a("div",{staticClass:"stats-row"},[a("div",{staticClass:"stat-card revenue-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"dollar"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatMoney(t.data.totalRevenue)))]),a("div",{staticClass:"stat-label"},[t._v("总收益")])])])]),a("div",{staticClass:"stat-card month-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"calendar"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatMoney(t.data.monthRevenue)))]),a("div",{staticClass:"stat-label"},[t._v("本月收益")])])])]),a("div",{staticClass:"stat-card yesterday-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"clock-circle"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatMoney(t.data.yesterdayRevenue)))]),a("div",{staticClass:"stat-label"},[t._v("昨日收益")])])])]),a("div",{staticClass:"refresh-action"},[a("a-button",{attrs:{loading:t.loading,size:"large"},on:{click:t.handleRefresh}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n          刷新数据\n        ")],1)],1)]),a("div",{staticClass:"stats-row"},[a("div",{staticClass:"stat-card sales-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"shopping-cart"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.totalSales||0))]),a("div",{staticClass:"stat-label"},[t._v("总销售次数")])])])]),a("div",{staticClass:"stat-card agent-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"robot"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.agentCount||0))]),a("div",{staticClass:"stat-label"},[t._v("智能体总数")])])])]),a("div",{staticClass:"stat-card approved-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"check-circle"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.approvedAgentCount||0))]),a("div",{staticClass:"stat-label"},[t._v("已通过")])])])]),a("div",{staticClass:"stat-card workflow-card"},[a("div",{staticClass:"card-content"},[a("div",{staticClass:"stat-icon"},[a("a-icon",{attrs:{type:"apartment"}})],1),a("div",{staticClass:"stat-info"},[a("div",{staticClass:"stat-value"},[t._v(t._s(t.data.workflowCount||0))]),a("div",{staticClass:"stat-label"},[t._v("工作流总数")])])])])])]),t.data.agentRevenueList&&t.data.agentRevenueList.length>0?a("div",{staticClass:"revenue-ranking"},[a("div",{staticClass:"ranking-header"},[a("h3",{staticClass:"ranking-title"},[a("a-icon",{attrs:{type:"bar-chart"}}),t._v("\n        智能体收益排行\n      ")],1),a("div",{staticClass:"ranking-actions"},[a("a-button",{attrs:{type:"link"},on:{click:t.toggleRankingExpanded}},[t._v("\n          "+t._s(t.rankingExpanded?"收起":"展开全部")+"\n          "),a("a-icon",{attrs:{type:t.rankingExpanded?"up":"down"}})],1)],1)]),a("div",{staticClass:"ranking-list"},t._l(t.displayRankingList,(function(e,r){return a("div",{key:e.agentId,staticClass:"ranking-item",class:{"top-three":r<3}},[a("div",{staticClass:"ranking-number"},[a("span",{staticClass:"rank-badge",class:"rank-"+(r+1)},[t._v(t._s(r+1))])]),a("div",{staticClass:"agent-info"},[a("div",{staticClass:"agent-avatar"},[e.agentAvatar?a("img",{attrs:{src:e.agentAvatar,alt:e.agentName},on:{error:t.handleImageError}}):a("div",{staticClass:"avatar-placeholder"},[a("a-icon",{attrs:{type:"robot"}})],1)]),a("div",{staticClass:"agent-details"},[a("div",{staticClass:"agent-name"},[t._v(t._s(e.agentName))]),a("div",{staticClass:"agent-meta"},[a("a-tag",{attrs:{color:t.getStatusColor(e.auditStatus),size:"small"}},[t._v("\n                "+t._s(e.auditStatusText)+"\n              ")]),a("span",{staticClass:"workflow-count"},[t._v(t._s(e.workflowCount||0)+" 个工作流")])],1)])]),a("div",{staticClass:"revenue-info"},[a("div",{staticClass:"total-revenue"},[a("span",{staticClass:"revenue-value"},[t._v("¥"+t._s(t.formatMoney(e.totalRevenue)))]),a("span",{staticClass:"revenue-label"},[t._v("总收益")])]),a("div",{staticClass:"month-revenue"},[a("span",{staticClass:"revenue-value"},[t._v("¥"+t._s(t.formatMoney(e.monthRevenue)))]),a("span",{staticClass:"revenue-label"},[t._v("本月收益")])])]),a("div",{staticClass:"sales-info"},[a("div",{staticClass:"sales-count"},[t._v(t._s(e.salesCount||0))]),a("div",{staticClass:"sales-label"},[t._v("销售次数")])])])})),0)]):t.loading?t._e():a("div",{staticClass:"empty-revenue"},[a("a-empty",{attrs:{description:"暂无收益数据",image:t.emptyImage}},[a("p",[t._v("创建并发布智能体后，这里将显示收益统计信息")])])],1)])},s=[],i=a("fc25");function n(t){return u(t)||l(t)||c(t)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"===typeof t)return d(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function l(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function u(t){if(Array.isArray(t))return d(t)}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}var h={name:"RevenueStats",props:{loading:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},data:function(){return{rankingExpanded:!1,emptyImage:i["a"].PRESENTED_IMAGE_SIMPLE}},computed:{displayRankingList:function(){if(!this.data.agentRevenueList)return[];var t=n(this.data.agentRevenueList).sort((function(t,e){var a=Number(t.totalRevenue)||0,r=Number(e.totalRevenue)||0;return r-a}));return this.rankingExpanded?t:t.slice(0,5)}},methods:{formatMoney:function(t){return t?Number(t).toFixed(2):"0.00"},getStatusColor:function(t){var e={1:"orange",2:"green",3:"red"};return e[t]||"default"},handleImageError:function(t){t.target.style.display="none",t.target.nextElementSibling.style.display="flex"},toggleRankingExpanded:function(){this.rankingExpanded=!this.rankingExpanded},handleRefresh:function(){this.$emit("refresh")}}},f=h,p=(a("ec44a"),a("2877")),v=Object(p["a"])(f,r,s,!1,null,"6deb4dc1",null);e["default"]=v.exports},"81eb":function(t,e,a){},"8582f":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"workflow-center"},[a("div",{staticClass:"workflow-tabs"},[a("div",{staticClass:"tabs-container"},[a("div",{staticClass:"tab-nav"},[a("div",{staticClass:"tab-title",attrs:{"aria-label":"工作流中心"}},[a("a-icon",{attrs:{type:"deployment-unit"}}),a("span",[t._v("工作流中心")])],1),a("div",{staticClass:"tab-item",class:{active:"market"===t.activeTab},on:{click:function(e){return t.switchTab("market")}}},[a("a-icon",{attrs:{type:"shop"}}),a("span",[t._v("智能体市场")])],1),a("div",{staticClass:"tab-item",class:{active:"creator"===t.activeTab},on:{click:function(e){return t.switchTab("creator")}}},[a("a-icon",{attrs:{type:"build"}}),a("span",[t._v("创作者中心")])],1)])])]),a("div",{staticClass:"workflow-content"},[a("div",{staticClass:"content-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:"market"===t.activeTab,expression:"activeTab === 'market'"}],staticClass:"tab-content"},[a("AgentMarket")],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"creator"===t.activeTab,expression:"activeTab === 'creator'"}],staticClass:"tab-content"},[a("CreatorCenter",{ref:"creatorCenter"})],1)])])])])},s=[],i=a("df7c"),n=a("1323"),o=a("349e"),c=a("9fb0"),l={name:"WorkflowCenter",components:{WebsitePage:i["default"],AgentMarket:n["default"],CreatorCenter:o["default"]},data:function(){return{activeTab:"market"}},mounted:function(){var t=this.$route.query.tab;if("creator"===t){var e=this.$ls.get(c["a"]);if(!e)return void this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}});this.activeTab="creator",document.title="创作者中心 - 智界AIGC"}else this.activeTab="market",document.title="AI工作流中心 - 智界AIGC"},watch:{"$route.query.tab":function(t){var e=this;if("creator"===t){var a=this.$ls.get(c["a"]);if(!a)return void this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}});this.activeTab="creator",document.title="创作者中心 - 智界AIGC",this.$nextTick((function(){e.refreshCreatorCenter()}))}else this.activeTab="market",document.title="AI工作流中心 - 智界AIGC"}},methods:{switchTab:function(t){var e=this;if("creator"===t){var a=this.$ls.get(c["a"]);if(!a){var r="/workflow-center?tab=".concat(t);return void this.$router.push({path:"/login",query:{redirect:r}})}}this.activeTab=t,this.$router.push({path:"/workflow-center",query:{tab:t}}).catch((function(){})),"creator"===t?(document.title="创作者中心 - 智界AIGC",this.$nextTick((function(){e.refreshCreatorCenter()}))):document.title="AI工作流中心 - 智界AIGC"},refreshCreatorCenter:function(){this.$refs.creatorCenter&&this.$refs.creatorCenter.refreshAllData()}}},u=l,d=(a("00ea"),a("2877")),h=Object(d["a"])(u,r,s,!1,null,"602ebded",null);e["default"]=h.exports},a763:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"agent-detail-modal-wrapper"},[a("a-modal",{staticClass:"agent-detail-modal custom-modal",attrs:{visible:t.visible,width:800,footer:null,closable:!1,maskClosable:!0,bodyStyle:{padding:0,borderRadius:"16px",overflow:"hidden"},centered:!0,destroyOnClose:!0},on:{cancel:t.handleClose}},[t.loading?a("div",{staticClass:"loading-container"},[a("a-spin",{attrs:{size:"large",tip:"加载中..."}},[a("div",{staticClass:"loading-placeholder"})])],1):a("div",{staticClass:"modal-content"},[a("div",{staticClass:"custom-close-button",on:{click:t.handleClose}},[a("a-icon",{attrs:{type:"close"}})],1),a("div",{staticClass:"modal-background"},[a("div",{staticClass:"bg-pattern"}),a("div",{staticClass:"bg-gradient"})]),a("div",{staticClass:"agent-info-section"},[a("div",{staticClass:"agent-header"},[a("div",{staticClass:"agent-avatar"},[t.agentDetail.agentAvatar?a("img",{attrs:{src:t.agentDetail.agentAvatar,alt:t.agentDetail.agentName},on:{error:t.handleImageError}}):a("div",{staticClass:"avatar-placeholder"},[a("a-icon",{attrs:{type:"robot"}})],1)]),a("div",{staticClass:"agent-info-and-price"},[a("div",{staticClass:"agent-basic-info"},[a("h2",{staticClass:"agent-name"},[t._v(t._s(t.agentDetail.agentName))]),a("p",{staticClass:"agent-description"},[t._v(t._s(t.agentDetail.agentDescription))]),a("div",{staticClass:"creator-info"},[a("div",{staticClass:"creator-avatar"},[t.agentDetail.creatorInfo&&t.agentDetail.creatorInfo.avatar?a("img",{attrs:{src:t.agentDetail.creatorInfo.avatar,alt:t.agentDetail.creatorInfo.name},on:{error:t.handleCreatorAvatarError}}):a("a-icon",{attrs:{type:"user"}})],1),a("div",{staticClass:"creator-details"},[a("div",{staticClass:"creator-name-line"},[a("span",{staticClass:"creator-name"},[t._v(t._s(t.creatorName))]),a("span",{staticClass:"creator-type"},[t._v(t._s(t.authorTypeText))])])])])]),a("div",{staticClass:"price-section"},[t.agentDetail.isFree?a("div",{staticClass:"price-container"},[a("span",{staticClass:"free-price"},[t._v("免费")])]):t.isPurchased?a("div",{staticClass:"price-container"},[a("span",{staticClass:"purchased-price"},[t._v("已购买")])]):t.agentDetail.showDiscountPrice?a("div",{staticClass:"price-container"},[a("span",{staticClass:"discount-price"},[t._v("¥"+t._s(t.agentDetail.discountPrice||0))]),a("span",{staticClass:"original-price"},[t._v("¥"+t._s(t.agentDetail.originalPrice||t.agentDetail.price||0))])]):a("div",{staticClass:"price-container"},[a("span",{staticClass:"current-price"},[t._v("¥"+t._s(t.agentDetail.discountPrice||t.agentDetail.price||t.agentDetail.originalPrice||0))])])])])])]),t.agentDetail.demoVideo?a("div",{staticClass:"demo-video-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"play-circle"}}),t._v("\n        演示视频\n      ")],1),a("div",{staticClass:"video-container"},[t.videoError?t._e():a("div",{staticClass:"video-wrapper"},[a("video",{ref:"demoVideo",staticClass:"demo-video",attrs:{src:t.agentDetail.demoVideo,controls:"",preload:"metadata"},on:{loadstart:t.handleVideoLoadStart,loadeddata:t.handleVideoLoaded,error:t.handleVideoError}},[t._v("\n            您的浏览器不支持视频播放\n          ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.videoLoading,expression:"videoLoading"}],staticClass:"video-loading"},[a("a-spin",{attrs:{size:"large"}},[a("a-icon",{staticStyle:{"font-size":"24px"},attrs:{slot:"indicator",type:"loading",spin:""},slot:"indicator"})],1),a("p",[t._v("视频加载中...")])],1)]),t.videoError?a("div",{staticClass:"video-error-placeholder"},[a("div",{staticClass:"error-content"},[a("a-icon",{staticClass:"error-icon",attrs:{type:"exclamation-circle"}}),a("h4",[t._v("视频加载失败")]),a("p",[t._v("抱歉，演示视频暂时无法播放")]),a("a-button",{attrs:{type:"primary",ghost:""},on:{click:t.retryVideoLoad}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n              重新加载\n            ")],1)],1)]):t._e()])]):t._e(),a("div",{staticClass:"workflow-section"},[a("h3",{staticClass:"section-title"},[t._v("\n        工作流列表\n        "),a("span",{staticClass:"workflow-count"},[t._v("("+t._s(t.workflowList.length)+"个)")])]),t.workflowLoading?a("div",{staticClass:"workflow-loading"},[a("a-spin",{attrs:{tip:"加载工作流中..."}})],1):t.workflowList.length>0?a("div",{staticClass:"workflow-list"},t._l(t.workflowList,(function(e,r){return a("div",{key:e.id,staticClass:"workflow-item"},[a("div",{staticClass:"workflow-info"},[a("div",{staticClass:"workflow-sequence"},[t._v(t._s(r+1))]),a("div",{staticClass:"workflow-avatar"},[e.agentAvatar||t.agentDetail.agentAvatar?a("img",{attrs:{src:e.agentAvatar||t.agentDetail.agentAvatar,alt:e.workflowName},on:{error:t.handleWorkflowImageError}}):a("a-icon",{attrs:{type:"setting"}})],1),a("div",{staticClass:"workflow-details"},[a("h4",{staticClass:"workflow-name"},[t._v(t._s(e.workflowName))]),a("p",{staticClass:"workflow-description"},[t._v(t._s(e.workflowDescription))]),a("div",{staticClass:"workflow-params"},[a("div",{staticClass:"params-label"},[a("a-icon",{attrs:{type:"setting"}}),a("span",[t._v("输入参数说明")])],1),a("div",{staticClass:"params-content"},[t._v("\n                  "+t._s(e.inputParamsDesc||"暂无输入参数说明")+"\n                ")])])])]),a("div",{staticClass:"workflow-actions"},[t.isPurchased||t.agentDetail.isFree?a("a-button",{attrs:{type:"primary",loading:t.downloadLoading[e.id]},on:{click:function(a){return t.handleWorkflowDownload(e)}}},[a("a-icon",{attrs:{type:"download"}}),t._v("\n                下载\n              ")],1):a("a-button",{attrs:{type:"default",disabled:""},on:{click:t.handleDownloadTip}},[a("a-icon",{attrs:{type:"download"}}),t._v("\n                请先购买\n              ")],1)],1)])})),0):a("div",{staticClass:"workflow-empty"},[a("a-empty",{attrs:{description:"暂无工作流"}})],1)]),a("div",{staticClass:"action-buttons modern-actions"},[a("a-button",{staticClass:"close-btn modern-btn-secondary",on:{click:t.handleClose}},[a("a-icon",{attrs:{type:"close"}}),t._v("\n        关闭\n      ")],1),a("div",{staticClass:"primary-actions"},[t.isPurchased||t.agentDetail.isFree?a("a-button",{staticClass:"detail-btn modern-btn-outline",attrs:{type:"default"},on:{click:t.handleViewDetail}},[a("a-icon",{attrs:{type:"eye"}}),t._v("\n            查看详情\n          ")],1):a("a-button",{staticClass:"detail-btn modern-btn-outline disabled",attrs:{type:"default",disabled:""}},[a("a-icon",{attrs:{type:"eye"}}),t._v("\n            查看详情\n          ")],1),t.isPurchased||t.agentDetail.isFree?t._e():a("a-button",{staticClass:"purchase-btn modern-btn-primary",attrs:{type:"primary",loading:t.purchaseLoading},on:{click:t.handlePurchase}},[a("a-icon",{attrs:{type:"shopping-cart"}}),t._v("\n            立即购买\n          ")],1),t.agentDetail.experienceLink?a("a-button",{staticClass:"experience-btn modern-btn-outline",attrs:{type:"default"},on:{click:t.handleExperience}},[a("a-icon",{attrs:{type:"robot"}}),t._v("\n          体验智能体\n        ")],1):a("a-button",{staticClass:"experience-btn modern-btn-outline disabled",attrs:{type:"default",disabled:""}},[a("a-icon",{attrs:{type:"robot"}}),t._v("\n          暂无体验\n        ")],1)],1)],1)])]),a("a-modal",{staticClass:"payment-modal",attrs:{title:"选择支付方式",width:520,footer:null,maskClosable:!1,centered:!0},model:{value:t.showPaymentModal,callback:function(e){t.showPaymentModal=e},expression:"showPaymentModal"}},[a("div",{staticClass:"payment-content"},[a("div",{staticClass:"order-info-card"},[a("div",{staticClass:"order-header"},[a("div",{staticClass:"order-icon"},[a("a-icon",{attrs:{type:"shopping-cart"}})],1),a("div",{staticClass:"order-title"},[a("h3",[t._v("订单详情")]),a("p",[t._v("请确认您的购买信息")])])]),a("div",{staticClass:"order-details"},[a("div",{staticClass:"order-item"},[a("span",{staticClass:"label"},[t._v("智能体名称")]),a("span",{staticClass:"value"},[t._v(t._s(t.orderInfo&&t.orderInfo.agentName))])]),a("div",{staticClass:"order-item total"},[a("span",{staticClass:"label"},[t._v("支付金额")]),a("span",{staticClass:"price"},[t._v("¥"+t._s(t.orderInfo&&t.orderInfo.purchasePrice))])])])]),a("div",{staticClass:"payment-methods"},[a("div",{staticClass:"payment-header"},[a("h3",[t._v("选择支付方式")]),a("p",[t._v("请选择您偏好的支付方式完成购买")])]),a("div",{staticClass:"payment-option",class:{insufficient:t.userBalance<(t.orderInfo&&t.orderInfo.purchasePrice),selected:"balance"===t.selectedPaymentMethod},on:{click:function(e){return t.selectPaymentMethod("balance")}}},[a("div",{staticClass:"payment-icon balance"},[a("a-icon",{attrs:{type:"wallet"}})],1),a("div",{staticClass:"payment-info"},[a("div",{staticClass:"payment-title"},[t._v("\n            账户余额支付\n            "),t.userBalance<(t.orderInfo&&t.orderInfo.purchasePrice)?a("span",{staticClass:"insufficient-tag"},[t._v("余额不足")]):t._e()]),a("div",{staticClass:"payment-desc"},[t._v("当前余额：¥"+t._s(t.userBalance))])]),a("div",{staticClass:"payment-status"},["balance"===t.selectedPaymentMethod?a("a-icon",{staticClass:"selected-icon",attrs:{type:"check-circle"}}):t.userBalance<(t.orderInfo&&t.orderInfo.purchasePrice)?a("a-icon",{staticClass:"insufficient",attrs:{type:"exclamation-circle"}}):a("a-icon",{staticClass:"available",attrs:{type:"wallet"}})],1)]),a("div",{staticClass:"payment-option",class:{selected:"alipay"===t.selectedPaymentMethod},on:{click:function(e){return t.selectPaymentMethod("alipay")}}},[a("div",{staticClass:"payment-icon alipay"},[a("a-icon",{attrs:{type:"alipay"}})],1),a("div",{staticClass:"payment-info"},[a("div",{staticClass:"payment-title"},[t._v("\n            支付宝支付\n          ")]),a("div",{staticClass:"payment-desc"},[t._v("安全便捷的在线支付")])]),a("div",{staticClass:"payment-status"},["alipay"===t.selectedPaymentMethod?a("a-icon",{staticClass:"selected-icon",attrs:{type:"check-circle"}}):a("a-icon",{attrs:{type:"right"}})],1)])]),a("div",{staticClass:"payment-actions"},[a("a-button",{staticClass:"cancel-btn",on:{click:function(e){t.showPaymentModal=!1}}},[a("a-icon",{attrs:{type:"close"}}),t._v("\n        取消支付\n      ")],1),a("a-button",{staticClass:"confirm-payment-btn",attrs:{type:"primary",disabled:!t.selectedPaymentMethod||"balance"===t.selectedPaymentMethod&&t.userBalance<(t.orderInfo&&t.orderInfo.purchasePrice),loading:t.paymentLoading},on:{click:t.confirmPayment}},[a("a-icon",{attrs:{type:"credit-card"}}),t._v("\n        确认支付 ¥"+t._s(t.orderInfo&&t.orderInfo.purchasePrice)+"\n      ")],1)],1)]),t.paymentLoading?a("div",{staticClass:"payment-loading"},[a("a-spin",{attrs:{size:"large",tip:"处理支付中..."}})],1):t._e()])],1)},s=[],i=a("a34a"),n=a.n(i);function o(t,e,a,r,s,i,n){try{var o=t[i](n),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(r,s)}function c(t){return function(){var e=this,a=arguments;return new Promise((function(r,s){var i=t.apply(e,a);function n(t){o(i,r,s,n,c,"next",t)}function c(t){o(i,r,s,n,c,"throw",t)}n(void 0)}))}}var l={name:"AgentDetailModal",props:{visible:{type:Boolean,default:!1},agentId:{type:String,default:""},isPurchased:{type:Boolean,default:!1}},data:function(){return{loading:!1,workflowLoading:!1,purchaseLoading:!1,downloadLoading:{},agentDetail:{},workflowList:[],videoLoading:!1,videoError:!1,showPaymentModal:!1,orderInfo:null,paymentLoading:!1,userBalance:0,selectedPaymentMethod:null}},computed:{creatorName:function(){return this.agentDetail.creatorInfo&&(this.agentDetail.creatorInfo.nickname||this.agentDetail.creatorInfo.name)||"未知创作者"},authorTypeText:function(){return"1"===this.agentDetail.authorType?"官方":"2"===this.agentDetail.authorType?"创作者":"未知"},finalPrice:function(){if(!this.agentDetail)return 0;if(this.agentDetail.isFree)return 0;var t=parseFloat(this.agentDetail.price||this.agentDetail.originalPrice)||0,e=parseFloat(this.agentDetail.discountPrice)||t;return this.agentDetail.showDiscountPrice?e:t}},watch:{visible:function(t){t&&this.agentId?(this.loadAgentDetail(),this.loadWorkflowList()):this.resetData()}},methods:{loadAgentDetail:function(){var t=c(n.a.mark((function t(){var e,a,r;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.prev=1,t.next=4,this.$http.get("/api/agent/market/detail/".concat(this.agentId));case 4:if(e=t.sent,a=e.data||e,!a.success||!a.result){t.next=10;break}this.agentDetail=a.result,t.next=11;break;case 10:throw new Error(a.message||"获取智能体详情失败");case 11:t.next=20;break;case 13:t.prev=13,t.t0=t["catch"](1),this.$message.error("加载智能体详情失败"),r=this.$parent.selectedAgent||{},this.agentDetail={id:this.agentId,agentName:r.agentName||"智能体",agentDescription:r.agentDescription||r.description||"暂无描述",agentAvatar:r.agentAvatar||"",demoVideo:r.demoVideo||"",experienceLink:r.experienceLink||"",price:r.price||0,originalPrice:r.originalPrice||r.price||0,authorType:r.authorType||"1",showSvipPromo:r.showSvipPromo||!1,showDiscountPrice:r.showDiscountPrice||!1,discountRate:r.discountRate||1,isFree:r.isFree||!1,creatorInfo:{name:"未知创作者",nickname:"未知创作者",avatar:""}};case 20:return t.prev=20,this.loading=!1,t.finish(20);case 23:case"end":return t.stop()}}),t,this,[[1,13,20,23]])})));function e(){return t.apply(this,arguments)}return e}(),loadWorkflowList:function(){var t=c(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.workflowLoading=!0,t.prev=1,t.next=5,fetch("/jeecg-boot/api/agent/market/".concat(this.agentId,"/workflows"));case 5:return e=t.sent,t.next=8,e.json();case 8:if(a=t.sent,!a.success||!a.result){t.next=14;break}this.workflowList=a.result,t.next=15;break;case 14:throw new Error(a.message||"获取工作流列表失败");case 15:t.next=23;break;case 17:t.prev=17,t.t0=t["catch"](1),this.$message.error("加载工作流列表失败: "+(t.t0.message||t.t0)),this.workflowList=[];case 23:return t.prev=23,this.workflowLoading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[1,17,23,26]])})));function e(){return t.apply(this,arguments)}return e}(),resetData:function(){this.agentDetail={},this.workflowList=[],this.downloadLoading={},this.videoLoading=!1,this.videoError=!1,this.$refs.demoVideo&&(this.$refs.demoVideo.pause(),this.$refs.demoVideo.currentTime=0)},handleClose:function(){this.$emit("close")},handlePurchase:function(){this.processPurchase()},processPurchase:function(){var t=c(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.purchaseLoading=!0,t.prev=1,e={agentId:this.agentDetail.id,agentName:this.agentDetail.agentName,purchasePrice:this.finalPrice,originalPrice:this.agentDetail.price||this.agentDetail.originalPrice,discountRate:this.agentDetail.discountRate||100},t.next=6,this.$http.post("/api/agent/market/purchase",e);case 6:if(a=t.sent,!a.success){t.next=16;break}return t.next=11,this.loadUserBalance();case 11:this.orderInfo=a.result,this.selectedPaymentMethod=null,this.showPaymentModal=!0,t.next=17;break;case 16:throw new Error(a.message||"创建订单失败");case 17:t.next=23;break;case 19:t.prev=19,t.t0=t["catch"](1),t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$message.error(t.t0.response.data.message):this.$message.error("创建订单失败，请稍后重试");case 23:return t.prev=23,this.purchaseLoading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[1,19,23,26]])})));function e(){return t.apply(this,arguments)}return e}(),selectPaymentMethod:function(t){"balance"===t&&this.userBalance<(this.orderInfo&&this.orderInfo.purchasePrice)?this.$message.warning("账户余额不足，请选择其他支付方式"):this.selectedPaymentMethod=t},confirmPayment:function(){var t=c(n.a.mark((function t(){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.selectedPaymentMethod){t.next=3;break}return this.$message.warning("请选择支付方式"),t.abrupt("return");case 3:if(this.orderInfo){t.next=6;break}return this.$message.error("订单信息错误"),t.abrupt("return");case 6:if(t.prev=6,this.paymentLoading=!0,"balance"!==this.selectedPaymentMethod){t.next=13;break}return t.next=11,this.handleBalancePayment();case 11:t.next=16;break;case 13:if("alipay"!==this.selectedPaymentMethod){t.next=16;break}return t.next=16,this.handleAlipayPayment();case 16:t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](6),t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$message.error(t.t0.response.data.message):this.$message.error("支付失败，请重试");case 22:return t.prev=22,this.paymentLoading=!1,t.finish(22);case 25:case"end":return t.stop()}}),t,this,[[6,18,22,25]])})));function e(){return t.apply(this,arguments)}return e}(),handlePayment:function(){var t=c(n.a.mark((function t(e){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.orderInfo){t.next=3;break}return this.$message.error("订单信息错误"),t.abrupt("return");case 3:if(t.prev=3,this.paymentLoading=!0,"balance"!==e){t.next=10;break}return t.next=8,this.handleBalancePayment();case 8:t.next=13;break;case 10:if("alipay"!==e){t.next=13;break}return t.next=13,this.handleAlipayPayment();case 13:t.next=19;break;case 15:t.prev=15,t.t0=t["catch"](3),t.t0.response&&t.t0.response.data&&t.t0.response.data.message?this.$message.error(t.t0.response.data.message):this.$message.error("支付失败，请重试");case 19:return t.prev=19,this.paymentLoading=!1,t.finish(19);case 22:case"end":return t.stop()}}),t,this,[[3,15,19,22]])})));function e(e){return t.apply(this,arguments)}return e}(),handleBalancePayment:function(){var t=c(n.a.mark((function t(){var e,a;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e={purchaseId:this.orderInfo.purchaseId,agentId:this.orderInfo.agentId,agentName:this.orderInfo.agentName,purchasePrice:this.orderInfo.purchasePrice},t.next=4,this.$http.post("/api/agent/market/purchase/balance-pay",e);case 4:if(a=t.sent,!a.success){t.next=12;break}this.$message.success("余额支付成功！您现在可以使用该智能体了"),this.$emit("purchase-success",this.agentDetail.id),this.showPaymentModal=!1,this.handleClose(),t.next=13;break;case 12:throw new Error(a.message||"余额支付失败");case 13:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleAlipayPayment:function(){var t=c(n.a.mark((function t(){var e,a,r,s,i;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e={purchaseId:this.orderInfo.purchaseId,agentId:this.orderInfo.agentId,agentName:this.orderInfo.agentName,purchasePrice:this.orderInfo.purchasePrice},t.next=4,this.$http.post("/api/agent/market/purchase/alipay",e);case 4:if(a=t.sent,!a.success){t.next=18;break}if(r=a.result.payForm,r){t.next=10;break}return this.$message.error("支付表单为空"),t.abrupt("return");case 10:s=document.createElement("div"),s.innerHTML=r,document.body.appendChild(s),i=s.querySelector("form"),i?(i.submit(),this.showPaymentModal=!1):this.$message.error("支付表单创建失败"),setTimeout((function(){document.body.contains(s)&&document.body.removeChild(s)}),1e3),t.next=19;break;case 18:throw new Error(a.message||"支付宝支付失败");case 19:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleViewDetail:function(){var t=this;if(this.isPurchased||this.agentDetail.isFree){var e=this.agentId;this.$emit("update:visible",!1),this.$nextTick((function(){t.$router.push({name:"AgentDetailPage",query:{agentId:e}}).catch((function(t){t.name}))}))}else this.$message.warning("请先购买该智能体后查看详情")},handleExperience:function(){if(this.agentDetail.experienceLink)try{window.open(this.agentDetail.experienceLink,"_blank"),this.$message.success("正在打开体验页面...")}catch(t){this.$message.error("打开体验页面失败")}else this.$message.warning("该智能体暂未提供体验链接")},handleDownloadTip:function(){this.$message.warning("请先购买智能体后再下载工作流")},handleWorkflowDownload:function(){var t=c(n.a.mark((function t(e){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isPurchased||this.agentDetail.isFree){t.next=4;break}return this.$message.warning("请先购买该智能体后再下载工作流"),t.abrupt("return");case 4:return this.$set(this.downloadLoading,e.id,!0),t.prev=5,t.next=8,this.navigateToDownloadPage(e);case 8:t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](5),this.$message.error("下载失败，请稍后重试");case 14:return t.prev=14,this.$set(this.downloadLoading,e.id,!1),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[5,10,14,17]])})));function e(e){return t.apply(this,arguments)}return e}(),navigateToDownloadPage:function(){var t=c(n.a.mark((function t(e){return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,new Promise((function(t){return setTimeout(t,800)}));case 3:"/workflow/download/".concat(e.id,"?agentId=").concat(this.agentDetail.id),this.$router.push({path:"/workflow/download",query:{workflowId:e.id,agentId:this.agentDetail.id,workflowName:e.workflowName}}),this.$message.success("正在跳转到下载页面..."),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](0),this.$message.error("跳转失败，请稍后重试");case 13:case"end":return t.stop()}}),t,this,[[0,9]])})));function e(e){return t.apply(this,arguments)}return e}(),handleVideoLoadStart:function(){this.videoLoading=!0,this.videoError=!1},handleVideoLoaded:function(){this.videoLoading=!1,this.videoError=!1},handleVideoError:function(t){this.videoLoading=!1,this.videoError=!0,this.videoPlaying=!1,this.$message.error("视频加载失败")},retryVideoLoad:function(){this.videoError=!1,this.videoLoading=!0;var t=this.$refs.demoVideo;t&&t.load()},handleImageError:function(t){t.target.style.display="none"},handleCreatorAvatarError:function(t){t.target.style.display="none"},handleWorkflowImageError:function(t){t.target.style.display="none"},loadUserBalance:function(){var t=c(n.a.mark((function t(){var e;return n.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/api/usercenter/overview");case 3:e=t.sent,e.success&&(this.userBalance=e.result.accountBalance||0),t.next=11;break;case 7:t.prev=7,t.t0=t["catch"](0),this.userBalance=0;case 11:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}()}},u=l,d=(a("b234"),a("2877")),h=Object(d["a"])(u,r,s,!1,null,"1faa6ada",null);e["default"]=h.exports},adc7:function(t,e,a){},b234:function(t,e,a){"use strict";var r=a("2b95"),s=a.n(r);s.a},b339:function(t,e,a){"use strict";var r=a("5e26"),s=a.n(r);s.a},bb00:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"revenue-ranking"},[t.data.agentRevenueList&&t.data.agentRevenueList.length>0?a("div",{staticClass:"ranking-container"},[a("div",{staticClass:"ranking-header"},[a("h3",{staticClass:"ranking-title"},[a("a-icon",{attrs:{type:"bar-chart"}}),t._v("\n        智能体收益排行\n      ")],1),a("div",{staticClass:"ranking-controls"},[a("div",{staticClass:"sort-controls"},[a("span",{staticClass:"sort-label"},[t._v("排序：")]),a("a-select",{staticStyle:{width:"100px","margin-right":"8px"},attrs:{size:"small"},on:{change:t.handleSortChange},model:{value:t.sortField,callback:function(e){t.sortField=e},expression:"sortField"}},[a("a-select-option",{attrs:{value:"totalRevenue"}},[t._v("总收益")]),a("a-select-option",{attrs:{value:"salesCount"}},[t._v("销售次数")])],1),a("a-button",{staticClass:"sort-order-btn",attrs:{size:"small",icon:"desc"===t.sortOrder?"sort-descending":"sort-ascending",title:"desc"===t.sortOrder?"降序":"升序"},on:{click:t.toggleSortOrder}},[t._v("\n            "+t._s("desc"===t.sortOrder?"降序":"升序")+"\n          ")])],1),a("div",{staticClass:"ranking-actions"},[a("a-button",{attrs:{type:"link",size:"small"},on:{click:t.toggleRankingExpanded}},[t._v("\n            "+t._s(t.rankingExpanded?"收起":"展开全部")+"\n            "),a("a-icon",{attrs:{type:t.rankingExpanded?"up":"down"}})],1)],1)])]),a("div",{staticClass:"ranking-list"},t._l(t.displayRankingList,(function(e,r){return a("div",{key:e.agentId,staticClass:"ranking-item",class:{"top-three":r<3}},[a("div",{staticClass:"ranking-number"},[a("span",{staticClass:"rank-badge",class:"rank-"+(r+1)},[t._v(t._s(r+1))])]),a("div",{staticClass:"agent-info"},[a("div",{staticClass:"agent-avatar"},[e.agentAvatar?a("img",{attrs:{src:e.agentAvatar,alt:e.agentName},on:{error:t.handleImageError}}):a("div",{staticClass:"avatar-placeholder"},[a("a-icon",{attrs:{type:"robot"}})],1)]),a("div",{staticClass:"agent-details"},[a("div",{staticClass:"agent-name"},[t._v(t._s(e.agentName))]),a("div",{staticClass:"agent-meta"},[a("a-tag",{attrs:{color:t.getStatusColor(e.auditStatus),size:"small"}},[t._v("\n                "+t._s(e.auditStatusText)+"\n              ")]),a("span",{staticClass:"workflow-count"},[t._v(t._s(e.workflowCount||0)+" 个工作流")])],1)])]),a("div",{staticClass:"revenue-info"},[a("div",{staticClass:"total-revenue"},[a("span",{staticClass:"revenue-value"},[t._v("¥"+t._s(t.formatMoney(e.totalRevenue)))]),a("span",{staticClass:"revenue-label"},[t._v("总收益")])])]),a("div",{staticClass:"sales-info"},[a("div",{staticClass:"sales-count"},[t._v(t._s(e.salesCount||0))]),a("div",{staticClass:"sales-label"},[t._v("销售次数")])])])})),0)]):t.loading?t._e():a("div",{staticClass:"empty-revenue"},[a("a-empty",{attrs:{description:"暂无收益数据",image:t.emptyImage}},[a("p",[t._v("创建并发布智能体后，这里将显示收益统计信息")])])],1)])},s=[],i=a("fc25");function n(t){return u(t)||l(t)||c(t)||o()}function o(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(t,e){if(t){if("string"===typeof t)return d(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function l(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function u(t){if(Array.isArray(t))return d(t)}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,r=new Array(e);a<e;a++)r[a]=t[a];return r}var h={name:"RevenueRanking",props:{loading:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},data:function(){return{rankingExpanded:!1,emptyImage:i["a"].PRESENTED_IMAGE_SIMPLE,sortField:"totalRevenue",sortOrder:"desc"}},computed:{displayRankingList:function(){var t=this;if(!this.data.agentRevenueList)return[];var e=n(this.data.agentRevenueList).sort((function(e,a){var r,s;"totalRevenue"===t.sortField?(r=Number(e.totalRevenue)||0,s=Number(a.totalRevenue)||0):"salesCount"===t.sortField&&(r=Number(e.salesCount)||0,s=Number(a.salesCount)||0);var i=r-s;return"desc"===t.sortOrder?-i:i}));return this.rankingExpanded?e:e.slice(0,5)}},methods:{formatMoney:function(t){return t?Number(t).toFixed(2):"0.00"},getStatusColor:function(t){var e={1:"orange",2:"green",3:"red"};return e[t]||"default"},handleImageError:function(t){t.target.style.display="none",t.target.nextElementSibling.style.display="flex"},toggleRankingExpanded:function(){this.rankingExpanded=!this.rankingExpanded},handleSortChange:function(){},toggleSortOrder:function(){this.sortOrder="desc"===this.sortOrder?"asc":"desc"}}},f=h,p=(a("1d5d"),a("2877")),v=Object(p["a"])(f,r,s,!1,null,"134325b1",null);e["default"]=v.exports},cc1b:function(t,e,a){"use strict";var r=a("ed1f"),s=a.n(r);s.a},de2d:function(t,e,a){"use strict";var r=a("eb07"),s=a.n(r);s.a},eb07:function(t,e,a){},ec44a:function(t,e,a){"use strict";var r=a("490a"),s=a.n(r);s.a},ed1f:function(t,e,a){},fd93:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"agent-card",on:{click:t.handleCardClick}},[!t.agent.isPurchased&&t.agent.showSvipPromo&&"1"===t.agent.authorType?a("div",{staticClass:"svip-promo-tag svip-free"},[t._v("\n    SVIP免费\n  ")]):t._e(),!t.agent.isPurchased&&t.agent.showSvipPromo&&"2"===t.agent.authorType?a("div",{staticClass:"svip-promo-tag svip-discount"},[t._v("\n    SVIP 5折\n  ")]):t._e(),a("div",{staticClass:"agent-cover"},[a("div",{staticClass:"cover-image"},[t.agent.demoVideo?a("video",{staticClass:"cover-video",attrs:{src:t.agent.demoVideo,muted:"",loop:"",preload:"metadata"},domProps:{muted:!0},on:{mouseenter:t.handleVideoHover,mouseleave:t.handleVideoLeave,loadedmetadata:t.handleVideoLoaded}}):t.agent.agentAvatar?a("img",{staticClass:"cover-image-img",attrs:{src:t.agent.agentAvatar,alt:t.agent.agentName},on:{error:t.handleImageError}}):a("div",{staticClass:"cover-placeholder"},[a("a-icon",{attrs:{type:"robot"}})],1)]),a("div",{staticClass:"author-type-tag",class:t.authorTypeClass},[a("a-icon",{attrs:{type:t.authorTypeIcon}}),a("span",[t._v(t._s(t.authorTypeText))])],1),t.agent.isFree?a("div",{staticClass:"svip-free-tag"},[a("a-icon",{attrs:{type:"crown"}}),a("span",[t._v("SVIP 免费")])],1):t.agent.isPurchased?a("div",{staticClass:"purchased-right-tag"},[a("a-icon",{attrs:{type:"check-circle"}}),a("span",[t._v("已购买")])],1):!t.agent.isPurchased&&t.agent.showDiscountPrice&&70===t.agent.discountRate?a("div",{staticClass:"vip-discount-tag"},[a("a-icon",{attrs:{type:"crown"}}),a("span",[t._v("VIP 7折")])],1):!t.agent.isPurchased&&t.agent.showDiscountPrice&&50===t.agent.discountRate?a("div",{staticClass:"svip-discount-tag"},[a("a-icon",{attrs:{type:"crown"}}),a("span",[t._v("SVIP 5折")])],1):t._e()]),a("div",{staticClass:"agent-info"},[a("div",{staticClass:"agent-header"},[a("h4",{staticClass:"agent-name",attrs:{title:t.agent.agentName}},[t._v("\n        "+t._s(t.agent.agentName)+"\n      ")]),a("div",{staticClass:"agent-price"},[t.agent.isFree?a("div",{staticClass:"price-container"},[a("span",{staticClass:"free-price"},[t._v("免费")])]):t.agent.isPurchased?a("div",{staticClass:"price-container"},[a("span",{staticClass:"purchased-price"},[t._v("已购买")])]):t.agent.showDiscountPrice?a("div",{staticClass:"price-container"},[a("span",{staticClass:"discount-price"},[t._v("¥"+t._s(t.agent.discountPrice||0))]),a("span",{staticClass:"original-price"},[t._v("¥"+t._s(t.agent.originalPrice||0))])]):a("div",{staticClass:"price-container"},[a("span",{staticClass:"current-price"},[t._v("¥"+t._s(t.agent.originalPrice||0))])])])]),t.agent.description?a("div",{staticClass:"agent-description"},[t._v("\n      "+t._s(t.agent.description)+"\n    ")]):t._e(),a("div",{staticClass:"agent-meta"},[a("span",{staticClass:"creator-info"},[a("div",{staticClass:"creator-avatar"},[t.creatorAvatar?a("img",{attrs:{src:t.creatorAvatar,alt:t.creatorName},on:{error:t.handleCreatorAvatarError}}):a("a-icon",{attrs:{type:"user"}})],1),a("span",{staticClass:"creator-name"},[t._v(t._s(t.creatorName))])]),a("span",{staticClass:"workflow-count"},[a("a-icon",{attrs:{type:"deployment-unit"}}),t._v("\n        "+t._s(t.workflowCount)+"个工作流\n      ")],1)])])])},s=[],i={name:"AgentCard",props:{agent:{type:Object,required:!0}},computed:{authorTypeClass:function(){return{official:"1"===this.agent.authorType,creator:"2"===this.agent.authorType}},authorTypeIcon:function(){return"1"===this.agent.authorType?"crown":"user"},authorTypeText:function(){return"1"===this.agent.authorType?"官方":"创作者"},priceTagClass:function(){return{free:this.agent.isFree,discount:this.agent.hasDiscount&&!this.agent.isFree,normal:!this.agent.hasDiscount&&!this.agent.isFree}},creatorName:function(){return this.agent.creatorName||this.agent.createBy||"未知创作者"},creatorAvatar:function(){return this.agent.creatorAvatar||""},workflowCount:function(){return this.agent.workflowCount||0}},methods:{handleCardClick:function(){this.$emit("view-detail",this.agent)},handleViewDetail:function(){this.$emit("view-detail",this.agent)},handleImageError:function(t){t.target.style.display="none"},handleCreatorAvatarError:function(t){t.target.style.display="none"},handleVideoHover:function(t){t.target.play().catch((function(t){}))},handleVideoLeave:function(t){t.target.pause(),t.target.currentTime=0},handleVideoLoaded:function(t){t.target.currentTime=0}}},n=i,o=(a("3261"),a("2877")),c=Object(o["a"])(n,r,s,!1,null,"3c6ce545",null);e["default"]=c.exports}}]);