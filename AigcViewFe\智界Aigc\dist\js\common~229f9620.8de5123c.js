(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~229f9620"],{"036f":function(t,e,a){},"0518":function(t,e,a){"use strict";var i=a("bf2c"),s=a.n(i);s.a},"0586":function(t,e,a){"use strict";var i=a("d932"),s=a.n(i);s.a},"06ff":function(t,e,a){},"0791":function(t,e,a){},"0932":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"category-filter"},[a("div",{staticClass:"category-tabs"},[a("div",{staticClass:"container"},[a("a-tag",{staticClass:"category-tag",class:{active:!t.selectedCategory},attrs:{color:t.selectedCategory?"default":"blue"},on:{click:function(e){return t.handleCategoryChange("")}}},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n        全部插件\n        "),t.totalCount>0?a("span",{staticClass:"count"},[t._v("("+t._s(t.totalCount)+")")]):t._e()],1),t._l(t.categories,(function(e){return a("a-tag",{key:e.value,staticClass:"category-tag",class:{active:t.selectedCategory===e.value},attrs:{color:t.selectedCategory===e.value?"blue":"default"},on:{click:function(a){return t.handleCategoryChange(e.value)}}},[a("a-icon",{attrs:{type:t.getCategoryIcon(e.value)}}),t._v("\n        "+t._s(e.text)+"\n        "),t.categoryCounts[e.value]>0?a("span",{staticClass:"count"},[t._v("\n          ("+t._s(t.categoryCounts[e.value])+")\n        ")]):t._e()],1)}))],2)]),t.loading?a("div",{staticClass:"loading-state"},[a("a-spin",{attrs:{size:"small"}}),a("span",[t._v("加载分类中...")])],1):t._e(),t.error?a("div",{staticClass:"error-state"},[a("a-alert",{attrs:{message:"分类加载失败",description:t.error,type:"error","show-icon":"",closable:""},on:{close:t.clearError}})],1):t._e()])},s=[],n=a("a34a"),r=a.n(n),o=a("4124"),c=a("8b05");function l(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){l(n,i,s,r,o,"next",t)}function o(t){l(n,i,s,r,o,"throw",t)}r(void 0)}))}}var d={name:"CategoryFilter",props:{categoryCounts:{type:Object,default:function(){return{}}},totalCount:{type:Number,default:0}},data:function(){return{categories:[],selectedCategory:"",loading:!1,error:null}},created:function(){var t=u(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.fetchCategories();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{fetchCategories:function(){var t=u(r.a.mark((function t(){var e,a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,this.error=null,t.prev=2,t.next=6,o["a"].getPluginCategories();case 6:if(e=t.sent,!e.success){t.next=14;break}a=e.result||[],this.categories=Object(c["b"])(a),Object(c["i"])(this.categories),t.next=15;break;case 14:throw new Error(e.message||"获取分类数据失败");case 15:t.next=21;break;case 17:t.prev=17,t.t0=t["catch"](2),this.error=t.t0.message||"网络请求失败";case 21:return t.prev=21,this.loading=!1,t.finish(21);case 24:case"end":return t.stop()}}),t,this,[[2,17,21,24]])})));function e(){return t.apply(this,arguments)}return e}(),handleCategoryChange:function(t){this.selectedCategory=t,this.$emit("category-change",{category:t,categoryText:this.getCategoryText(t)})},getCategoryText:function(t){if(!t)return"全部插件";var e=this.categories.find((function(e){return e.value===t}));return e?e.text:t},getCategoryIcon:function(t){var e={content_generation:"edit",social_media:"share-alt",marketing_tools:"rocket",productivity:"tool",design_creative:"bg-colors",data_analysis:"bar-chart",ai_assistant:"robot",image_processing:"picture",text_processing:"file-text",video_audio:"video-camera"};return e[t]||"tags"},clearError:function(){this.error=null},reloadCategories:function(){var t=u(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.fetchCategories();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetCategory:function(){this.handleCategoryChange("")}}},p=d,g=(a("f474"),a("2877")),h=Object(g["a"])(p,i,s,!1,null,"d622f89e",null);e["default"]=h.exports},"0a37":function(t,e,a){"use strict";var i=a("e2fe"),s=a.n(i);s.a},"11c4":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"plugin-technical"},[a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"api"}}),t._v("\n      API接口说明\n    ")],1),a("div",{staticClass:"api-info"},[a("a-descriptions",{attrs:{column:1,bordered:""}},[a("a-descriptions-item",{attrs:{label:"插件标识"}},[a("a-tag",{attrs:{color:"blue"}},[t._v(t._s(t.pluginKey||"未设置"))])],1),a("a-descriptions-item",{attrs:{label:"调用方式"}},[a("code",[t._v("POST /api/aigc/verify-apikey")])]),a("a-descriptions-item",{attrs:{label:"认证方式"}},[t._v("\n          API Key 认证\n        ")]),a("a-descriptions-item",{attrs:{label:"响应格式"}},[t._v("\n          JSON\n        ")]),a("a-descriptions-item",{attrs:{label:"请求限制"}},[t._v("\n          "+t._s(t.pluginDetail.neednum||0)+" 点数/次\n        ")])],1)],1)]),a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"setting"}}),t._v("\n      请求参数\n    ")],1),a("a-table",{attrs:{columns:t.paramColumns,"data-source":t.requestParams,pagination:!1,size:"middle"},scopedSlots:t._u([{key:"type",fn:function(e){return[a("a-tag",{attrs:{color:t.getTypeColor(e)}},[t._v(t._s(e))])]}},{key:"required",fn:function(e){return[a("a-tag",{attrs:{color:e?"red":"default"}},[t._v("\n          "+t._s(e?"必填":"可选")+"\n        ")])]}}])})],1),a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"code"}}),t._v("\n      响应示例\n    ")],1),a("a-tabs",{attrs:{type:"card"}},[a("a-tab-pane",{key:"success",attrs:{tab:"成功响应"}},[a("pre",{staticClass:"code-block"},[t._v(t._s(t.successResponse))])]),a("a-tab-pane",{key:"error",attrs:{tab:"错误响应"}},[a("pre",{staticClass:"code-block"},[t._v(t._s(t.errorResponse))])])],1)],1),a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"exclamation-circle"}}),t._v("\n      错误码说明\n    ")],1),a("a-table",{attrs:{columns:t.errorColumns,"data-source":t.errorCodes,pagination:!1,size:"middle"},scopedSlots:t._u([{key:"code",fn:function(e){return[a("code",{staticClass:"error-code"},[t._v(t._s(e))])]}}])})],1),a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"file-text"}}),t._v("\n      SDK示例\n    ")],1),a("a-tabs",{attrs:{type:"card"}},[a("a-tab-pane",{key:"javascript",attrs:{tab:"JavaScript"}},[a("pre",{staticClass:"code-block"},[t._v(t._s(t.javascriptExample))])]),a("a-tab-pane",{key:"python",attrs:{tab:"Python"}},[a("pre",{staticClass:"code-block"},[t._v(t._s(t.pythonExample))])]),a("a-tab-pane",{key:"curl",attrs:{tab:"cURL"}},[a("pre",{staticClass:"code-block"},[t._v(t._s(t.curlExample))])])],1)],1),a("div",{staticClass:"technical-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"tool"}}),t._v("\n      技术规格\n    ")],1),t._m(0)])])},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"specs-grid"},[a("div",{staticClass:"spec-card"},[a("h4",[t._v("性能指标")]),a("ul",[a("li",[t._v("响应时间: < 3秒")]),a("li",[t._v("并发支持: 1000+ QPS")]),a("li",[t._v("可用性: 99.9%")]),a("li",[t._v("数据传输: HTTPS加密")])])]),a("div",{staticClass:"spec-card"},[a("h4",[t._v("兼容性")]),a("ul",[a("li",[t._v("支持所有现代浏览器")]),a("li",[t._v("移动端友好")]),a("li",[t._v("RESTful API标准")]),a("li",[t._v("JSON数据格式")])])]),a("div",{staticClass:"spec-card"},[a("h4",[t._v("安全保障")]),a("ul",[a("li",[t._v("API Key认证")]),a("li",[t._v("请求签名验证")]),a("li",[t._v("数据加密传输")]),a("li",[t._v("访问频率限制")])])])])}],n={name:"PluginTechnical",props:{pluginDetail:{type:Object,default:function(){return{}}},pluginKey:{type:String,default:""}},data:function(){return{paramColumns:[{title:"参数名",dataIndex:"name",key:"name",width:120},{title:"类型",dataIndex:"type",key:"type",width:80,scopedSlots:{customRender:"type"}},{title:"必填",dataIndex:"required",key:"required",width:80,scopedSlots:{customRender:"required"}},{title:"说明",dataIndex:"description",key:"description"}],requestParams:[{name:"apiKey",type:"string",required:!0,description:"用户的API密钥，用于身份验证"},{name:"pluginName",type:"string",required:!0,description:"插件名称，用于标识调用的插件"},{name:"inputData",type:"object",required:!0,description:"插件输入数据，具体格式根据插件而定"},{name:"options",type:"object",required:!1,description:"可选配置参数，如输出格式、质量等"}],errorColumns:[{title:"错误码",dataIndex:"code",key:"code",width:100,scopedSlots:{customRender:"code"}},{title:"错误信息",dataIndex:"message",key:"message",width:200},{title:"解决方案",dataIndex:"solution",key:"solution"}],errorCodes:[{code:"400",message:"请求参数错误",solution:"检查请求参数格式和必填字段"},{code:"401",message:"API Key无效",solution:"检查API Key是否正确或已过期"},{code:"402",message:"余额不足",solution:"请充值后重试"},{code:"403",message:"访问被拒绝",solution:"检查API Key权限或联系客服"},{code:"429",message:"请求频率超限",solution:"降低请求频率或升级套餐"},{code:"500",message:"服务器内部错误",solution:"稍后重试或联系技术支持"}]}},computed:{successResponse:function(){return JSON.stringify({success:!0,code:200,message:"处理成功",result:{taskId:"task_123456789",status:"completed",output:{content:"生成的内容...",format:"text",timestamp:"2024-06-24T10:30:00Z"},usage:{consumed:this.pluginDetail.neednum||5,remaining:95}}},null,2)},errorResponse:function(){return JSON.stringify({success:!1,code:402,message:"余额不足",error:{type:"INSUFFICIENT_BALANCE",details:"当前余额: 2.5 点数, 需要: 5.0 点数"}},null,2)},javascriptExample:function(){return"// 使用 fetch API 调用插件\nconst callPlugin = async () => {\n  try {\n    const response = await fetch('/api/aigc/verify-apikey', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        apiKey: 'your-api-key',\n        pluginName: '".concat(this.pluginDetail.plubname||"插件名称","',\n        inputData: {\n          // 输入数据\n          text: '要处理的内容'\n        },\n        options: {\n          format: 'json'\n        }\n      })\n    });\n\n    const result = await response.json();\n    console.log('插件调用结果:', result);\n  } catch (error) {\n    console.error('调用失败:', error);\n  }\n};\n\ncallPlugin();")},pythonExample:function(){return'import requests\nimport json\n\ndef call_plugin():\n    url = "https://your-domain.com/api/aigc/verify-apikey"\n\n    payload = {\n        "apiKey": "your-api-key",\n        "pluginName": "'.concat(this.pluginDetail.plubname||"插件名称",'",\n        "inputData": {\n            "text": "要处理的内容"\n        },\n        "options": {\n            "format": "json"\n        }\n    }\n\n    headers = {\n        "Content-Type": "application/json"\n    }\n\n    try:\n        response = requests.post(url, json=payload, headers=headers)\n        result = response.json()\n        print("插件调用结果:", json.dumps(result, indent=2, ensure_ascii=False))\n    except Exception as e:\n        print("调用失败:", str(e))\n\ncall_plugin()')},curlExample:function(){return'curl -X POST "https://your-domain.com/api/aigc/verify-apikey" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "apiKey": "your-api-key",\n    "pluginName": "'.concat(this.pluginDetail.plubname||"插件名称",'",\n    "inputData": {\n      "text": "要处理的内容"\n    },\n    "options": {\n      "format": "json"\n    }\n  }\'')}},methods:{getTypeColor:function(t){var e={string:"blue",number:"green",object:"orange",array:"purple",boolean:"cyan"};return e[t]||"default"}}},r=n,o=(a("5fb2"),a("2877")),c=Object(o["a"])(r,i,s,!1,null,"05125f97",null);e["default"]=c.exports},1749:function(t,e,a){},"1a7f":function(t,e,a){"use strict";var i=a("0791"),s=a.n(i);s.a},2649:function(t,e,a){},2652:function(t,e,a){},"29b6":function(t,e,a){},"2a86":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"plugin-features"},[a("div",{staticClass:"features-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"environment"}}),t._v("\n      适用场景\n    ")],1),a("div",{staticClass:"scenarios-container"},[a("div",{staticClass:"scenarios-tags"},t._l(t.scenariosList,(function(e,i){return a("a-tag",{key:i,staticClass:"scenario-tag",attrs:{color:t.getScenarioColor(i),size:"large"}},[a("a-icon",{attrs:{type:t.getScenarioIcon(e)}}),t._v("\n          "+t._s(e)+"\n        ")],1)})),1),t.scenariosList.length>0?a("div",{staticClass:"scenarios-description"},[a("p",[t._v("此插件特别适用于以上场景，能够有效提升工作效率和输出质量。")])]):t._e()])]),a("div",{staticClass:"features-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"star"}}),t._v("\n      核心功能\n    ")],1),a("div",{staticClass:"features-grid"},t._l(t.coreFeatures,(function(e,i){return a("div",{key:i,staticClass:"feature-card"},[a("div",{staticClass:"feature-icon"},[a("a-icon",{attrs:{type:e.icon}})],1),a("div",{staticClass:"feature-content"},[a("h4",{staticClass:"feature-title"},[t._v(t._s(e.title))]),a("p",{staticClass:"feature-description"},[t._v(t._s(e.description))])])])})),0)]),a("div",{staticClass:"features-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"rocket"}}),t._v("\n      技术优势\n    ")],1),a("div",{staticClass:"advantages-list"},t._l(t.techAdvantages,(function(e,i){return a("div",{key:i,staticClass:"advantage-item"},[a("div",{staticClass:"advantage-header"},[a("a-icon",{staticClass:"advantage-icon",attrs:{type:e.icon}}),a("span",{staticClass:"advantage-title"},[t._v(t._s(e.title))])],1),a("p",{staticClass:"advantage-description"},[t._v(t._s(e.description))]),e.metrics?a("div",{staticClass:"advantage-metrics"},t._l(e.metrics,(function(e,i){return a("a-tag",{key:i,staticClass:"metric-tag",attrs:{color:e.color}},[t._v("\n            "+t._s(e.label)+": "+t._s(e.value)+"\n          ")])})),1):t._e()])})),0)]),a("div",{staticClass:"features-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"deployment-unit"}}),t._v("\n      应用案例\n    ")],1),a("div",{staticClass:"use-cases"},t._l(t.useCases,(function(e,i){return a("a-card",{key:i,staticClass:"use-case-card",attrs:{title:e.title},scopedSlots:t._u([{key:"extra",fn:function(){return[a("a-tag",{attrs:{color:e.difficulty.color}},[t._v("\n            "+t._s(e.difficulty.label)+"\n          ")])]},proxy:!0}],null,!0)},[a("p",{staticClass:"use-case-description"},[t._v(t._s(e.description))]),a("div",{staticClass:"use-case-steps"},[a("h5",[t._v("操作步骤：")]),a("ol",t._l(e.steps,(function(e,i){return a("li",{key:i},[t._v("\n              "+t._s(e)+"\n            ")])})),0)]),a("div",{staticClass:"use-case-result"},[a("a-icon",{staticClass:"result-icon",attrs:{type:"check-circle"}}),a("span",[t._v("预期效果："+t._s(e.result))])],1)])})),1)]),t.showEffectDemo?a("div",{staticClass:"features-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"picture"}}),t._v("\n      效果展示\n    ")],1),a("div",{staticClass:"effect-demo"},[a("a-carousel",{attrs:{autoplay:""}},t._l(t.effectDemos,(function(e,i){return a("div",{key:i,staticClass:"demo-slide"},[a("img",{staticClass:"demo-image",attrs:{src:e.image,alt:e.title}}),a("div",{staticClass:"demo-overlay"},[a("h4",{staticClass:"demo-title"},[t._v(t._s(e.title))]),a("p",{staticClass:"demo-description"},[t._v(t._s(e.description))])])])})),0)],1)]):t._e()])},s=[],n={name:"PluginFeatures",props:{pluginDetail:{type:Object,default:function(){return{}}},category:{type:String,default:""}},computed:{scenariosList:function(){return this.pluginDetail.scenarios_dictText?this.pluginDetail.scenarios_dictText.split(",").map((function(t){return t.trim()})):["内容创作","办公效率"]},coreFeatures:function(){var t={"ai-chat":[{icon:"message",title:"智能对话",description:"基于先进AI模型，提供自然流畅的对话体验"},{icon:"bulb",title:"创意生成",description:"快速生成创意内容，激发无限灵感"},{icon:"setting",title:"个性定制",description:"支持多种参数调节，满足个性化需求"}],"ai-image":[{icon:"picture",title:"图像生成",description:"根据文字描述生成高质量图像"},{icon:"bg-colors",title:"风格多样",description:"支持多种艺术风格和画面效果"},{icon:"expand",title:"高清输出",description:"生成高分辨率、细节丰富的图像"}],default:[{icon:"thunderbolt",title:"高效处理",description:"快速响应，高效完成各类任务"},{icon:"safety",title:"稳定可靠",description:"经过充分测试，确保稳定运行"},{icon:"crown",title:"专业品质",description:"专业级输出质量，满足商用需求"}]};return t[this.category]||t["default"]},techAdvantages:function(){return[{icon:"dashboard",title:"高性能处理",description:"采用优化算法，处理速度提升300%，响应时间控制在秒级",metrics:[{label:"处理速度",value:"< 3秒",color:"green"},{label:"成功率",value:"99.5%",color:"blue"},{label:"并发支持",value:"1000+",color:"orange"}]},{icon:"shield",title:"数据安全",description:"采用端到端加密，确保用户数据安全，符合GDPR等隐私保护标准",metrics:[{label:"加密级别",value:"AES-256",color:"red"}]},{icon:"api",title:"易于集成",description:"提供标准API接口，支持多种编程语言，快速集成到现有系统",metrics:[{label:"API响应",value:"< 100ms",color:"cyan"},{label:"文档完整度",value:"100%",color:"geekblue"}]}]},useCases:function(){return[{title:"内容创作场景",description:"帮助创作者快速生成高质量内容，提升创作效率",difficulty:{label:"简单",color:"green"},steps:["观看插件视频教程，了解功能特点","跟随教程学习正确的操作方法","按照教程步骤进行实际操作","参考教程技巧优化使用效果"],result:"掌握插件使用技巧，获得高质量创作内容"},{title:"商业应用场景",description:"为企业提供专业的AI解决方案，提升业务效率",difficulty:{label:"中等",color:"orange"},steps:["详细观看教程，理解业务应用方法","学习教程中的高级配置技巧","根据教程指导进行批量操作","运用教程经验优化工作流程"],result:"熟练掌握插件应用，实现业务流程自动化"}]},showEffectDemo:function(){return["ai-image","ai-video"].includes(this.category)},effectDemos:function(){return[{image:"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg",title:"效果示例 1",description:"展示插件的实际处理效果"},{image:"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg",title:"效果示例 2",description:"不同参数下的输出结果"}]}},methods:{getScenarioColor:function(t){var e=["blue","green","orange","purple","cyan","red","geekblue","magenta","volcano","gold"];return e[t%e.length]},getScenarioIcon:function(t){var e={"内容创作":"edit","办公效率":"laptop","教育培训":"book","电商营销":"shopping","娱乐游戏":"smile","社交媒体":"share-alt","数据分析":"bar-chart","设计创意":"bg-colors","客户服务":"customer-service","项目管理":"project","技术开发":"code","财务管理":"dollar","人力资源":"team","医疗健康":"heart","法律咨询":"safety","房产中介":"home","旅游出行":"car","生活服务":"tool"};return e[t]||"tag"}}},r=n,o=(a("76f3"),a("2877")),c=Object(o["a"])(r,i,s,!1,null,"00c5adf6",null);e["default"]=c.exports},"31ef":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"signin-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("签到奖励")]),a("p",{staticClass:"simple-subtitle"},[t._v("每日签到获取积分奖励，兑换更多精彩内容")])]),a("section",{staticClass:"signin-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"signin-card"},[a("div",{staticClass:"signin-status"},[a("div",{staticClass:"status-icon"},[a("a-icon",{attrs:{type:"calendar"}})],1),a("h2",[t._v("每日签到")]),a("p",[t._v("连续签到"+t._s(t.consecutiveDays)+"天")]),a("button",{staticClass:"btn-signin",attrs:{disabled:t.hasSignedToday},on:{click:t.handleSignIn}},[t._v("\n              "+t._s(t.hasSignedToday?"今日已签到":"立即签到")+"\n            ")])]),a("div",{staticClass:"rewards-grid"},t._l(t.rewards,(function(e,i){return a("div",{key:i,staticClass:"reward-item",class:{completed:i<t.consecutiveDays}},[a("div",{staticClass:"reward-day"},[t._v("第"+t._s(i+1)+"天")]),a("div",{staticClass:"reward-icon"},[a("a-icon",{attrs:{type:e.icon}})],1),a("div",{staticClass:"reward-text"},[t._v(t._s(e.text))])])})),0)])])])])])},s=[],n=a("df7c"),r={name:"SignIn",components:{WebsitePage:n["default"]},data:function(){return{hasSignedToday:!1,consecutiveDays:3,rewards:[{icon:"gift",text:"10积分"},{icon:"star",text:"15积分"},{icon:"trophy",text:"20积分"},{icon:"crown",text:"30积分"},{icon:"diamond",text:"50积分"},{icon:"fire",text:"100积分"},{icon:"thunderbolt",text:"会员体验券"}]}},methods:{handleSignIn:function(){this.hasSignedToday||(this.hasSignedToday=!0,this.consecutiveDays++,this.$notification.success({message:"签到成功",description:"恭喜您！今日签到成功，获得积分奖励！",placement:"topRight",duration:4,style:{width:"380px",marginTop:"101px",borderRadius:"8px",boxShadow:"0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)"}}))}}},o=r,c=(a("b315"),a("2877")),l=Object(c["a"])(o,i,s,!1,null,"6a33d9b0",null);e["default"]=l.exports},3347:function(t,e,a){"use strict";var i=a("036f"),s=a.n(i);s.a},"34d5":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"membership-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("订阅会员")]),a("p",{staticClass:"simple-subtitle"},[t._v("成为会员享受更多特权，解锁高级功能")])]),a("section",{staticClass:"recharge-section"},[a("div",{staticClass:"container"},[a("QuickRecharge",{ref:"quickRecharge",on:{"recharge-success":t.handleRechargeSuccess}})],1)]),a("section",{staticClass:"plans-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"plans-grid"},t._l(t.plans,(function(e){return a("div",{key:e.id,staticClass:"plan-card",class:{featured:e.featured}},[e.featured?a("div",{staticClass:"plan-badge"},[t._v("推荐")]):t._e(),e.hasPromotion?a("div",{staticClass:"promotion-ribbon"},[a("span",{staticClass:"promotion-text"},[t._v("限时赠送半年")])]):t._e(),a("div",{staticClass:"plan-header"},[a("h3",{staticClass:"plan-name"},[t._v(t._s(e.name))]),a("div",{staticClass:"plan-price"},[e.originalPrice?a("div",{staticClass:"original-price"},[a("span",{staticClass:"original-price-text"},[t._v("原价：¥"+t._s(e.originalPrice))]),a("span",{staticClass:"discount-badge"},[t._v(t._s(e.discountText))])]):t._e(),a("div",{staticClass:"current-price"},[a("span",{staticClass:"price-symbol"},[t._v("¥")]),a("span",{staticClass:"price-amount"},[t._v(t._s(e.price))]),a("span",{staticClass:"price-period"},[t._v("/"+t._s(e.period))])]),e.saveAmount?a("div",{staticClass:"save-amount"},[t._v("\n                  立省¥"+t._s(e.saveAmount)+"\n                ")]):t._e()]),a("p",{staticClass:"plan-description"},[t._v(t._s(e.description))])]),a("div",{staticClass:"plan-features"},t._l(e.features,(function(e){return a("div",{key:e.text||e,staticClass:"feature-item",class:{"feature-disabled":e.disabled,"feature-exclusive":e.exclusive}},[a("a-icon",{class:{"icon-disabled":e.disabled,"icon-exclusive":e.exclusive},attrs:{type:e.disabled?"close":"check"}}),a("span",{staticClass:"feature-text",domProps:{innerHTML:t._s(e.text||e)}}),e.exclusive?a("span",{staticClass:"exclusive-badge"},[t._v("专属")]):t._e(),e.disabled?a("span",{staticClass:"disabled-text"},[t._v("（SVIP专享）")]):t._e()],1)})),0),a("button",{staticClass:"btn-subscribe",class:{featured:e.featured},on:{click:function(a){return t.handleSubscribe(e)}}},[t._v("\n              "+t._s(t.getButtonText(e))+"\n            ")]),t.showUpgradeNotice(e)?a("div",{staticClass:"upgrade-notice"},[a("i",{staticClass:"icon-info"}),a("span",[t._v(t._s(t.getUpgradeNoticeText(e)))])]):t._e()])})),0)])])]),a("a-modal",{attrs:{title:"选择支付方式",visible:t.showPaymentModal,footer:null,width:"500px"},on:{cancel:function(e){t.showPaymentModal=!1}}},[a("div",{staticClass:"payment-modal-content"},[a("div",{staticClass:"order-info"},[a("h4",[t._v("订单信息")]),a("div",{staticClass:"order-details"},[a("div",{staticClass:"order-item"},[a("span",[t._v("套餐名称：")]),a("span",[t._v(t._s(t.selectedPlan?t.selectedPlan.name:""))])]),a("div",{staticClass:"order-item"},[a("span",[t._v("支付金额：")]),a("span",{staticClass:"amount"},[t._v("¥"+t._s(t.currentOrderAmount))])])])]),a("div",{staticClass:"payment-methods"},[a("h4",[t._v("支付方式")]),a("a-radio-group",{attrs:{size:"large"},model:{value:t.selectedPaymentMethod,callback:function(e){t.selectedPaymentMethod=e},expression:"selectedPaymentMethod"}},[a("a-radio-button",{attrs:{value:"alipay-page"}},[a("i",{staticClass:"anticon anticon-alipay"}),t._v("\n            支付宝网页\n          ")])],1)],1),a("div",{staticClass:"payment-actions"},[a("a-button",{attrs:{type:"primary",size:"large",loading:t.paymentLoading,block:""},on:{click:t.handlePayment}},[t._v("\n          确认支付\n        ")])],1)])]),a("a-modal",{attrs:{title:"扫码支付",visible:t.showQrModal,footer:null,width:"400px"},on:{cancel:t.closeQrModal}},[a("div",{staticClass:"qr-payment-content"},[a("div",{staticClass:"qr-code-container"},[t.qrCodeUrl?a("div",{staticClass:"qr-code"},[a("img",{attrs:{src:t.qrCodeUrl,alt:"支付二维码"}})]):a("div",{staticClass:"qr-loading"},[a("a-spin",{attrs:{size:"large"}}),a("p",[t._v("正在生成二维码...")])],1)]),a("div",{staticClass:"qr-info"},[a("p",{staticClass:"qr-amount"},[t._v("支付金额：¥"+t._s(t.currentOrderAmount))]),a("p",{staticClass:"qr-tip"},[t._v("请使用支付宝扫码支付")])]),a("div",{staticClass:"qr-status"},[a("a-button",{attrs:{loading:t.checkingStatus},on:{click:t.checkPaymentStatus}},[t._v("\n          检查支付状态\n        ")])],1)])])],1)},s=[],n=a("a34a"),r=a.n(n),o=a("df7c"),c=a("0411"),l=a("77ea"),u=a("9fb0"),d=a("2b0e");function p(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function g(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){p(n,i,s,r,o,"next",t)}function o(t){p(n,i,s,r,o,"throw",t)}r(void 0)}))}}var h={name:"Membership",components:{WebsitePage:o["default"],QuickRecharge:c["default"]},data:function(){return{userRole:"user",paymentLoading:!1,showPaymentModal:!1,selectedPlan:null,selectedPaymentMethod:"alipay-page",showQrModal:!1,qrCodeUrl:"",currentOrderId:"",currentOrderAmount:0,paymentCheckTimer:null,checkingStatus:!1,plans:[{id:1,name:"VIP月卡",price:"29",originalPrice:"39",discountText:"限时7.4折",saveAmount:"10",period:"月",description:"适合体验用户的基础功能",features:[{text:"解锁VIP课程",disabled:!1},{text:"插件基础折扣",disabled:!1},{text:"邀请奖励35%基础比例",disabled:!1},{text:"调用工作流基础折扣",disabled:!1},{text:"复制所有工作流",disabled:!0},{text:"解锁流媒体转换",disabled:!0},{text:"部分插件免费",disabled:!0}],buttonText:"立即购买",featured:!1},{id:2,name:"VIP年卡",price:"298",originalPrice:"468",discountText:"限时6.4折",saveAmount:"170",period:"年",description:"适合长期使用的优惠套餐",features:[{text:"解锁VIP课程",disabled:!1},{text:"插件基础折扣",disabled:!1},{text:"邀请奖励35%基础比例",disabled:!1},{text:"调用工作流基础折扣",disabled:!1},{text:"复制所有工作流",disabled:!0},{text:"解锁流媒体转换",disabled:!0},{text:"部分插件免费",disabled:!0}],buttonText:"立即购买",featured:!1,hasPromotion:!0},{id:3,name:"SVIP年卡",price:"489",originalPrice:"999",discountText:"限时4.9折",saveAmount:"510",period:"年",description:"适合专业用户的全功能套餐",features:[{text:'解锁<strong class="highlight">全部课程</strong>',disabled:!1,exclusive:!0},{text:'插件<strong class="highlight">最高折扣</strong>',disabled:!1,exclusive:!0},{text:'邀请<strong class="highlight">奖励50%</strong>',disabled:!1,exclusive:!0},{text:'调用工作流<strong class="highlight">最高折扣</strong>',disabled:!1,exclusive:!0},{text:'复制<strong class="highlight">所有工作流</strong>',disabled:!1,exclusive:!0},{text:'<strong class="highlight">解锁流媒体转换</strong>',disabled:!1,exclusive:!0},{text:'部分<strong class="highlight">插件免费</strong>',disabled:!1,exclusive:!0}],buttonText:"立即购买",featured:!0,hasPromotion:!0}]}},beforeDestroy:function(){this.paymentCheckTimer&&clearInterval(this.paymentCheckTimer)},mounted:function(){var t=g(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.checkLoginStatus()){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,this.loadUserRole();case 5:this.checkPaymentSuccessFromUrl();case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{checkLoginStatus:function(){var t=d["default"].ls.get(u["a"]);return!!t||(this.$router.push({path:"/login",query:{redirect:this.$route.fullPath}}),!1)},getButtonText:function(t){var e=this.getUserRole(),a=this.getPlanRole(t.id);return"admin"===e?"立即购买":e===a?"续费会员":"user"===e?"立即购买":this.isUpgrade(e,a)?"升级会员":"立即购买"},getPlanRole:function(t){var e={1:"VIP",2:"VIP",3:"SVIP"};return e[t]||"user"},getMembershipLevel:function(t){var e={1:2,2:2,3:3};return e[t]||2},isUpgrade:function(t,e){var a={user:1,VIP:2,SVIP:3,admin:999};return a[e]>(a[t]||0)},showUpgradeNotice:function(t){var e=this.getUserRole(),a=this.getPlanRole(t.id);return"VIP"===e&&"SVIP"===a},getUpgradeNoticeText:function(t){var e=this.getUserRole(),a=this.getPlanRole(t.id);return"VIP"===e&&"SVIP"===a?"当前VIP剩余天数会锁定，SVIP到期后会解冻VIP天数，无须担心VIP服务中断哦~":""},getUserRole:function(){return this.userRole||"user"},loadUserRole:function(){var t=g(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,Object(l["w"])();case 4:e=t.sent,e.success?this.userRole=e.result.role_code||"user":this.userRole="user",t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.userRole="user";case 12:case"end":return t.stop()}}),t,this,[[0,8]])})));function e(){return t.apply(this,arguments)}return e}(),handleSubscribe:function(){var t=g(r.a.mark((function t(e){var a,i,s,n;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=this.$ls.get(u["a"]),a){t.next=6;break}return this.$message.warning("请先登录后再购买会员"),this.$router.push("/user/login"),t.abrupt("return");case 6:return t.prev=6,this.paymentLoading=!0,i={membershipLevel:this.getMembershipLevel(e.id),duration:e.name.includes("月")?1:12,amount:parseFloat(e.price),planName:e.name,paymentMethod:"alipay",source:"membership"},this.$message.loading("正在创建订单...",0),t.next=13,Object(l["b"])(i);case 13:s=t.sent,this.$message.destroy(),s.success?(n=s.result,this.selectedPlan=e,this.currentOrderId=n.orderId,this.currentOrderAmount=n.amount,this.showPaymentModal=!0):this.$message.error(s.message||"创建订单失败"),t.next=23;break;case 18:t.prev=18,t.t0=t["catch"](6),this.$message.destroy(),this.$message.error("创建订单失败，请重试");case 23:return t.prev=23,this.paymentLoading=!1,t.finish(23);case 26:case"end":return t.stop()}}),t,this,[[6,18,23,26]])})));function e(e){return t.apply(this,arguments)}return e}(),handlePayment:function(){var t=g(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.currentOrderId){t.next=3;break}return this.$message.error("订单信息错误"),t.abrupt("return");case 3:return t.prev=3,this.paymentLoading=!0,t.next=7,this.handleAlipayPagePayment();case 7:this.showPaymentModal=!1,t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),this.$message.error("支付失败，请重试");case 14:return t.prev=14,this.paymentLoading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[3,10,14,17]])})));function e(){return t.apply(this,arguments)}return e}(),handleAlipayPagePayment:function(){var t=g(r.a.mark((function t(){var e,a,i,s,n;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在跳转到支付宝支付...",0),e={orderId:this.currentOrderId,amount:this.currentOrderAmount,subject:"智界Aigc会员 - ".concat(this.selectedPlan.name),body:"购买".concat(this.selectedPlan.name,"，金额：¥").concat(this.currentOrderAmount)},t.next=7,this.$http.post("/api/alipay/createOrder",e);case 7:if(a=t.sent,this.$message.destroy(),!a.success){t.next=24;break}if(i=a.result.payForm,i){t.next=16;break}return this.$message.error("支付表单为空"),t.abrupt("return");case 16:s=document.createElement("div"),s.innerHTML=i,document.body.appendChild(s),n=s.querySelector("form"),n?n.submit():this.$message.error("支付表单创建失败"),setTimeout((function(){document.body.contains(s)&&document.body.removeChild(s)}),1e3),t.next=26;break;case 24:this.$message.error(a.message||"创建支付订单失败");case 26:t.next=33;break;case 28:t.prev=28,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝支付失败，请重试");case 33:case"end":return t.stop()}}),t,this,[[0,28]])})));function e(){return t.apply(this,arguments)}return e}(),handleAlipayQrPayment:function(){var t=g(r.a.mark((function t(){var e,a,i;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.$message.loading("正在生成支付二维码...",0),e={orderId:this.currentOrderId,amount:this.currentOrderAmount,subject:"智界Aigc会员 - ".concat(this.selectedPlan.name),body:"购买".concat(this.selectedPlan.name,"，金额：¥").concat(this.currentOrderAmount)},t.next=7,this.$http.post("/api/alipay/createQrOrder",e);case 7:if(a=t.sent,this.$message.destroy(),!a.success){t.next=19;break}if(i=a.result.qrCode,i){t.next=16;break}return this.$message.error("支付二维码生成失败"),t.abrupt("return");case 16:this.showQrCodeModal(i),t.next=21;break;case 19:this.$message.error(a.message||"创建扫码支付订单失败");case 21:t.next=28;break;case 23:t.prev=23,t.t0=t["catch"](0),this.$message.destroy(),this.$message.error("支付宝扫码支付失败，请重试");case 28:case"end":return t.stop()}}),t,this,[[0,23]])})));function e(){return t.apply(this,arguments)}return e}(),showQrCodeModal:function(t){this.qrCodeUrl="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=".concat(encodeURIComponent(t)),this.showQrModal=!0,this.startPaymentStatusCheck()},closeQrModal:function(){this.showQrModal=!1,this.qrCodeUrl="",this.paymentCheckTimer&&(clearInterval(this.paymentCheckTimer),this.paymentCheckTimer=null)},startPaymentStatusCheck:function(){var t=this;this.paymentCheckTimer&&clearInterval(this.paymentCheckTimer),this.paymentCheckTimer=setInterval((function(){t.checkPaymentStatus()}),3e3)},checkPaymentStatus:function(){var t=g(r.a.mark((function t(){var e,a=this;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.currentOrderId){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,this.checkingStatus=!0,t.next=7,this.$http.get("/api/alipay/queryOrder/".concat(this.currentOrderId));case 7:e=t.sent,e.success&&"TRADE_SUCCESS"===e.result.status&&(this.$message.success("支付成功！会员权益已生效"),this.closeQrModal(),localStorage.removeItem("userRole"),localStorage.removeItem("roleCode"),this.$refs.quickRecharge&&this.$refs.quickRecharge.loadUserInfo&&this.$refs.quickRecharge.loadUserInfo(),this.forceRefreshUserRole(),setTimeout((function(){a.$message.success("会员购买成功！页面即将刷新以更新会员状态"),window.location.reload()}),2e3)),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2);case 15:return t.prev=15,this.checkingStatus=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[2,12,15,18]])})));function e(){return t.apply(this,arguments)}return e}(),handleRechargeSuccess:function(){this.$message.success("充值成功！您可以继续选择套餐")},getOrderType:function(){var t=g(r.a.mark((function t(e){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!e.startsWith("RECHARGE_")){t.next=5;break}return t.abrupt("return",{orderType:"recharge"});case 5:if(!e.startsWith("ORDER_")){t.next=9;break}return t.abrupt("return",{orderType:"membership"});case 9:return t.abrupt("return",{orderType:"unknown"});case 10:t.next=16;break;case 12:throw t.prev=12,t.t0=t["catch"](0),t.t0;case 16:case"end":return t.stop()}}),t,null,[[0,12]])})));function e(e){return t.apply(this,arguments)}return e}(),forceRefreshUserRole:function(){var t=g(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,localStorage.removeItem("userRole"),localStorage.removeItem("roleCode"),t.next=6,new Promise((function(t){return setTimeout(t,1e3)}));case 6:return t.next=8,this.loadUserRole();case 8:t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](0);case 14:case"end":return t.stop()}}),t,this,[[0,11]])})));function e(){return t.apply(this,arguments)}return e}(),checkPaymentSuccessFromUrl:function(){var t=g(r.a.mark((function t(){var e,a,i,s,n,o;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=new URLSearchParams(window.location.search),a=e.get("paymentSuccess"),i=e.get("orderId"),"true"!==a||!i){t.next=19;break}return t.prev=5,t.next=8,this.getOrderType(i);case 8:s=t.sent,n=s.orderType,"recharge"===n?this.$message.success("充值成功！您可以继续选择套餐"):"membership"===n?(this.$message.success("会员购买成功！权益已生效"),localStorage.removeItem("userRole"),localStorage.removeItem("roleCode"),this.$refs.quickRecharge&&this.$refs.quickRecharge.loadUserInfo&&this.$refs.quickRecharge.loadUserInfo(),this.forceRefreshUserRole()):i.startsWith("RECHARGE_")?this.$message.success("充值成功！您可以继续选择套餐"):this.$message.success("支付成功！"),t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](5),i.startsWith("RECHARGE_")?this.$message.success("充值成功！您可以继续选择套餐"):this.$message.success("支付成功！");case 17:o=window.location.pathname,window.history.replaceState({},document.title,o);case 19:case"end":return t.stop()}}),t,this,[[5,13]])})));function e(){return t.apply(this,arguments)}return e}()}},v=h,f=(a("767d"),a("2877")),m=Object(f["a"])(v,i,s,!1,null,null,null);e["default"]=m.exports},3727:function(t,e,a){},4015:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"related-plugins"},[a("div",{staticClass:"related-card"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n      相关推荐\n      "),t.categoryText?a("span",{staticClass:"category-hint"},[t._v("- "+t._s(t.categoryText))]):t._e()],1),t.recommendations&&t.recommendations.length>0?a("div",{staticClass:"plugins-grid"},t._l(t.recommendations,(function(e){return a("div",{key:e.id,staticClass:"plugin-card",on:{click:function(a){return t.goToPlugin(e.id)}}},[a("div",{staticClass:"plugin-image"},[a("img",{attrs:{src:t.getPluginImage(e),alt:e.plubname},on:{error:t.handleImageError}}),a("div",{staticClass:"image-overlay"},[a("a-icon",{staticClass:"view-icon",attrs:{type:"eye"}})],1)]),a("div",{staticClass:"plugin-info"},[a("h4",{staticClass:"plugin-name"},[t._v(t._s(e.plubname))]),a("p",{staticClass:"plugin-description"},[t._v(t._s(e.plubinfo||"暂无描述"))]),a("div",{staticClass:"plugin-meta"},[a("div",{staticClass:"meta-item"},[a("a-icon",{attrs:{type:"dollar"}}),a("span",[t._v(t._s(t.getPluginPriceText(e)))])],1)]),a("div",{staticClass:"plugin-tags"},[a("a-tag",{attrs:{color:t.getCategoryColor(e.plubCategory),size:"small"}},[t._v("\n              "+t._s(t.getCategoryText(e.plubCategory))+"\n            ")]),a("a-tag",{attrs:{color:t.getStatusColor(e.status),size:"small"}},[t._v("\n              "+t._s(t.getStatusText(e.status))+"\n            ")])],1)]),a("div",{staticClass:"plugin-actions"},[a("a-button",{attrs:{type:"primary",size:"small"},on:{click:function(a){return a.stopPropagation(),t.goToPlugin(e.id)}}},[t._v("\n            查看详情\n          ")])],1)])})),0):a("div",{staticClass:"no-recommendations"},[a("a-empty",{attrs:{description:"暂无相关推荐"}},[a("a-button",{attrs:{type:"primary"},on:{click:t.goToMarket}},[a("a-icon",{attrs:{type:"shop"}}),t._v("\n          浏览更多插件\n        ")],1)],1)],1),t.recommendations&&t.recommendations.length>0?a("div",{staticClass:"more-actions"},[a("a-button",{staticClass:"more-plugins-btn",on:{click:t.viewMoreByCategory}},[a("a-icon",{attrs:{type:"appstore"}}),a("span",[t._v("查看更多插件")]),a("a-icon",{attrs:{type:"right"}})],1)],1):t._e()])])},s=[],n=a("a34a"),r=a.n(n),o=a("8b05");function c(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function l(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){c(n,i,s,r,o,"next",t)}function o(t){c(n,i,s,r,o,"throw",t)}r(void 0)}))}}var u={name:"RelatedPlugins",props:{recommendations:{type:Array,default:function(){return[]}},currentCategory:{type:String,default:""},currentPluginId:{type:String,default:""}},data:function(){return{defaultPluginImage:"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"}},mounted:function(){var t=l(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=4,this.$categoryService.getCategories();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),watch:{recommendations:{handler:function(t){},immediate:!0}},computed:{categoryText:function(){return this.currentCategory?this.$categoryService.getCategoryText(this.currentCategory):"相关"},defaultPluginImage:function(){return"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"}},methods:{getPluginImage:function(t){return Object(o["e"])(t,this.defaultPluginImage)},handleImageError:function(t){t.target.src=this.defaultPluginImage},formatNumber:function(t){return t?t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString():"0"},getCategoryColor:function(t){return this.$categoryService.getCategoryColor(t)},getCategoryText:function(t){return this.$categoryService.getCategoryText(t)},getStatusColor:function(t){var e={0:"red",1:"green",2:"orange",3:"red"};return e[t]||"default"},getStatusText:function(t){var e={0:"已下架",1:"已上架",2:"审核中",3:"已拒绝"};return e[t]||"未知状态"},goToPlugin:function(t){t!==this.currentPluginId?this.$router.push("/market/plugin/".concat(t)):this.$message.info("这就是当前插件")},goToMarket:function(){this.$router.push("/market")},viewMoreByCategory:function(){this.$router.push("/market?category=".concat(this.currentCategory))},getPluginPriceText:function(t){var e=t.neednum,a=1===t.isSvipFree||"1"===t.isSvipFree;return!e||e<=0?"免费":a?"SVIP免费，低至¥".concat(e,"/次"):"低至¥".concat(e,"/次")}}},d=u,p=(a("0586"),a("2877")),g=Object(p["a"])(d,i,s,!1,null,"427a4d1d",null);e["default"]=g.exports},4404:function(t,e,a){},"4c1a":function(t,e,a){"use strict";var i=a("4404"),s=a.n(i);s.a},"515e":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"plugin-introduction"},[a("div",{staticClass:"intro-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"info-circle"}}),t._v("\n      插件概述\n    ")],1),a("div",{staticClass:"intro-summary"},[a("p",{staticClass:"summary-text"},[t._v(t._s(t.info||"暂无插件简介"))])])]),a("div",{staticClass:"intro-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"star"}}),t._v("\n      功能亮点\n    ")],1),a("div",{staticClass:"highlights"},[a("div",{staticClass:"highlight-item"},[a("a-icon",{staticClass:"highlight-icon",attrs:{type:"check-circle"}}),a("span",[t._v("智能化处理，提升工作效率")])],1),a("div",{staticClass:"highlight-item"},[a("a-icon",{staticClass:"highlight-icon",attrs:{type:"check-circle"}}),a("span",[t._v("简单易用，无需复杂配置")])],1),a("div",{staticClass:"highlight-item"},[a("a-icon",{staticClass:"highlight-icon",attrs:{type:"check-circle"}}),a("span",[t._v("高质量输出，满足专业需求")])],1),a("div",{staticClass:"highlight-item"},[a("a-icon",{staticClass:"highlight-icon",attrs:{type:"check-circle"}}),a("span",[t._v("持续更新，功能不断完善")])],1)])]),a("div",{staticClass:"intro-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"deployment-unit"}}),t._v("\n      适用场景\n    ")],1),a("div",{staticClass:"scenarios"},t._l(t.scenariosList,(function(e,i){return a("a-tag",{key:i,staticClass:"scenario-tag",attrs:{color:t.getScenarioColor(i)}},[a("a-icon",{attrs:{type:t.getScenarioIcon(e)}}),t._v("\n        "+t._s(e)+"\n      ")],1)})),1)]),t.content||t.info?t._e():a("div",{staticClass:"empty-content"},[a("a-empty",{attrs:{description:"暂无详细介绍"}},[a("a-button",{attrs:{type:"primary"},on:{click:t.suggestContent}},[t._v("\n        建议完善介绍\n      ")])],1)],1)])},s=[],n={name:"PluginIntroduction",props:{content:{type:String,default:""},info:{type:String,default:""},pluginName:{type:String,default:""},pluginDetail:{type:Object,default:function(){return{}}}},computed:{scenariosList:function(){return this.pluginDetail.scenarios_dictText?this.pluginDetail.scenarios_dictText.split(",").map((function(t){return t.trim()})):["内容创作","办公效率"]}},methods:{suggestContent:function(){this.$message.info("您可以联系插件作者完善介绍内容")},getScenarioColor:function(t){var e=["blue","green","orange","purple","cyan","red","geekblue","magenta","volcano","gold"];return e[t%e.length]},getScenarioIcon:function(t){var e={"内容创作":"edit","办公效率":"laptop","教育培训":"book","电商营销":"shopping","娱乐游戏":"smile","社交媒体":"share-alt","数据分析":"bar-chart","设计创意":"bg-colors","客户服务":"customer-service","项目管理":"project","技术开发":"code","财务管理":"dollar","人力资源":"team","医疗健康":"heart","法律咨询":"safety","房产中介":"home","旅游出行":"car","生活服务":"tool"};return e[t]||"tag"}}},r=n,o=(a("5f90"),a("2877")),c=Object(o["a"])(r,i,s,!1,null,"59b05f8a",null);e["default"]=c.exports},"561b":function(t,e,a){"use strict";var i=a("3727"),s=a.n(i);s.a},"5f90":function(t,e,a){"use strict";var i=a("2649"),s=a.n(i);s.a},"5fb2":function(t,e,a){"use strict";var i=a("9aed"),s=a.n(i);s.a},"626c":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"market-container"},[a("div",{staticClass:"simple-header"},[a("h1",{staticClass:"simple-title"},[t._v("插件中心")])]),a("section",{staticClass:"search-section"},[a("div",{staticClass:"container"},[a("div",{staticClass:"search-layout"},[a("div",{staticClass:"search-input-group"},[a("div",{staticClass:"custom-search-input"},[a("div",{staticClass:"search-icon"},[a("a-icon",{attrs:{type:"search"}})],1),a("input",{directives:[{name:"model",rawName:"v-model",value:t.searchKeyword,expression:"searchKeyword"}],staticClass:"search-input-native",attrs:{placeholder:"搜索插件名称、描述..."},domProps:{value:t.searchKeyword},on:{input:[function(e){e.target.composing||(t.searchKeyword=e.target.value)},t.handleSearchInput],keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleSearch(e)}}}),t.searchKeyword?a("div",{staticClass:"clear-search-icon",attrs:{title:"清空搜索"},on:{click:t.clearSearchInput}},[a("a-icon",{attrs:{type:"close-circle"}})],1):t._e()]),a("a-button",{staticClass:"clear-filters-btn",attrs:{size:"large",disabled:!t.hasActiveFilters},on:{click:t.clearAllFilters}},[a("a-icon",{attrs:{type:"clear"}}),t._v("\n              清空所有筛选\n            ")],1)],1)])])]),a("section",{staticClass:"main-content"},[a("div",{staticClass:"container"},[a("div",{staticClass:"content-layout"},[a("aside",{staticClass:"sidebar"},[a("div",{staticClass:"filter-panel"},[a("div",{staticClass:"filter-section"},[a("div",{staticClass:"filter-header",on:{click:function(e){return t.toggleSection("category")}}},[a("h3",{staticClass:"filter-title"},[a("a-icon",{staticClass:"filter-icon",attrs:{type:"appstore"}}),t._v("\n                    插件分类\n                    "),t.currentFilters.category?a("span",{staticClass:"filter-badge"},[t._v("1")]):t._e()],1),a("a-icon",{staticClass:"collapse-icon",attrs:{type:t.collapsedSections.category?"down":"up"}})],1),a("a-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.collapsedSections.category,expression:"!collapsedSections.category"}],staticClass:"filter-content"},[a("div",{staticClass:"category-grid"},[a("div",{staticClass:"category-tag",class:{active:""===t.currentFilters.category},on:{click:function(e){return t.selectCategory("")}}},[a("span",{staticClass:"tag-icon"},[t._v("🌟")]),a("span",{staticClass:"tag-text"},[t._v("全部")]),a("span",{staticClass:"tag-count"},[t._v(t._s(t.totalPlugins))])]),t._l(t.categories,(function(e){return a("div",{key:e.value,staticClass:"category-tag",class:{active:t.currentFilters.category===e.value},on:{click:function(a){return t.selectCategory(e.value)}}},[a("span",{staticClass:"tag-icon"},[t._v(t._s(t.getCategoryIcon(e.value)))]),a("span",{staticClass:"tag-text"},[t._v(t._s(e.text))]),a("span",{staticClass:"tag-count"},[t._v(t._s(t.categoryCounts[e.value]||0))])])}))],2)])])],1),a("div",{staticClass:"filter-section"},[a("div",{staticClass:"filter-header",on:{click:function(e){return t.toggleSection("price")}}},[a("h3",{staticClass:"filter-title"},[a("a-icon",{staticClass:"filter-icon",attrs:{type:"dollar"}}),t._v("\n                    价格范围\n                    "),t.currentFilters.priceRange?a("span",{staticClass:"filter-badge"},[t._v("1")]):t._e()],1),a("a-icon",{staticClass:"collapse-icon",attrs:{type:t.collapsedSections.price?"down":"up"}})],1),a("a-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.collapsedSections.price,expression:"!collapsedSections.price"}],staticClass:"filter-content"},[a("div",{staticClass:"price-grid"},[a("div",{staticClass:"price-tag",class:{active:""===t.currentFilters.priceRange&&!t.showCustomPrice},on:{click:function(e){return t.selectPriceRange("")}}},[a("span",{staticClass:"tag-icon"},[t._v("💰")]),a("span",{staticClass:"tag-text"},[t._v("全部")])]),a("div",{staticClass:"price-tag",class:{active:"0-1"===t.currentFilters.priceRange},on:{click:function(e){return t.selectPriceRange("0-1")}}},[a("span",{staticClass:"tag-icon"},[t._v("🪙")]),a("span",{staticClass:"tag-text"},[t._v("¥0-1")])]),a("div",{staticClass:"price-tag",class:{active:"1-5"===t.currentFilters.priceRange},on:{click:function(e){return t.selectPriceRange("1-5")}}},[a("span",{staticClass:"tag-icon"},[t._v("💵")]),a("span",{staticClass:"tag-text"},[t._v("¥1-5")])]),a("div",{staticClass:"price-tag",class:{active:"5+"===t.currentFilters.priceRange},on:{click:function(e){return t.selectPriceRange("5+")}}},[a("span",{staticClass:"tag-icon"},[t._v("💎")]),a("span",{staticClass:"tag-text"},[t._v("¥5+")])])]),a("div",{staticClass:"custom-price-range"},[a("div",{staticClass:"custom-price-header"},[a("span",{staticClass:"custom-price-icon"},[t._v("⚙️")]),a("span",{staticClass:"custom-price-label"},[t._v("自定义价格")])]),a("div",{staticClass:"price-inputs"},[a("div",{staticClass:"price-input-row"},[a("label",{staticClass:"input-label"},[t._v("最低价格")]),a("a-input-number",{staticClass:"price-input",attrs:{min:0,max:999,placeholder:"请输入最低价格",size:"default"},on:{pressEnter:t.applyCustomPrice},model:{value:t.customPriceMin,callback:function(e){t.customPriceMin=e},expression:"customPriceMin"}})],1),a("div",{staticClass:"price-input-row"},[a("label",{staticClass:"input-label"},[t._v("最高价格")]),a("a-input-number",{staticClass:"price-input",attrs:{min:t.customPriceMin||0,max:999,placeholder:"请输入最高价格",size:"default"},on:{pressEnter:t.applyCustomPrice},model:{value:t.customPriceMax,callback:function(e){t.customPriceMax=e},expression:"customPriceMax"}})],1),a("a-button",{staticClass:"apply-custom-btn",attrs:{type:"primary",disabled:!t.customPriceMin&&!t.customPriceMax,block:""},on:{click:t.applyCustomPrice}},[t._v("\n                          确定筛选\n                        ")])],1)])])])],1),a("div",{staticClass:"filter-section"},[a("div",{staticClass:"filter-header",on:{click:function(e){return t.toggleSection("sort")}}},[a("h3",{staticClass:"filter-title"},[a("a-icon",{staticClass:"filter-icon",attrs:{type:"sort-ascending"}}),t._v("\n                    排序方式\n                    "),"default"!==t.currentFilters.sortType?a("span",{staticClass:"filter-badge"},[t._v("1")]):t._e()],1),a("a-icon",{staticClass:"collapse-icon",attrs:{type:t.collapsedSections.sort?"down":"up"}})],1),a("a-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.collapsedSections.sort,expression:"!collapsedSections.sort"}],staticClass:"filter-content"},[a("div",{staticClass:"sort-grid"},[a("div",{staticClass:"sort-tag",class:{active:"default"===t.currentFilters.sortType},on:{click:function(e){return t.selectSort("default")}}},[a("span",{staticClass:"tag-icon"},[t._v("🌟")]),a("span",{staticClass:"tag-text"},[t._v("默认")])]),a("div",{staticClass:"sort-tag",class:{active:"newest"===t.currentFilters.sortType},on:{click:function(e){return t.selectSort("newest")}}},[a("span",{staticClass:"tag-icon"},[t._v("⏰")]),a("span",{staticClass:"tag-text"},[t._v("最新")])]),a("div",{staticClass:"sort-tag",class:{active:"price-asc"===t.currentFilters.sortType},on:{click:function(e){return t.selectSort("price-asc")}}},[a("span",{staticClass:"tag-icon"},[t._v("📈")]),a("span",{staticClass:"tag-text"},[t._v("价格↑")])]),a("div",{staticClass:"sort-tag",class:{active:"price-desc"===t.currentFilters.sortType},on:{click:function(e){return t.selectSort("price-desc")}}},[a("span",{staticClass:"tag-icon"},[t._v("📉")]),a("span",{staticClass:"tag-text"},[t._v("价格↓")])])])])])],1)])]),a("main",{staticClass:"main-area"},[a("div",{staticClass:"results-header"},[a("div",{staticClass:"header-left"},[a("h2",{staticClass:"results-title"},[t.currentFilters.category?a("span",[t._v(t._s(t.getCategoryText(t.currentFilters.category))+"插件")]):t.currentFilters.keyword?a("span",[t._v("搜索结果")]):a("span",[t._v("全部插件")])]),t.currentFilters.keyword||t.currentFilters.priceRange||"default"!==t.currentFilters.sortType?a("div",{staticClass:"active-filters"},[t.currentFilters.keyword?a("a-tag",{staticClass:"filter-tag",attrs:{closable:"",color:"green"},on:{close:t.clearKeywordFilter}},[t._v('\n                    "'+t._s(t.currentFilters.keyword)+'"\n                  ')]):t._e(),t.currentFilters.priceRange?a("a-tag",{staticClass:"filter-tag",attrs:{closable:"",color:"orange"},on:{close:t.clearPriceFilter}},[t._v("\n                    "+t._s(t.getPriceRangeText(t.currentFilters.priceRange))+"\n                  ")]):t._e(),"default"!==t.currentFilters.sortType?a("a-tag",{staticClass:"filter-tag",attrs:{closable:"",color:"purple"},on:{close:t.clearSortFilter}},[t._v("\n                    "+t._s(t.getSortTypeText(t.currentFilters.sortType))+"\n                  ")]):t._e()],1):t._e()]),a("div",{staticClass:"header-right"},[a("div",{staticClass:"results-count"},[a("span",{staticClass:"count-number"},[t._v(t._s(t.filteredPlugins.length))]),t._v(" 个一级插件，共 "),a("span",{staticClass:"count-number"},[t._v(t._s(t.filteredTotalPlugins))]),t._v(" 个插件\n                ")])])]),a("div",{staticClass:"plugins-grid-wrapper"},[a("PluginGrid",{attrs:{plugins:t.currentPagePlugins,loading:t.loading,error:t.error},on:{"plugin-use":t.handlePluginUse,"plugin-detail":t.handlePluginDetail,"combined-plugin-detail":t.viewCombinedPluginDetails,retry:t.handleRetry,"clear-filters":t.clearAllFilters}})],1),t.filteredPlugins.length>t.pageSize?a("div",{staticClass:"pagination-wrapper"},[a("a-pagination",{attrs:{current:t.currentPage,total:t.filteredPlugins.length,"page-size":t.pageSize,"page-size-options":["8","12","16","24"],"show-size-changer":!0,"show-quick-jumper":!0,"show-total":function(t,e){return"显示第 "+e[0]+"-"+e[1]+" 条，共 "+t+" 条"}},on:{change:t.handlePageChange,showSizeChange:t.handlePageSizeChange}})],1):t._e()])])])])]),t.showCombinedModal?a("div",{staticClass:"combined-modal-overlay",on:{click:t.closeCombinedModal}},[a("div",{staticClass:"combined-modal-content",on:{click:function(t){t.stopPropagation()}}},[a("div",{staticClass:"combined-modal-header"},[a("div",{staticClass:"header-content1"},[a("div",{staticClass:"header-icon"},[t._v("🔗")]),a("div",{staticClass:"header-text"},[a("h2",[t._v("选择插件")]),a("p",{staticClass:"header-subtitle"},[t._v(t._s(t.selectedCombinedPlugin&&t.selectedCombinedPlugin.combinedName))])])]),a("button",{staticClass:"combined-modal-close",on:{click:t.closeCombinedModal}},[t._v("×")])]),a("div",{staticClass:"combined-modal-body"},[t.combinedModalLoading?a("div",{staticClass:"loading-state"},[a("a-spin",{attrs:{size:"large"}}),a("p",[t._v("正在加载子插件...")])],1):0===t.combinedSubPlugins.length?a("div",{staticClass:"empty-state"},[a("p",[t._v("暂无子插件")])]):a("div",{staticClass:"sub-plugins-grid"},t._l(t.combinedSubPlugins,(function(e){return a("div",{key:e.id,staticClass:"sub-plugin-card",on:{click:function(a){return t.selectSubPlugin(e)}}},[a("div",{staticClass:"sub-plugin-image"},[a("img",{attrs:{src:t.getPluginImage(e),alt:e.plubname}}),a("div",{staticClass:"sub-plugin-category"},[a("span",{staticClass:"category-icon"},[t._v("🔧")]),a("span",[t._v(t._s(e.plubCategory_dictText||"其他"))])]),a("div",{staticClass:"sub-plugin-status"},[t._v("上架")])]),a("div",{staticClass:"sub-plugin-content"},[a("div",{staticClass:"sub-plugin-header"},[a("h3",{staticClass:"sub-plugin-title"},[t._v(t._s(e.plubname))]),a("div",{staticClass:"sub-plugin-author"},[a("span",{staticClass:"author-icon"},[t._v("👤")]),a("span",[t._v("创作者 "+t._s(e.plubwrite_dictText||"未知"))])])]),a("p",{staticClass:"sub-plugin-description"},[t._v(t._s(e.plubinfo||"暂无描述"))]),a("div",{staticClass:"sub-plugin-footer"},[a("div",{staticClass:"sub-plugin-price"},[a("span",{staticClass:"price-amount"},[t._v(t._s(t.getSubPluginPriceText(e)))])]),a("button",{staticClass:"sub-plugin-btn"},[a("span",{staticClass:"btn-icon"},[t._v("👁")]),a("span",[t._v("查看详情")])])])])])})),0)])])]):t._e(),t.showJianYingFloat?a("div",{staticClass:"jianying-float-container"},[a("button",{staticClass:"jianying-float-btn",on:{click:t.goToJianYingDraft}},[a("div",{staticClass:"float-close-btn",on:{click:function(e){return e.stopPropagation(),t.hideJianYingFloat(e)}}},[a("a-icon",{attrs:{type:"close"}})],1),a("div",{staticClass:"btn-icon"},[a("a-icon",{attrs:{type:"download"}})],1),a("div",{staticClass:"btn-text"},[t._v("剪映小助手下载")]),a("div",{staticClass:"btn-glow"}),a("div",{ref:"particles",staticClass:"btn-particles"}),a("div",{staticClass:"jianying-waves"})])]):t._e()])},s=[],n=a("a34a"),r=a.n(n),o=a("df7c"),c=a("0932"),l=a("9027e"),u=a("9810"),d=a("4124"),p=a("8b05"),g=a("a73df"),h=a("8d13");function v(t,e){return y(t)||m(t,e)||_(t,e)||f()}function f(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],i=!0,s=!1,n=void 0;try{for(var r,o=t[Symbol.iterator]();!(i=(r=o.next()).done);i=!0)if(a.push(r.value),e&&a.length===e)break}catch(c){s=!0,n=c}finally{try{i||null==o["return"]||o["return"]()}finally{if(s)throw n}}return a}}function y(t){if(Array.isArray(t))return t}function b(t){return w(t)||P(t)||_(t)||C()}function C(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t,e){if(t){if("string"===typeof t)return k(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?k(t,e):void 0}}function P(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function w(t){if(Array.isArray(t))return k(t)}function k(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function x(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function S(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?x(Object(a),!0).forEach((function(e){F(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):x(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function F(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function T(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function I(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){T(n,i,s,r,o,"next",t)}function o(t){T(n,i,s,r,o,"throw",t)}r(void 0)}))}}var A={name:"Market",mixins:[g["a"]],components:{WebsitePage:o["default"],CategoryFilter:c["default"],SearchFilter:l["default"],PluginGrid:u["default"]},data:function(){return{heartbeatConfig:Object(h["a"])("market",{apiKey:"market-page-heartbeat-key",enableDebugLog:!1}),allPlugins:[],filteredPlugins:[],currentPagePlugins:[],originalPluginsData:[],categories:[],categoryCounts:{},currentPage:1,pageSize:12,currentFilters:{category:"",keyword:"",priceRange:"",sortType:"default",author:""},loading:!1,error:null,totalPlugins:0,searchKeyword:"",showSuggestions:!1,suggestions:["文案生成","图片处理","视频剪辑","数据分析","代码助手"],collapsedSections:{search:!1,category:!1,price:!0,sort:!0},showCustomPrice:!1,customPriceMin:null,customPriceMax:null,showCombinedModal:!1,selectedCombinedPlugin:null,combinedModalLoading:!1,combinedSubPlugins:[],showJianYingFloat:!0}},computed:{hasActiveFilters:function(){return this.currentFilters.category||this.currentFilters.keyword||this.currentFilters.priceRange||"default"!==this.currentFilters.sortType},totalOriginalPlugins:function(){return this.originalPluginsData?this.originalPluginsData.length:0},filteredTotalPlugins:function(){var t=this,e=0;return this.filteredPlugins.forEach((function(a){if(1===a.isCombined){var i=t.getSubPluginsFromOriginalData(a.combinedName);e+=i.length}else e+=1})),e},defaultPluginImage:function(){return"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"}},created:function(){var t=I(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.showJianYingFloat=!0,0!==this.allPlugins.length){t.next=7;break}return t.next=5,this.initializeMarket();case 5:t.next=10;break;case 7:this.restoreMarketState(),this.applyFilters();case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){document.body.style.overflow=""},watch:{currentFilters:{handler:function(t){this.saveMarketState(t)},deep:!0},searchKeyword:function(t){this.saveMarketState(S(S({},this.currentFilters),{},{keyword:t}))}},methods:{initializeMarket:function(){var t=I(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,t.prev=1,this.restoreMarketState(),t.next=5,Promise.all([this.loadCategories(),this.loadPlugins()]);case 5:t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](1),this.error="商城初始化失败，请刷新页面重试";case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[1,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),loadCategories:function(){var t=I(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,d["a"].getPluginCategories();case 3:e=t.sent,e.success&&(this.categories=Object(p["b"])(e.result||[])),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadPlugins:function(){var t=I(r.a.mark((function t(){var e,a,i,s,n;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e={pageNo:1,pageSize:1e3,status:1},t.next=4,d["a"].getPluginList(e);case 4:if(a=t.sent,!a.success){t.next=17;break}i=a.result||a.data,s=i.records||i||[],this.originalPluginsData=b(s),n=Object(p["g"])(s),this.allPlugins=n,this.totalPlugins=n.length,this.calculateCategoryCounts(),this.applyFilters(),t.next=18;break;case 17:throw new Error(a.message||"获取插件数据失败");case 18:t.next=24;break;case 20:t.prev=20,t.t0=t["catch"](0),this.error=t.t0.message||"加载插件数据失败";case 24:case"end":return t.stop()}}),t,this,[[0,20]])})));function e(){return t.apply(this,arguments)}return e}(),combinedPluginHasCategory:function(t,e){return t.plubCategory===e},calculateCategoryCounts:function(){var t=this;this.categoryCounts={},this.categories.forEach((function(e){t.categoryCounts[e.value]=0}));var e=0;this.allPlugins.forEach((function(a){1===a.isCombined||"1"===a.isCombined?(e++,a.plubCategory&&t.categoryCounts.hasOwnProperty(a.plubCategory)&&t.categoryCounts[a.plubCategory]++):a.plubCategory&&t.categoryCounts.hasOwnProperty(a.plubCategory)&&t.categoryCounts[a.plubCategory]++})),this.categoryCounts["combine"]=e},applyFilters:function(){var t=this,e=b(this.allPlugins);if(this.currentFilters.category&&(e="combine"===this.currentFilters.category?e.filter((function(t){return 1===t.isCombined||"1"===t.isCombined})):e.filter((function(e){return 1!==e.isCombined&&"1"!==e.isCombined?e.plubCategory===t.currentFilters.category:t.combinedPluginHasCategory(e,t.currentFilters.category)}))),this.currentFilters.keyword){var a=this.currentFilters.keyword.toLowerCase();e=this.filterByKeyword(e,a)}this.currentFilters.priceRange&&(e=this.filterByPriceRange(e,this.currentFilters.priceRange)),e=this.sortPlugins(e,this.currentFilters.sortType),this.filteredPlugins=e,this.updateCurrentPagePlugins()},filterByKeyword:function(t,e){var a=this;return t.filter((function(t){if(1===t.isCombined||"1"===t.isCombined){if(t.combinedName&&t.combinedName.toLowerCase().includes(e)||t.combinedDescription&&t.combinedDescription.toLowerCase().includes(e))return!0;var i=a.getSubPluginsFromOriginalData(t.combinedName);return i.some((function(t){return t.plubname&&t.plubname.toLowerCase().includes(e)}))}return t.plubname&&t.plubname.toLowerCase().includes(e)||t.plubinfo&&t.plubinfo.toLowerCase().includes(e)}))},getSubPluginsFromOriginalData:function(t){return this.originalPluginsData?this.originalPluginsData.filter((function(e){return 1===e.isCombined&&e.combinedName===t})):[]},filterByPriceRange:function(t,e){return e?t.filter((function(t){var a=parseFloat(t.neednum)||0;switch(e){case"0-1":return a>=0&&a<=1;case"1-5":return a>1&&a<=5;case"5+":return a>5;default:if(e.includes("-")){var i=e.split("-").map((function(t){return parseFloat(t)||0})),s=v(i,2),n=s[0],r=s[1];return 999===r?a>=n:a>=n&&a<=r}return!0}})):t},sortPlugins:function(t,e){var a=b(t);switch(e){case"default":return a.sort((function(t,e){var a=parseFloat(t.sortOrder||t.sort_order||999999),i=parseFloat(e.sortOrder||e.sort_order||999999);return a-i}));case"newest":return a.sort((function(t,e){var a=new Date(t.createTime||t.createBy||0).getTime(),i=new Date(e.createTime||e.createBy||0).getTime();return i-a}));case"price-asc":return a.sort((function(t,e){var a=parseFloat(t.neednum)||0,i=parseFloat(e.neednum)||0;return a-i}));case"price-desc":return a.sort((function(t,e){var a=parseFloat(t.neednum)||0,i=parseFloat(e.neednum)||0;return i-a}));case"name-asc":return a.sort((function(t,e){var a=(t.plubname||"").toLowerCase(),i=(e.plubname||"").toLowerCase();return a.localeCompare(i)}));default:return a.sort((function(t,e){var a=parseFloat(t.sortWeight||t.sort_weight||999999),i=parseFloat(e.sortWeight||e.sort_weight||999999);return a-i}))}},updateCurrentPagePlugins:function(){var t=(this.currentPage-1)*this.pageSize,e=t+this.pageSize;this.currentPagePlugins=this.filteredPlugins.slice(t,e)},handleCategoryChange:function(t){this.currentFilters.category=t.category,this.currentPage=1,this.applyFilters()},handleSearchChange:function(t){this.currentFilters.keyword=t.keyword,this.currentPage=1,this.applyFilters()},handleFilterChange:function(t){this.currentFilters=S(S({},this.currentFilters),t),this.currentPage=1,this.applyFilters()},handlePageChange:function(t,e){this.currentPage=t,e&&(this.pageSize=e),this.updateCurrentPagePlugins(),window.scrollTo({top:0,behavior:"smooth"})},handlePageSizeChange:function(t,e){this.currentPage=t,this.pageSize=e,this.updateCurrentPagePlugins()},handlePluginUse:function(t){Object(p["j"])(t)?this.$router.push("/market/plugin/".concat(t.id)):this.$notification.error({message:"插件数据异常",description:"无法查看详情",placement:"topRight"})},handlePluginDetail:function(t){t&&t.id?this.$router.push("/market/plugin/".concat(t.id)):this.$notification.error({message:"插件数据异常",description:"无法查看详情",placement:"topRight"})},handleRetry:function(){this.error=null,this.loadPlugins()},getSubPluginPriceText:function(t){var e=t.neednum,a=1===t.isSvipFree||"1"===t.isSvipFree;return!e||e<=0?"免费":a?"SVIP免费，低至¥".concat(e,"/次"):"低至¥".concat(e,"/次")},clearAllFilters:function(){this.currentFilters={category:"",keyword:"",priceRange:"",sortType:"default",author:""},this.searchKeyword="",this.customPriceMin=null,this.customPriceMax=null,this.currentPage=1,this.clearMarketState(),this.$refs.categoryFilter&&this.$refs.categoryFilter.resetCategory(),this.$refs.searchFilter&&this.$refs.searchFilter.resetFilters(),this.applyFilters(),this.$notification.info({message:"筛选已清空",description:"已清空所有筛选条件",placement:"topRight"})},handleSearchInput:function(){this.currentFilters.keyword=this.searchKeyword,this.currentPage=1,this.applyFilters()},handleSearch:function(){this.currentFilters.keyword=this.searchKeyword,this.currentPage=1,this.applyFilters(),this.searchKeyword&&this.$notification.success({message:"搜索执行中",description:'正在搜索"'.concat(this.searchKeyword,'"相关插件'),placement:"topRight"})},clearSearchInput:function(){this.searchKeyword="",this.currentFilters.keyword="",this.currentPage=1,this.applyFilters(),this.$notification.info({message:"搜索已清空",description:"已清空搜索关键词",placement:"topRight"})},selectCategory:function(t){this.currentFilters.category=t,this.currentPage=1,this.applyFilters()},selectPriceRange:function(t){this.currentFilters.priceRange=t,this.currentPage=1,this.customPriceMin=null,this.customPriceMax=null,this.applyFilters()},selectSort:function(t){this.currentFilters.sortType=t,this.currentPage=1,this.applyFilters()},getCategoryIcon:function(t){var e={"ai-chat":"💬","ai-image":"🎨","ai-video":"🎬","ai-audio":"🎵","social-share":"📱",tools:"⚙️",entertainment:"🎮",combine:"🔗",other:"🔧","内容生成":"✍️","图片生成":"🎨","视频处理":"🎬","数据分析":"📊","开发工具":"⚙️","设计创意":"🎭","营销工具":"📈","AI对话":"💬","AI绘画":"🎨","AI视频":"🎬","AI音频":"🎵","社交分享":"📱","工具类":"⚙️","娱乐":"🎮","组合插件":"🔗","其他":"🔧"};return e[t]||"🔧"},getCategoryText:function(t){var e=this.categories.find((function(e){return e.value===t}));return e?e.text:t},getPriceRangeText:function(t){var e={"0-1":"¥0 - ¥1","1-5":"¥1 - ¥5","5+":"¥5以上"};if(e[t])return e[t];if(t&&t.includes("-")){var a=t.split("-"),i=v(a,2),s=i[0],n=i[1];return"999"===n?"¥".concat(s,"以上"):"¥".concat(s," - ¥").concat(n)}return t},getSortTypeText:function(t){var e={newest:"最新发布","price-asc":"价格从低到高","price-desc":"价格从高到低","name-asc":"名称A-Z"};return e[t]||t},clearCategoryFilter:function(){this.currentFilters.category="",this.currentPage=1,this.applyFilters()},clearKeywordFilter:function(){this.currentFilters.keyword="",this.searchKeyword="",this.currentPage=1,this.applyFilters()},clearPriceFilter:function(){this.currentFilters.priceRange="",this.showCustomPrice=!1,this.customPriceMin=null,this.customPriceMax=null,this.currentPage=1,this.applyFilters()},clearSortFilter:function(){this.currentFilters.sortType="default",this.currentPage=1,this.applyFilters()},toggleSection:function(t){this.collapsedSections[t]=!this.collapsedSections[t]},applyCustomPrice:function(){if(null!==this.customPriceMin||null!==this.customPriceMax){var t=this.customPriceMin||0,e=this.customPriceMax||999;this.currentFilters.priceRange="".concat(t,"-").concat(e),this.currentPage=1,this.applyFilters(),this.$notification.success({message:"价格筛选已应用",description:"已应用价格范围：¥".concat(t," - ").concat(999===e?"以上":"¥"+e),placement:"topRight"})}},toggleCustomPrice:function(){this.showCustomPrice=!this.showCustomPrice,this.showCustomPrice||this.currentFilters.priceRange&&this.currentFilters.priceRange.includes("-")&&!["0-1","1-5","5+"].includes(this.currentFilters.priceRange)&&(this.currentFilters.priceRange="",this.applyFilters())},saveMarketState:function(t){try{var e={category:t.category||"",search:t.keyword||this.searchKeyword||"",priceRange:t.priceRange||"",sortBy:t.sortType||"default",timestamp:Date.now()};localStorage.setItem("market_filter_state",JSON.stringify(e))}catch(a){}},restoreMarketState:function(){try{var t=localStorage.getItem("market_filter_state");if(!t)return;var e=JSON.parse(t),a=Date.now(),i=a-(e.timestamp||0),s=864e5;if(i>s)return void localStorage.removeItem("market_filter_state");e.category&&(this.currentFilters.category=e.category),e.search&&(this.currentFilters.keyword=e.search,this.searchKeyword=e.search),e.priceRange&&(this.currentFilters.priceRange=e.priceRange),e.sortBy&&"default"!==e.sortBy&&(this.currentFilters.sortType=e.sortBy)}catch(n){localStorage.removeItem("market_filter_state")}},clearMarketState:function(){try{localStorage.removeItem("market_filter_state")}catch(t){}},viewCombinedPluginDetails:function(){var t=I(r.a.mark((function t(e){var a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.selectedCombinedPlugin=e,this.showCombinedModal=!0,this.combinedModalLoading=!0,this.combinedSubPlugins=[],document.body.style.overflow="hidden";try{a=this.getSubPluginsFromOriginalData(e.combinedName),this.combinedSubPlugins=a}catch(i){this.$notification.error({message:"加载失败",description:"获取子插件列表失败",placement:"topRight"})}finally{this.combinedModalLoading=!1}case 7:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleSelectSubPlugin:function(t){this.showCombinedModal=!1,this.$router.push({name:"PluginDetail",params:{id:t.id}})},closeCombinedModal:function(){this.showCombinedModal=!1,this.selectedCombinedPlugin=null,this.combinedSubPlugins=[],document.body.style.overflow=""},selectSubPlugin:function(t){this.closeCombinedModal(),this.$router.push({name:"PluginDetail",params:{id:t.id}})},getPluginImage:function(t){return Object(p["e"])(t,this.defaultPluginImage)},goToJianYingDraft:function(){this.$router.push("/JianYingDraft")},hideJianYingFloat:function(){this.showJianYingFloat=!1}}},O=A,j=(a("a83e"),a("2877")),R=Object(j["a"])(O,i,s,!1,null,"45202a92",null);e["default"]=R.exports},"67fb":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"market-main"},[a("section",{staticClass:"page-header"},[a("div",{staticClass:"container"},[a("div",{staticClass:"header-content"},[a("h1",{staticClass:"page-title"},[t._v("插件商城")]),a("p",{staticClass:"page-subtitle"},[t._v("发现和使用优质AI插件，提升您的创作效率")]),a("div",{staticClass:"header-stats"},[a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-number"},[t._v(t._s(t.totalPlugins))]),a("span",{staticClass:"stat-label"},[t._v("个插件")])]),a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-number"},[t._v(t._s(t.categories.length))]),a("span",{staticClass:"stat-label"},[t._v("个分类")])]),a("div",{staticClass:"stat-item"},[a("span",{staticClass:"stat-number"},[t._v(t._s(t.onlineUsers))]),a("span",{staticClass:"stat-label"},[t._v("在线用户")])])])])])]),a("section",{staticClass:"category-section"},[a("CategoryFilter",{ref:"categoryFilter",attrs:{"category-counts":t.categoryCounts,"total-count":t.totalPlugins},on:{"category-change":t.handleCategoryChange}})],1),a("section",{staticClass:"search-section"},[a("SearchFilter",{ref:"searchFilter",attrs:{"show-stats":!0,"result-count":t.filteredPlugins.length,"total-count":t.totalPlugins},on:{"search-change":t.handleSearchChange,"filter-change":t.handleFilterChange}})],1),a("section",{staticClass:"plugins-section"},[a("div",{staticClass:"container"},[t.hasActiveFilters?a("div",{staticClass:"filter-summary"},[a("div",{staticClass:"summary-content"},[a("a-icon",{attrs:{type:"filter"}}),a("span",[t._v("当前筛选条件：")]),t.currentFilters.category?a("a-tag",{attrs:{color:"blue",closable:""},on:{close:t.clearCategoryFilter}},[t._v("\n              "+t._s(t.getCategoryText(t.currentFilters.category))+"\n            ")]):t._e(),t.currentFilters.keyword?a("a-tag",{attrs:{color:"green",closable:""},on:{close:t.clearKeywordFilter}},[t._v("\n              关键词: "+t._s(t.currentFilters.keyword)+"\n            ")]):t._e(),t.currentFilters.priceRange?a("a-tag",{attrs:{color:"orange",closable:""},on:{close:t.clearPriceFilter}},[t._v("\n              价格: "+t._s(t.currentFilters.priceRange)+"\n            ")]):t._e(),t.currentFilters.author?a("a-tag",{attrs:{color:"purple",closable:""},on:{close:t.clearAuthorFilter}},[t._v("\n              创作者: "+t._s(t.currentFilters.author)+"\n            ")]):t._e(),a("a-button",{attrs:{type:"link",size:"small"},on:{click:t.clearAllFilters}},[t._v("\n              清空所有筛选\n            ")])],1)]):t._e(),a("PluginGrid",{attrs:{plugins:t.currentPagePlugins,loading:t.loading,"loading-more":t.loadingMore,error:t.error,"loading-text":t.loadingText},on:{"plugin-use":t.handlePluginUse,"plugin-detail":t.handlePluginDetail,retry:t.handleRetry,"reset-filters":t.handleResetFilters,"clear-filters":t.clearAllFilters,"browse-all":t.handleBrowseAll}}),a("div",{staticClass:"temp-pagination",staticStyle:{padding:"2rem 0","text-align":"center"}},[a("a-pagination",{attrs:{current:t.currentPage,total:t.filteredPlugins.length,"page-size":t.pageSize,"page-size-options":t.pageSizeOptions,"show-size-changer":!0,"show-quick-jumper":!0},on:{change:t.handlePageChange,showSizeChange:t.handlePageSizeChange}})],1)],1)])])])},s=[],n=a("a34a"),r=a.n(n),o=a("df7c"),c=a("0932"),l=a("9027e"),u=a("9810"),d=a("4124"),p=a("8b05");function g(t,e){return f(t)||v(t,e)||P(t,e)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],i=!0,s=!1,n=void 0;try{for(var r,o=t[Symbol.iterator]();!(i=(r=o.next()).done);i=!0)if(a.push(r.value),e&&a.length===e)break}catch(c){s=!0,n=c}finally{try{i||null==o["return"]||o["return"]()}finally{if(s)throw n}}return a}}function f(t){if(Array.isArray(t))return t}function m(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function y(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?m(Object(a),!0).forEach((function(e){b(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):m(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function b(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function C(t){return k(t)||w(t)||P(t)||_()}function _(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function P(t,e){if(t){if("string"===typeof t)return x(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?x(t,e):void 0}}function w(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function k(t){if(Array.isArray(t))return x(t)}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function S(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function F(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){S(n,i,s,r,o,"next",t)}function o(t){S(n,i,s,r,o,"throw",t)}r(void 0)}))}}var T={name:"MarketMain",components:{WebsitePage:o["default"],CategoryFilter:c["default"],SearchFilter:l["default"],PluginGrid:u["default"]},data:function(){return{allPlugins:[],filteredPlugins:[],currentPagePlugins:[],categories:[],categoryCounts:{},currentPage:1,pageSize:12,pageSizeOptions:[8,12,16,24,32],currentFilters:{category:"",keyword:"",priceRange:"",sortType:"default",author:""},loading:!1,loadingMore:!1,error:null,loadingText:"正在加载插件...",totalPlugins:0,onlineUsers:1234}},computed:{hasActiveFilters:function(){return!!(this.currentFilters.category||this.currentFilters.keyword||this.currentFilters.priceRange||this.currentFilters.author||"default"!==this.currentFilters.sortType)}},created:function(){var t=F(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.initializeMarket();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{initializeMarket:function(){var t=F(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading=!0,this.loadingText="正在初始化商城...",t.prev=2,t.next=5,Promise.all([this.loadCategories(),this.loadPlugins()]);case 5:t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](2),this.error="商城初始化失败，请刷新页面重试";case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[2,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}(),loadCategories:function(){var t=F(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,d["a"].getPluginCategories();case 3:e=t.sent,e.success&&(this.categories=Object(p["b"])(e.result||[])),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadPlugins:function(){var t=F(r.a.mark((function t(){var e,a,i;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loadingText="正在加载插件数据...",e=this.buildApiParams(),t.next=5,d["a"].getPluginList(e);case 5:if(a=t.sent,!a.success){t.next=15;break}i=a.result||a.data,this.allPlugins=i.records||i||[],this.totalPlugins=i.total||this.allPlugins.length,this.calculateCategoryCounts(),this.applyFilters(),t.next=16;break;case 15:throw new Error(a.message||"获取插件数据失败");case 16:t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](0),this.error=t.t0.message||"加载插件数据失败";case 22:case"end":return t.stop()}}),t,this,[[0,18]])})));function e(){return t.apply(this,arguments)}return e}(),buildApiParams:function(){var t={pageNo:1,pageSize:1e3,status:1};if(this.currentFilters.category&&(t.plubCategory=this.currentFilters.category),this.currentFilters.keyword&&(t.plubname=this.currentFilters.keyword),this.currentFilters.author&&(t.plubwrite=this.currentFilters.author),this.currentFilters.priceRange){var e=this.parsePriceRange(this.currentFilters.priceRange);e&&(void 0!==e.min&&(t.neednum_begin=e.min),void 0!==e.max&&(t.neednum_end=e.max))}if("default"!==this.currentFilters.sortType){var a=this.getSortConfig(this.currentFilters.sortType);t.column=a.column,t.order=a.order}return t},calculateCategoryCounts:function(){var t=this;this.categoryCounts={},this.categories.forEach((function(e){t.categoryCounts[e.value]=0})),this.allPlugins.forEach((function(e){e.plubCategory&&t.categoryCounts.hasOwnProperty(e.plubCategory)&&t.categoryCounts[e.plubCategory]++}))},applyFilters:function(){var t=this,e=C(this.allPlugins);if(this.currentFilters.category&&(e=e.filter((function(e){return e.plubCategory===t.currentFilters.category}))),this.currentFilters.keyword){var a=this.currentFilters.keyword.toLowerCase();e=e.filter((function(t){return t.plubname&&t.plubname.toLowerCase().includes(a)}))}if(this.currentFilters.priceRange){var i=this.parsePriceRange(this.currentFilters.priceRange);i&&(e=e.filter((function(t){var e=parseFloat(t.neednum||0);return!(void 0!==i.min&&e<i.min)&&!(void 0!==i.max&&e>i.max)})))}this.currentFilters.author&&(e=e.filter((function(e){return e.plubwrite===t.currentFilters.author}))),"default"!==this.currentFilters.sortType&&(e=this.sortPlugins(e,this.currentFilters.sortType)),this.filteredPlugins=e,this.updateCurrentPagePlugins()},updateCurrentPagePlugins:function(){var t=(this.currentPage-1)*this.pageSize,e=t+this.pageSize;this.currentPagePlugins=this.filteredPlugins.slice(t,e)},handleCategoryChange:function(t){this.currentFilters.category=t.category,this.currentPage=1,this.applyFilters()},handleSearchChange:function(t){this.currentFilters.keyword=t.keyword,this.currentPage=1,this.applyFilters()},handleFilterChange:function(t){this.currentFilters=y(y({},this.currentFilters),t),this.currentPage=1,this.applyFilters()},handlePageChange:function(t,e){this.currentPage=t,e&&(this.pageSize=e),this.updateCurrentPagePlugins(),this.scrollToTop()},handlePageSizeChange:function(t,e){this.currentPage=t,this.pageSize=e,this.updateCurrentPagePlugins()},handlePluginUse:function(t){if(Object(p["j"])(t)){var e=this.$store.getters.userInfo;return e?e.accountBalance<t.neednum?(this.$message.warning("余额不足，请先充值"),void this.$router.push("/usercenter?tab=credits")):void this.callPlugin(t):(this.$message.warning("请先登录"),void this.$router.push("/login"))}this.$message.error("插件数据异常")},callPlugin:function(){var t=F(r.a.mark((function t(e){var a,i;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={pluginKey:e.pluginKey},t.next=4,d["a"].usePlugin(a);case 4:if(i=t.sent,!i.success){t.next=9;break}this.$message.success("插件调用成功"),t.next=10;break;case 9:throw new Error(i.message||"插件调用失败");case 10:t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](0),this.$message.error("插件调用失败："+t.t0.message);case 16:case"end":return t.stop()}}),t,this,[[0,12]])})));function e(e){return t.apply(this,arguments)}return e}(),handlePluginDetail:function(t){this.$router.push("/market/plugin/".concat(t.pluginKey))},handleRetry:function(){this.error=null,this.loadPlugins()},handleResetFilters:function(){this.clearAllFilters()},handleBrowseAll:function(){this.clearAllFilters()},clearAllFilters:function(){this.currentFilters={category:"",keyword:"",priceRange:"",sortType:"default",author:""},this.currentPage=1,this.$refs.categoryFilter&&this.$refs.categoryFilter.resetCategory(),this.$refs.searchFilter&&this.$refs.searchFilter.resetFilters(),this.applyFilters(),this.$message.info("已清空所有筛选条件")},clearCategoryFilter:function(){this.currentFilters.category="",this.currentPage=1,this.applyFilters()},clearKeywordFilter:function(){this.currentFilters.keyword="",this.currentPage=1,this.applyFilters()},clearPriceFilter:function(){this.currentFilters.priceRange="",this.currentPage=1,this.applyFilters()},clearAuthorFilter:function(){this.currentFilters.author="",this.currentPage=1,this.applyFilters()},getCategoryText:function(t){var e=this.categories.find((function(e){return e.value===t}));return e?e.text:t},parsePriceRange:function(t){if(!t)return null;if("5+"===t)return{min:5};var e=t.split("-").map(Number),a=g(e,2),i=a[0],s=a[1];return{min:i,max:s}},getSortConfig:function(t){var e={"price-asc":{column:"neednum",order:"asc"},"price-desc":{column:"neednum",order:"desc"},newest:{column:"createTime",order:"desc"},"name-asc":{column:"plubname",order:"asc"},"name-desc":{column:"plubname",order:"desc"}};return e[t]||{column:"sortOrder",order:"asc"}},sortPlugins:function(t,e){var a=C(t);switch(e){case"price-asc":return a.sort((function(t,e){return(parseFloat(t.neednum)||0)-(parseFloat(e.neednum)||0)}));case"price-desc":return a.sort((function(t,e){return(parseFloat(e.neednum)||0)-(parseFloat(t.neednum)||0)}));case"name-asc":return a.sort((function(t,e){return(t.plubname||"").localeCompare(e.plubname||"")}));case"name-desc":return a.sort((function(t,e){return(e.plubname||"").localeCompare(t.plubname||"")}));case"newest":return a.sort((function(t,e){return new Date(e.createTime||0)-new Date(t.createTime||0)}));default:return a}},scrollToTop:function(){window.scrollTo({top:0,behavior:"smooth"})}}},I=T,A=(a("561b"),a("2877")),O=Object(A["a"])(I,i,s,!1,null,"4b306db3",null);e["default"]=O.exports},"68f0":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"author-info"},[a("div",{staticClass:"author-card"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"user"}}),t._v("\n      创作者信息\n    ")],1),t.author&&t.author.authorname?a("div",{staticClass:"author-content"},[a("div",{staticClass:"author-header"},[a("div",{staticClass:"author-avatar"},[a("a-avatar",{style:{backgroundColor:t.getAvatarColor(t.author.authorname)},attrs:{size:80,src:t.author.avatar||t.defaultAvatar}},[t._v("\n            "+t._s(t.getAvatarText(t.author.authorname))+"\n          ")])],1),a("div",{staticClass:"author-basic"},[a("h4",{staticClass:"author-name"},[t._v(t._s(t.author.authorname))]),a("p",{staticClass:"author-title"},[t._v(t._s(t.getAuthorTitle()))]),a("div",{staticClass:"author-stats"},[a("div",{staticClass:"stat-item"},[a("a-icon",{attrs:{type:"appstore"}}),a("span",{staticClass:"stat-label"},[t._v("创作插件：")]),a("span",{staticClass:"stat-value"},[t._v(t._s(t.pluginCount)+" 个")])],1),t.author.plubusenum?a("div",{staticClass:"stat-item"},[a("a-icon",{attrs:{type:"fire"}}),a("span",{staticClass:"stat-label"},[t._v("总使用量：")]),a("span",{staticClass:"stat-value"},[t._v(t._s(t.formatNumber(t.author.plubusenum))+" 次")])],1):t._e(),t.author.totalIncome?a("div",{staticClass:"stat-item"},[a("a-icon",{attrs:{type:"trophy"}}),a("span",{staticClass:"stat-label"},[t._v("累计收益：")]),a("span",{staticClass:"stat-value"},[t._v(t._s(t.formatNumber(t.author.totalIncome))+" 点数")])],1):t._e()])])]),t.author.createinfo?a("div",{staticClass:"author-description"},[a("h5",{staticClass:"description-title"},[a("a-icon",{attrs:{type:"file-text"}}),t._v("\n          个人简介\n        ")],1),a("p",{staticClass:"description-text"},[t._v(t._s(t.author.createinfo))])]):t._e(),a("div",{staticClass:"author-expertise"},[a("h5",{staticClass:"expertise-title"},[a("a-icon",{attrs:{type:"star"}}),t._v("\n          专业领域\n        ")],1),a("div",{staticClass:"expertise-tags"},t._l(t.expertiseList,(function(e,i){return a("a-tag",{key:i,staticClass:"expertise-tag",attrs:{color:t.getExpertiseColor(i)}},[t._v("\n            "+t._s(e)+"\n          ")])})),1)]),t.badges.length>0?a("div",{staticClass:"author-badges"},[a("h5",{staticClass:"badges-title"},[a("a-icon",{attrs:{type:"crown"}}),t._v("\n          成就徽章\n        ")],1),a("div",{staticClass:"badges-list"},t._l(t.badges,(function(e,i){return a("div",{key:i,staticClass:"badge-item"},[a("a-icon",{staticClass:"badge-icon",attrs:{type:e.icon}}),a("span",{staticClass:"badge-text"},[t._v(t._s(e.text))])],1)})),0)]):t._e()]):a("div",{staticClass:"no-author"},[a("a-empty",{attrs:{description:"暂无创作者信息"}},[a("a-button",{attrs:{type:"dashed"},on:{click:t.suggestAuthor}},[t._v("\n          建议完善信息\n        ")])],1)],1)])])},s=[],n={name:"AuthorInfo",props:{author:{type:Object,default:function(){return{}}},pluginCount:{type:Number,default:0}},data:function(){return{defaultAvatar:"/default-avatar.jpg"}},computed:{expertiseList:function(){return this.author.expertise_dictText?this.author.expertise_dictText.split(",").map((function(t){return t.trim()})):["AI技术","内容生成","工具开发"]},badges:function(){var t=[];return this.pluginCount>=10?t.push({icon:"crown",text:"资深开发者"}):this.pluginCount>=5&&t.push({icon:"star",text:"活跃开发者"}),this.author.plubusenum>=1e4?t.push({icon:"fire",text:"热门创作者"}):this.author.plubusenum>=1e3&&t.push({icon:"like",text:"受欢迎创作者"}),this.author.totalIncome>=1e3&&t.push({icon:"trophy",text:"优秀创作者"}),t}},methods:{getAvatarText:function(t){return t?t.charAt(0).toUpperCase():"U"},getAvatarColor:function(t){if(!t)return"#1890ff";var e=["#f56a00","#7265e6","#ffbf00","#00a2ae","#1890ff","#722ed1","#eb2f96","#52c41a"],a=t.charCodeAt(0)%e.length;return e[a]},getExpertiseColor:function(t){var e=["blue","green","orange","purple","cyan","red"];return e[t%e.length]},formatNumber:function(t){return t?t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString():"0"},formatDate:function(t){return t?new Date(t).toLocaleDateString("zh-CN"):"未知"},contactAuthor:function(){this.$message.info("联系功能开发中，敬请期待")},followAuthor:function(){this.$message.success("关注功能开发中，敬请期待")},suggestAuthor:function(){this.$message.info("建议创作者完善个人信息")},getAuthorTitle:function(){return this.author.title_dictText?this.author.title_dictText:this.author.title?this.author.title:"资深AI开发者"}}},r=n,o=(a("1a7f"),a("2877")),c=Object(o["a"])(r,i,s,!1,null,"dbaf8302",null);e["default"]=c.exports},"6b76":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("WebsitePage",[a("div",{staticClass:"plugin-detail-page"},[t.loading?a("div",{staticClass:"loading-container"},[a("div",{staticClass:"loading-content"},[a("a-spin",{attrs:{size:"large",tip:"加载插件详情中..."}},[a("div",{staticClass:"loading-skeleton"},[a("div",{staticClass:"skeleton-header"},[a("div",{staticClass:"skeleton-breadcrumb"}),a("div",{staticClass:"skeleton-plugin-info"},[a("div",{staticClass:"skeleton-image"}),a("div",{staticClass:"skeleton-details"},[a("div",{staticClass:"skeleton-title"}),a("div",{staticClass:"skeleton-description"}),a("div",{staticClass:"skeleton-meta"},[a("div",{staticClass:"skeleton-tag"}),a("div",{staticClass:"skeleton-tag"}),a("div",{staticClass:"skeleton-tag"})])])])]),a("div",{staticClass:"skeleton-tabs"},[a("div",{staticClass:"skeleton-tab-bar"},[a("div",{staticClass:"skeleton-tab"}),a("div",{staticClass:"skeleton-tab"}),a("div",{staticClass:"skeleton-tab"}),a("div",{staticClass:"skeleton-tab"})]),a("div",{staticClass:"skeleton-tab-content"})])])])],1)]):t.pluginDetail?a("div",{staticClass:"plugin-detail-content"},[a("div",{staticClass:"navigation-bar"},[a("div",{staticClass:"nav-left"},[a("a-button",{staticClass:"back-button",attrs:{type:"text",size:"large"},on:{click:t.goBack}},[a("a-icon",{attrs:{type:"arrow-left"}})],1),a("div",{staticClass:"breadcrumb-path"},[a("span",{staticClass:"path-item",on:{click:function(e){return t.$router.push("/")}}},[a("a-icon",{attrs:{type:"home"}}),t._v("\n            首页\n          ")],1),a("a-icon",{staticClass:"path-separator",attrs:{type:"right"}}),a("span",{staticClass:"path-item",on:{click:t.goBackToMarket}},[a("a-icon",{attrs:{type:"shop"}}),t._v("\n            商城\n          ")],1),a("a-icon",{staticClass:"path-separator",attrs:{type:"right"}}),a("span",{staticClass:"path-current"},[t._v("\n            "+t._s(t.pluginDetail.plubname||"插件详情")+"\n          ")])],1)],1),a("div",{staticClass:"nav-right"},[a("a-button",{staticClass:"share-button",on:{click:t.sharePlugin}},[a("a-icon",{attrs:{type:"share-alt"}}),t._v("\n          分享\n        ")],1)],1)]),a("div",{staticClass:"plugin-header"},[a("div",{staticClass:"plugin-header-content"},[a("div",{staticClass:"plugin-image"},[a("img",{attrs:{src:t.getPluginImage(t.pluginDetail),alt:t.pluginDetail.plubname},on:{error:t.handleImageError}})]),a("div",{staticClass:"plugin-info"},[a("h1",{staticClass:"plugin-title"},[t._v(t._s(t.pluginDetail.plubname))]),a("p",{staticClass:"plugin-description"},[t._v(t._s(t.pluginDetail.plubinfo))]),a("div",{staticClass:"plugin-meta"},[a("div",{staticClass:"meta-row"},[a("div",{staticClass:"meta-item"},[a("a-tag",{attrs:{color:t.getCategoryColor(t.pluginDetail.plubCategory),size:"large"}},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n                  "+t._s(t.categoryText)+"\n                ")],1)],1),a("div",{staticClass:"meta-item"},[a("a-tag",{attrs:{color:t.getStatusColor(t.pluginDetail.status),size:"large"}},[a("a-icon",{attrs:{type:"check-circle"}}),t._v("\n                  "+t._s(t.getStatusText(t.pluginDetail.status))+"\n                ")],1)],1)]),a("div",{staticClass:"meta-row"},[a("div",{staticClass:"meta-item"},[a("a-icon",{staticClass:"meta-icon",attrs:{type:"user"}}),a("span",{staticClass:"meta-label"},[t._v("创作者：")]),a("span",{staticClass:"meta-value"},[t._v(t._s(t.authorInfo.authorname||"未知"))])],1),a("div",{staticClass:"meta-item price-item"},[a("a-icon",{staticClass:"meta-icon price-icon",attrs:{type:"dollar"}}),a("span",{staticClass:"meta-label"},[t._v("价格：")]),a("span",{staticClass:"meta-value price-value"},[t._v(t._s(t.getDetailPriceText()))])],1),t.hasTutorial?a("div",{staticClass:"meta-item tutorial-item"},[a("span",{staticClass:"tutorial-hint",on:{click:t.goToTutorial}},[t._v("\n                  本插件有教程视频，详细请点此观看\n                ")])]):t._e()]),a("div",{staticClass:"meta-row"},[a("div",{staticClass:"meta-item"},[a("a-icon",{staticClass:"meta-icon",attrs:{type:"calendar"}}),a("span",{staticClass:"meta-label"},[t._v("发布时间：")]),a("span",{staticClass:"meta-value"},[t._v(t._s(t.formatDate(t.pluginDetail.createTime)))])],1),a("div",{staticClass:"meta-item"},[a("a-icon",{staticClass:"meta-icon",attrs:{type:"sync"}}),a("span",{staticClass:"meta-label"},[t._v("更新时间：")]),a("span",{staticClass:"meta-value"},[t._v(t._s(t.formatDate(t.pluginDetail.updateTime)))])],1)])])])])]),a("div",{staticClass:"plugin-tabs-container"},[a("div",{staticClass:"custom-tabs-nav"},[a("div",{staticClass:"tabs-nav-wrapper"},t._l(t.tabList,(function(e,i){return a("div",{key:e.key,class:["tab-item",{active:t.activeTab===e.key}],on:{click:function(a){return t.handleTabClick(e.key)}}},[a("i",{class:e.icon}),a("span",[t._v(t._s(e.label))])])})),0)]),a("div",{staticClass:"custom-tabs-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:"intro"===t.activeTab,expression:"activeTab === 'intro'"}],staticClass:"tab-pane"},[a("plugin-introduction",{attrs:{content:t.pluginDetail.plubContent,info:t.pluginDetail.plubinfo,"plugin-name":t.pluginDetail.plubname,"plugin-detail":t.pluginDetail}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"tutorial"===t.activeTab,expression:"activeTab === 'tutorial'"}],staticClass:"tab-pane"},[a("plugin-tutorial",{attrs:{"tutorial-link":t.pluginDetail.tutorialLink,"video-file":t.pluginDetail.plubvideo,"plugin-name":t.pluginDetail.plubname,"detailed-content":t.pluginDetail.plubContent}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"features"===t.activeTab,expression:"activeTab === 'features'"}],staticClass:"tab-pane"},[a("plugin-features",{attrs:{"plugin-detail":t.pluginDetail,category:t.pluginDetail.plubCategory}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"tech"===t.activeTab,expression:"activeTab === 'tech'"}],staticClass:"tab-pane"},[a("plugin-technical",{attrs:{"plugin-detail":t.pluginDetail,"plugin-key":t.pluginDetail.pluginKey}})],1)])]),a("div",{staticClass:"author-section"},[a("author-info",{attrs:{author:t.authorInfo,"plugin-count":t.authorPluginCount}})],1),a("div",{staticClass:"recommendations-section"},[a("related-plugins",{attrs:{recommendations:t.recommendations,"current-category":t.pluginDetail.plubCategory,"current-plugin-id":t.pluginDetail.id}})],1)]):a("div",{staticClass:"error-container"},[a("a-result",{attrs:{status:t.errorStatus,title:t.errorTitle,"sub-title":t.errorMessage},scopedSlots:t._u([{key:"extra",fn:function(){return[a("div",{staticClass:"error-actions"},[t.canRetry?a("a-button",{attrs:{type:"primary"},on:{click:t.retryLoad}},[a("a-icon",{attrs:{type:"reload"}}),t._v("\n            重新加载\n          ")],1):t._e(),a("a-button",{on:{click:function(e){return t.$router.push("/market")}}},[a("a-icon",{attrs:{type:"shop"}}),t._v("\n            返回商城\n          ")],1),a("a-button",{attrs:{type:"dashed"},on:{click:t.goHome}},[a("a-icon",{attrs:{type:"home"}}),t._v("\n            返回首页\n          ")],1)],1)]},proxy:!0}])})],1)])])},s=[],n=a("a34a"),r=a.n(n),o=a("8b05"),c=a("4124"),l=a("df7c"),u=a("515e"),d=a("f360"),p=a("2a86"),g=a("11c4"),h=a("68f0"),v=a("4015");function f(t){return C(t)||b(t)||y(t)||m()}function m(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function y(t,e){if(t){if("string"===typeof t)return _(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_(t,e):void 0}}function b(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function C(t){if(Array.isArray(t))return _(t)}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function P(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function w(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){P(n,i,s,r,o,"next",t)}function o(t){P(n,i,s,r,o,"throw",t)}r(void 0)}))}}var k={name:"PluginDetail",components:{WebsitePage:l["default"],PluginIntroduction:u["default"],PluginTutorial:d["default"],PluginFeatures:p["default"],PluginTechnical:g["default"],AuthorInfo:h["default"],RelatedPlugins:v["default"]},data:function(){return{loading:!0,pluginDetail:null,authorInfo:{},authorPluginCount:0,recommendations:[],activeTab:"tutorial",tabList:[{key:"tutorial",label:"使用教程",icon:"anticon anticon-play-circle"},{key:"intro",label:"插件介绍",icon:"anticon anticon-file-text"},{key:"features",label:"功能特点",icon:"anticon anticon-star"},{key:"tech",label:"技术说明",icon:"anticon anticon-code"}],errorStatus:"404",errorTitle:"插件不存在",errorMessage:"抱歉，您访问的插件不存在或已被删除",canRetry:!1,retryCount:0,maxRetries:3,debugMode:!1,defaultPluginImage:"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"}},computed:{pluginId:function(){return this.$route.params.id},hasTutorial:function(){if(!this.pluginDetail)return!1;var t=this.pluginDetail.tutorialLink&&""!==this.pluginDetail.tutorialLink.trim(),e=this.pluginDetail.plubvideo&&""!==this.pluginDetail.plubvideo.trim();return t||e},categoryText:function(){return this.pluginDetail&&this.pluginDetail.plubCategory?this.$categoryService.getCategoryText(this.pluginDetail.plubCategory):"未知分类"}},created:function(){var t=w(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.trackPageView(),t.next=3,this.$categoryService.getCategories();case 3:return t.next=5,this.loadPluginDetail();case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),watch:{$route:function(t,e){t.params.id!==e.params.id&&this.loadPluginDetail()}},methods:{loadPluginDetail:function(){var t=w(r.a.mark((function t(){var e,a,i;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.loading=!0,this.canRetry=!1,this.pluginId){t.next=6;break}return this.handleError("参数错误","插件ID不能为空","400",!1),t.abrupt("return");case 6:return t.next=8,c["a"].getPluginDetail(this.pluginId);case 8:if(e=t.sent,!e.success||!e.result){t.next=20;break}return this.pluginDetail=e.result.plugin,this.authorInfo=e.result.author||{},this.authorPluginCount=this.authorInfo.plubnum||0,this.retryCount=0,this.debugMode,this.pluginDetail.plubname&&(document.title="".concat(this.pluginDetail.plubname," - 插件详情 - 智界AIGC")),t.next=18,this.fetchRecommendations();case 18:t.next=23;break;case 20:a=e.code||500,i=e.message||"获取插件详情失败",404===a?this.handleError("插件不存在","抱歉，您访问的插件不存在或已被删除","404",!1):a>=500?this.handleError("服务器错误","服务器暂时无法响应，请稍后重试","500",!0):this.handleError("加载失败",i,"warning",!0);case 23:t.next=29;break;case 25:t.prev=25,t.t0=t["catch"](0),t.t0.message&&t.t0.message.includes("Network Error")?this.handleError("网络错误","网络连接失败，请检查网络后重试","500",!0):t.t0.message&&t.t0.message.includes("timeout")?this.handleError("请求超时","请求超时，请稍后重试","500",!0):this.handleError("加载失败","加载插件详情时发生未知错误","warning",!0);case 29:return t.prev=29,this.loading=!1,t.finish(29);case 32:case"end":return t.stop()}}),t,this,[[0,25,29,32]])})));function e(){return t.apply(this,arguments)}return e}(),handleError:function(t,e,a,i){this.pluginDetail=null,this.errorTitle=t,this.errorMessage=e,this.errorStatus=a,this.canRetry=i&&this.retryCount<this.maxRetries,i&&this.retryCount<this.maxRetries?this.$message.error("".concat(e,"，可以尝试重新加载")):this.$message.error(e)},retryLoad:function(){var t=w(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!(this.retryCount>=this.maxRetries)){t.next=3;break}return this.$message.warning("重试次数已达上限"),t.abrupt("return");case 3:return this.retryCount++,this.$message.info("正在重试加载... (".concat(this.retryCount,"/").concat(this.maxRetries,")")),t.next=7,this.loadPluginDetail();case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),goHome:function(){this.$router.push("/")},getPluginImage:function(t){return Object(o["e"])(t,this.defaultPluginImage)},handleImageError:function(t){t.target.src=this.defaultPluginImage},formatDate:function(t){return t?new Date(t).toLocaleDateString("zh-CN"):"未知"},formatNumber:function(t){return t?t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString():"0"},getCategoryColor:function(t){return this.$categoryService.getCategoryColor(t)},getStatusColor:function(t){var e={0:"red",1:"green",2:"orange",3:"red"};return e[t]||"default"},getStatusText:function(t){var e={0:"已下架",1:"已上架",2:"审核中",3:"已拒绝"};return e[t]||"未知状态"},onTabChange:function(t){},handleTabClick:function(t){this.activeTab=t},goToTutorial:function(){this.activeTab="tutorial"},getDetailPriceText:function(){var t=this.pluginDetail.neednum,e=1===this.pluginDetail.isSvipFree||"1"===this.pluginDetail.isSvipFree;return!t||t<=0?"免费":e?"SVIP免费，低至¥".concat(t,"/次"):"低至¥".concat(t,"/次")},fetchRecommendations:function(){var t=w(r.a.mark((function t(){var e,a,i,s,n,l,u,d,p,g=this;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,this.pluginDetail&&this.pluginDetail.plubCategory){t.next=5;break}return this.recommendations=[],t.abrupt("return");case 5:return e={pageSize:50,status:1},t.next=8,c["a"].getPluginList(e);case 8:a=t.sent,this.debugMode,a.success&&a.result&&a.result.records?(i=a.result.records,s=i.filter((function(t){return t.id!==g.pluginDetail.id})),n=Object(o["g"])(s),l=this.pluginDetail.plubCategory,u=n.filter((function(t){return t.plubCategory===l})),d=n.filter((function(t){return t.plubCategory!==l})),p=[].concat(f(u.sort((function(){return Math.random()-.5}))),f(d.sort((function(){return Math.random()-.5})))).slice(0,3),this.recommendations=p,this.debugMode):this.recommendations=[],t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](0),this.recommendations=[];case 17:case"end":return t.stop()}}),t,this,[[0,13]])})));function e(){return t.apply(this,arguments)}return e}(),goBack:function(){window.history.length>1&&document.referrer.includes("/market")?this.$router.go(-1):this.goBackToMarket()},goBackToMarket:function(){var t=this.getMarketState();if(t&&Object.keys(t).length>0){var e={};t.category&&(e.category=t.category),t.search&&(e.search=t.search),t.priceRange&&(e.priceRange=t.priceRange),t.sortBy&&(e.sortBy=t.sortBy),this.$router.push({path:"/market",query:e})}else this.$router.push("/market")},getMarketState:function(){try{var t=localStorage.getItem("market_filter_state");return t?JSON.parse(t):{}}catch(e){return{}}},sharePlugin:function(){var t=this,e=window.location.href;navigator.clipboard?navigator.clipboard.writeText(e).then((function(){t.$message.success("插件详情链接已复制到剪贴板")})).catch((function(){t.fallbackCopyToClipboard(e)})):this.fallbackCopyToClipboard(e)},fallbackCopyToClipboard:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select();try{document.execCommand("copy"),this.$message.success("插件详情链接已复制到剪贴板")}catch(a){this.$message.error("复制失败，请手动复制链接")}document.body.removeChild(e)},trackPageView:function(){try{this.pluginId,(new Date).toISOString(),navigator.userAgent,document.referrer}catch(t){}},trackPerformance:function(){try{if(window.performance&&window.performance.timing){var t=window.performance.timing;t.loadEventEnd,t.navigationStart}}catch(e){}},reportError:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{t.message,t.stack,window.location.href,navigator.userAgent,(new Date).toISOString()}catch(a){}}},beforeDestroy:function(){this.retryTimer&&clearTimeout(this.retryTimer)},errorCaptured:function(t,e,a){return this.reportError(t,"组件错误: ".concat(a)),!1}},x=k,S=(a("4c1a"),a("2877")),F=Object(S["a"])(x,i,s,!1,null,"a8f96a74",null);e["default"]=F.exports},"6cdf":function(t,e,a){"use strict";var i=a("06ff"),s=a.n(i);s.a},7014:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-modal",{staticClass:"combined-plugin-modal",attrs:{visible:t.visible,title:t.modalTitle,width:"1200px",footer:null,"z-index":2e3,"mask-closable":!0,"destroy-on-close":!1,"get-container":function(){return t.document.body}},on:{cancel:t.handleCancel}},[a("div",{staticClass:"combined-plugin-header"},[a("div",{staticClass:"combined-info"},[a("div",{staticClass:"combined-image"},[a("img",{attrs:{src:t.getPluginImage(t.combinedPlugin),alt:t.combinedPlugin&&t.combinedPlugin.combinedName},on:{error:t.handleImageError}})]),a("div",{staticClass:"combined-details"},[a("h2",{staticClass:"combined-title"},[t._v(t._s(t.combinedPlugin&&t.combinedPlugin.combinedName))]),a("p",{staticClass:"combined-description"},[t._v(t._s(t.combinedPlugin&&t.combinedPlugin.combinedDescription))]),a("div",{staticClass:"combined-meta"},[a("span",{staticClass:"meta-item"},[a("a-icon",{attrs:{type:"user"}}),t._v("\n            创作者："+t._s(t.combinedPlugin&&t.combinedPlugin.plubwrite_dictText||"未知")+"\n          ")],1),a("span",{staticClass:"meta-item"},[a("a-icon",{attrs:{type:"tag"}}),t._v("\n            分类："+t._s(t.getCategoryText(t.combinedPlugin&&t.combinedPlugin.plubCategory))+"\n          ")],1),a("span",{staticClass:"meta-item"},[a("a-icon",{attrs:{type:"link"}}),t._v("\n            组合插件\n          ")],1)])])])]),a("a-divider",[a("span",{staticClass:"divider-text"},[a("a-icon",{attrs:{type:"appstore"}}),t._v("\n      包含的插件 ("+t._s(t.subPlugins.length)+")\n    ")],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"sub-plugins-container"},[t.loading?a("div",{staticClass:"loading-container"},[a("a-spin",{attrs:{size:"large"}},[a("span",{attrs:{slot:"tip"},slot:"tip"},[t._v("正在加载插件列表...")])])],1):0===t.subPlugins.length?a("div",{staticClass:"empty-container"},[a("a-empty",{attrs:{description:"暂无子插件"}})],1):a("div",{staticClass:"sub-plugins-grid"},t._l(t.subPlugins,(function(e){return a("div",{key:e.id,staticClass:"sub-plugin-card",on:{click:function(a){return t.selectSubPlugin(e)}}},[a("div",{staticClass:"sub-plugin-image"},[a("img",{attrs:{src:t.getPluginImage(e),alt:e.plubname},on:{error:t.handleSubImageError}}),a("div",{staticClass:"plugin-overlay"},[a("a-icon",{staticClass:"view-icon",attrs:{type:"eye"}})],1)]),a("div",{staticClass:"sub-plugin-info"},[a("h4",{staticClass:"sub-plugin-title",attrs:{title:e.plubname}},[t._v("\n            "+t._s(e.plubname)+"\n          ")]),a("p",{staticClass:"sub-plugin-description",attrs:{title:e.plubinfo}},[t._v("\n            "+t._s(t.truncateText(e.plubinfo,60))+"\n          ")]),a("div",{staticClass:"sub-plugin-meta"},[a("span",{staticClass:"category-tag"},[t._v("\n              "+t._s(t.getCategoryText(e.plubCategory))+"\n            ")]),a("span",{staticClass:"price-tag"},[t._v("\n              "+t._s(t.getSubPluginPriceText(e))+"\n            ")])])]),a("div",{staticClass:"sub-plugin-actions"},[a("a-button",{attrs:{type:"primary",size:"small"},on:{click:function(a){return a.stopPropagation(),t.selectSubPlugin(e)}}},[a("a-icon",{attrs:{type:"eye"}}),t._v("\n            查看详情\n          ")],1)],1)])})),0)])],1)},s=[],n=a("a34a"),r=a.n(n),o=a("0fea"),c=a("8b05");function l(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){l(n,i,s,r,o,"next",t)}function o(t){l(n,i,s,r,o,"throw",t)}r(void 0)}))}}var d={name:"CombinedPluginModal",props:{value:{type:Boolean,default:!1},combinedPlugin:{type:Object,default:function(){return{}}}},data:function(){return{visible:!1,loading:!1,subPlugins:[]}},mounted:function(){},computed:{modalTitle:function(){return this.combinedPlugin&&this.combinedPlugin.combinedName?"选择插件 - ".concat(this.combinedPlugin.combinedName):"选择插件"},defaultPluginImage:function(){return"/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"}},watch:{value:{immediate:!0,handler:function(t){this.visible=t,t&&this.combinedPlugin&&this.combinedPlugin.combinedName&&this.loadSubPlugins()}},visible:function(t){this.$emit("input",t)}},methods:{loadSubPlugins:function(){var t=u(r.a.mark((function t(){var e,a;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.combinedPlugin.combinedName){t.next=3;break}return this.$notification.warning({message:"参数错误",description:"组合插件名称不能为空",placement:"topRight"}),t.abrupt("return");case 3:return this.loading=!0,t.prev=4,t.next=8,Object(o["c"])("/plubshop/aigcPlubShop/list",{pageNo:1,pageSize:100,combinedName:this.combinedPlugin.combinedName,isCombined:1,status:1});case 8:e=t.sent,e.success?(a=e.result||e.data,this.subPlugins=a.records||a||[]):(this.$notification.error({message:"加载失败",description:"获取子插件列表失败："+e.message,placement:"topRight"}),this.subPlugins=[]),t.next=17;break;case 12:t.prev=12,t.t0=t["catch"](4),this.$notification.error({message:"系统异常",description:"获取子插件列表异常",placement:"topRight"}),this.subPlugins=[];case 17:return t.prev=17,this.loading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,this,[[4,12,17,20]])})));function e(){return t.apply(this,arguments)}return e}(),selectSubPlugin:function(t){this.$emit("select-sub-plugin",t)},handleCancel:function(){this.visible=!1,this.subPlugins=[]},getPluginImage:function(t){return Object(c["e"])(t,this.defaultPluginImage)},handleImageError:function(t){t.target.src="/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"},handleSubImageError:function(t){t.target.src="/jeecg-boot/sys/common/static/defaults/plugin-default.jpg"},getCategoryText:function(t){return this.$categoryService?this.$categoryService.getCategoryText(t):t||"未知分类"},truncateText:function(t,e){return t?t.length<=e?t:t.substring(0,e)+"...":""},formatPrice:function(t){return null===t||void 0===t?"0":parseFloat(t).toFixed(2)},getSubPluginPriceText:function(t){var e=t.neednum,a=1===t.isSvipFree||"1"===t.isSvipFree;return!e||e<=0?"免费":a?"SVIP免费，低至¥".concat(this.formatPrice(e),"/次"):"低至¥".concat(this.formatPrice(e),"/次")}}},p=d,g=(a("7601"),a("2877")),h=Object(g["a"])(p,i,s,!1,null,"3fa5b7ed",null);e["default"]=h.exports},7601:function(t,e,a){"use strict";var i=a("2652"),s=a.n(i);s.a},"767d":function(t,e,a){"use strict";var i=a("1749"),s=a.n(i);s.a},"76f3":function(t,e,a){"use strict";var i=a("94b9"),s=a.n(i);s.a},8198:function(t,e,a){"use strict";var i=a("e21a"),s=a.n(i);s.a},"8b05":function(t,e,a){"use strict";function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function s(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}function n(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?s(Object(a),!0).forEach((function(e){r(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):s(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function r(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function o(t){return Array.isArray(t)?t.map((function(t){return n({value:t.value,text:t.text||t.label},t)})):[]}function c(t){return!!Array.isArray(t)&&t.every((function(t){return t&&"undefined"!==typeof t.value&&(t.text||t.label)}))}function l(t){try{var e="market_categories_cache",a={data:t,timestamp:Date.now(),expiry:864e5};localStorage.setItem(e,JSON.stringify(a))}catch(i){}}function u(){try{var t="market_categories_cache",e=localStorage.getItem(t);if(!e)return null;var a=JSON.parse(e),i=Date.now();return i-a.timestamp>a.expiry?(localStorage.removeItem(t),null):a.data}catch(s){return null}}function d(t){return t||0===t?parseFloat(t).toFixed(2):"0.00"}function p(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return t?t.length>e?t.substring(0,e)+"...":t:""}function g(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,s="",n=null;if("object"===i(t)&&null!==t?(n=t,s=h(n)):(s=t,n=a),!s||""===s.trim())return e;if(s.startsWith("http"))return s;if(window.getFileAccessHttpUrl)return window.getFileAccessHttpUrl(s);var r=Object({NODE_ENV:"production",VUE_APP_API_BASE_URL:"https://www.aigcview.cn/jeecg-boot",VUE_APP_CAS_BASE_URL:"https://www.aigcview.cn/jeecg-boot/cas",VUE_APP_ONLINE_BASE_URL:"https://www.aigcview.cn/jeecg-boot/onlinePreview",VUE_APP_PLATFORM_NAME:"JeecgBoot 企业级低代码平台",VUE_APP_SSO:"false",BASE_URL:"/"}).VUE_APP_IMG_BASE_URL||"/jeecg-boot/sys/common/static/";return"".concat(r).concat(s)}function h(t){if(!t||"object"!==i(t))return"";if(t.plubimg&&""!==t.plubimg.trim())return t.plubimg;var e=1===t.isCombined||"1"===t.isCombined;return e&&t.combinedImage&&""!==t.combinedImage.trim()?t.combinedImage:""}function v(t){var e=[],a=new Map;t.forEach((function(t){if(1===t.isCombined||"1"===t.isCombined){var i=t.combinedName;i&&(a.has(i)||a.set(i,[]),a.get(i).push(t))}else e.push(t)}));var i=[];a.forEach((function(t,e){var a=Math.floor(Math.random()*t.length),s=t[a];i.push(s)}));var s=[].concat(e,i);return s}function f(t){if(!t||"object"!==i(t))return!1;var e=["plubname","pluginKey"];return e.every((function(e){return t[e]}))}function m(t){var e={"推荐":"status-recommend","热门":"status-hot","新品":"status-new","限时":"status-limited"};return e[t]||"status-default"}a.d(e,"b",(function(){return o})),a.d(e,"i",(function(){return c})),a.d(e,"a",(function(){return l})),a.d(e,"d",(function(){return u})),a.d(e,"c",(function(){return d})),a.d(e,"h",(function(){return p})),a.d(e,"e",(function(){return g})),a.d(e,"g",(function(){return v})),a.d(e,"j",(function(){return f})),a.d(e,"f",(function(){return m}))},"9027e":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"search-filter"},[a("div",{staticClass:"container"},[a("div",{staticClass:"search-section"},[a("div",{staticClass:"search-input-wrapper"},[a("a-input-search",{staticClass:"search-input",attrs:{placeholder:"搜索插件名称...",size:"large",loading:t.searchLoading},on:{search:t.handleSearch,pressEnter:t.handleSearch,change:t.handleSearchChange},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}},[a("a-icon",{attrs:{slot:"prefix",type:"search"},slot:"prefix"})],1)],1)]),a("div",{staticClass:"filter-section"},[a("div",{staticClass:"filter-group"},[a("label",{staticClass:"filter-label"},[t._v("价格范围：")]),a("a-select",{staticStyle:{width:"180px"},attrs:{placeholder:"选择价格区间",allowClear:""},on:{change:t.handlePriceChange},model:{value:t.priceRange,callback:function(e){t.priceRange=e},expression:"priceRange"}},[a("a-select-option",{attrs:{value:""}},[t._v("不限")]),a("a-select-option",{attrs:{value:"0-0.5"}},[t._v("¥0 - ¥0.5")]),a("a-select-option",{attrs:{value:"0.5-1"}},[t._v("¥0.5 - ¥1")]),a("a-select-option",{attrs:{value:"1-2"}},[t._v("¥1 - ¥2")]),a("a-select-option",{attrs:{value:"2-5"}},[t._v("¥2 - ¥5")]),a("a-select-option",{attrs:{value:"5+"}},[t._v("¥5以上")])],1)],1),a("div",{staticClass:"filter-group"},[a("label",{staticClass:"filter-label"},[t._v("排序方式：")]),a("a-select",{staticStyle:{width:"160px"},attrs:{placeholder:"选择排序方式"},on:{change:t.handleSortChange},model:{value:t.sortType,callback:function(e){t.sortType=e},expression:"sortType"}},[a("a-select-option",{attrs:{value:"default"}},[t._v("默认排序")]),a("a-select-option",{attrs:{value:"price-asc"}},[t._v("价格从低到高")]),a("a-select-option",{attrs:{value:"price-desc"}},[t._v("价格从高到低")]),a("a-select-option",{attrs:{value:"newest"}},[t._v("最新发布")]),a("a-select-option",{attrs:{value:"name-asc"}},[t._v("名称A-Z")]),a("a-select-option",{attrs:{value:"name-desc"}},[t._v("名称Z-A")])],1)],1),a("div",{staticClass:"filter-group"},[a("label",{staticClass:"filter-label"},[t._v("创作者：")]),a("a-select",{staticStyle:{width:"160px"},attrs:{placeholder:"选择创作者",allowClear:"",loading:t.authorsLoading},on:{change:t.handleAuthorChange},model:{value:t.selectedAuthor,callback:function(e){t.selectedAuthor=e},expression:"selectedAuthor"}},t._l(t.authors,(function(e){return a("a-select-option",{key:e.value,attrs:{value:e.value}},[t._v("\n            "+t._s(e.text)+"\n          ")])})),1)],1),a("div",{staticClass:"filter-group"},[a("a-button",{attrs:{icon:"reload"},on:{click:t.resetFilters}},[t._v("\n          重置筛选\n        ")])],1)]),t.showStats?a("div",{staticClass:"filter-stats"},[a("span",{staticClass:"stats-text"},[a("a-icon",{attrs:{type:"filter"}}),t._v("\n        "+t._s(t.getFilterStatsText())+"\n      ")],1),t.hasActiveFilters?a("a-button",{attrs:{type:"link",size:"small"},on:{click:t.clearAllFilters}},[t._v("\n        清空所有筛选\n      ")]):t._e()],1):t._e()])])},s=[],n=a("a34a"),r=a.n(n),o=a("2ef0");function c(t,e){return g(t)||p(t,e)||u(t,e)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t,e){if(t){if("string"===typeof t)return d(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,i=new Array(e);a<e;a++)i[a]=t[a];return i}function p(t,e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t)){var a=[],i=!0,s=!1,n=void 0;try{for(var r,o=t[Symbol.iterator]();!(i=(r=o.next()).done);i=!0)if(a.push(r.value),e&&a.length===e)break}catch(c){s=!0,n=c}finally{try{i||null==o["return"]||o["return"]()}finally{if(s)throw n}}return a}}function g(t){if(Array.isArray(t))return t}function h(t,e,a,i,s,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(i,s)}function v(t){return function(){var e=this,a=arguments;return new Promise((function(i,s){var n=t.apply(e,a);function r(t){h(n,i,s,r,o,"next",t)}function o(t){h(n,i,s,r,o,"throw",t)}r(void 0)}))}}var f={name:"SearchFilter",props:{showStats:{type:Boolean,default:!0},resultCount:{type:Number,default:0},totalCount:{type:Number,default:0}},data:function(){return{searchKeyword:"",searchLoading:!1,priceRange:"",sortType:"default",selectedAuthor:"",authors:[],authorsLoading:!1,debouncedSearch:null}},computed:{hasActiveFilters:function(){return!!(this.searchKeyword||this.priceRange||this.selectedAuthor||"default"!==this.sortType)}},created:function(){this.debouncedSearch=Object(o["debounce"])(this.emitSearchChange,500),this.loadAuthors()},methods:{handleSearch:function(){this.emitSearchChange()},handleSearchChange:function(){this.debouncedSearch()},handlePriceChange:function(t){this.emitFilterChange()},handleSortChange:function(t){this.emitFilterChange()},handleAuthorChange:function(t){this.emitFilterChange()},emitSearchChange:function(){this.$emit("search-change",{keyword:this.searchKeyword.trim()})},emitFilterChange:function(){var t={keyword:this.searchKeyword.trim(),priceRange:this.priceRange,sortType:this.sortType,author:this.selectedAuthor};this.$emit("filter-change",t)},resetFilters:function(){this.searchKeyword="",this.priceRange="",this.sortType="default",this.selectedAuthor="",this.$notification.info({message:"筛选重置",description:"筛选条件已重置",placement:"topRight"}),this.emitFilterChange()},clearAllFilters:function(){this.resetFilters()},getFilterStatsText:function(){return this.hasActiveFilters?"找到 ".concat(this.resultCount," 个插件（共 ").concat(this.totalCount," 个）"):"共 ".concat(this.totalCount," 个插件")},loadAuthors: <AUTHORS>