(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~03e26bad"],{"004c":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("page-layout",{attrs:{avatar:t.avatar}},[a("div",{attrs:{slot:"headerContent"},slot:"headerContent"},[a("div",{staticClass:"title"},[t._v(t._s(t.timeFix)+"，"+t._s(t.nickname())),a("span",{staticClass:"welcome-text"},[t._v("，"+t._s(t.welcome()))])]),a("div",[t._v("前端工程师 | 蚂蚁金服 - 某某某事业群 - VUE平台")])]),a("div",{attrs:{slot:"extra"},slot:"extra"},[a("a-row",{staticClass:"more-info"},[a("a-col",{attrs:{span:8}},[a("head-info",{attrs:{title:"项目数",content:"56",center:!1,bordered:!1}})],1),a("a-col",{attrs:{span:8}},[a("head-info",{attrs:{title:"团队排名",content:"8/24",center:!1,bordered:!1}})],1),a("a-col",{attrs:{span:8}},[a("head-info",{attrs:{title:"项目访问",content:"2,223",center:!1}})],1)],1)],1),a("div",[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:16,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"project-list",staticStyle:{"margin-bottom":"24px"},attrs:{loading:t.loading,bordered:!1,title:"进行中的项目","body-style":{padding:0}}},[a("a",{attrs:{slot:"extra"},slot:"extra"},[t._v("全部项目")]),a("div",t._l(t.projects,(function(e,s){return a("a-card-grid",{key:s,staticClass:"project-card-grid"},[a("a-card",{attrs:{bordered:!1,"body-style":{padding:0}}},[a("a-card-meta",[a("div",{staticClass:"card-title",attrs:{slot:"title"},slot:"title"},[a("a-avatar",{attrs:{size:"small",src:e.cover}}),a("a",[t._v(t._s(e.title))])],1),a("div",{staticClass:"card-description",attrs:{slot:"description"},slot:"description"},[t._v("\n                    "+t._s(e.description)+"\n                  ")])]),a("div",{staticClass:"project-item"},[a("a",{attrs:{href:"/#/"}},[t._v("科学搬砖组")]),a("span",{staticClass:"datetime"},[t._v("9小时前")])])],1)],1)})),1)]),a("a-card",{attrs:{loading:t.loading,title:"动态",bordered:!1}},[a("a-list",t._l(t.activities,(function(e,s){return a("a-list-item",{key:s},[a("a-list-item-meta",[a("a-avatar",{attrs:{slot:"avatar",src:e.user.avatar},slot:"avatar"}),a("div",{attrs:{slot:"title"},slot:"title"},[a("span",[t._v(t._s(e.user.nickname))]),t._v(" \n                  在 "),a("a",{attrs:{href:"#"}},[t._v(t._s(e.project.name))]),t._v(" \n                  "),a("span",[t._v(t._s(e.project.action))]),t._v(" \n                  "),a("a",{attrs:{href:"#"}},[t._v(t._s(e.project.event))])]),a("div",{attrs:{slot:"description"},slot:"description"},[t._v(t._s(e.time))])],1)],1)})),1)],1)],1),a("a-col",{staticStyle:{padding:"0 12px"},attrs:{xl:8,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a",[t._v("操作一")]),a("a",[t._v("操作二")]),a("a",[t._v("操作三")]),a("a",[t._v("操作四")]),a("a",[t._v("操作五")]),a("a",[t._v("操作六")]),a("a-button",{attrs:{size:"small",type:"primary",ghost:"",icon:"plus"}},[t._v("添加")])],1)]),a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"XX 指数",loading:t.radarLoading,bordered:!1,"body-style":{padding:0}}},[a("div",{staticStyle:{"min-height":"400px"}},[a("radar",{attrs:{data:t.radarData}})],1)]),a("a-card",{attrs:{loading:t.loading,title:"团队",bordered:!1}},[a("div",{staticClass:"members"},[a("a-row",t._l(t.teams,(function(e,s){return a("a-col",{key:s,attrs:{span:12}},[a("a",[a("a-avatar",{attrs:{size:"small",src:e.avatar}}),a("span",{staticClass:"member"},[t._v(t._s(e.name))])],1)])})),1)],1)])],1)],1)],1)])},r=[],i=a("ca00"),o=a("2f62"),n=a("b445"),l=a("81d1"),c=a("3981"),d=a("0fea");function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,s)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var f=a("7104"),v={name:"Workplace",components:{PageLayout:n["default"],HeadInfo:l["default"],Radar:c["default"]},data:function(){return{timeFix:Object(i["o"])(),avatar:"",user:{},projects:[],loading:!0,radarLoading:!0,activities:[],teams:[],axis1Opts:{dataKey:"item",line:null,tickLine:null,grid:{lineStyle:{lineDash:null},hideFirstLine:!1}},axis2Opts:{dataKey:"score",line:null,tickLine:null,grid:{type:"polygon",lineStyle:{lineDash:null}}},scale:[{dataKey:"score",min:0,max:80}],axisData:[{item:"引用",a:70,b:30,c:40},{item:"口碑",a:60,b:70,c:40},{item:"产量",a:50,b:60,c:40},{item:"贡献",a:40,b:50,c:40},{item:"热度",a:60,b:70,c:40},{item:"引用",a:70,b:50,c:40}],radarData:[]}},computed:{userInfo:function(){return this.$store.getters.userInfo}},created:function(){this.user=this.userInfo,this.avatar=Object(d["d"])(this.userInfo.avatar),Object(d["f"])().then((function(t){})),Object(d["g"])().then((function(t){}))},mounted:function(){this.getProjects(),this.getActivity(),this.getTeams(),this.initRadar()},methods:p(p({},Object(o["c"])(["nickname","welcome"])),{},{getProjects:function(){var t=this;this.$http.get("/mock/api/list/search/projects").then((function(e){t.projects=e.result&&e.result.data,t.loading=!1}))},getActivity:function(){var t=this;this.$http.get("/mock/api/workplace/activity").then((function(e){t.activities=e.result}))},getTeams:function(){var t=this;this.$http.get("/mock/api/workplace/teams").then((function(e){t.teams=e.result}))},initRadar:function(){var t=this;this.radarLoading=!0,this.$http.get("/mock/api/workplace/radar").then((function(e){var a=(new f.View).source(e.result);a.transform({type:"fold",fields:["个人","团队","部门"],key:"user",value:"score"}),t.radarData=a.rows,t.radarLoading=!1}))}})},h=v,y=(a("8925"),a("2877")),b=Object(y["a"])(h,s,r,!1,null,"ab42607a",null);e["default"]=b.exports},"0314":function(t,e,a){"use strict";var s=a("3840"),r=a.n(s);r.a},"0673":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"exception"},[a("div",{staticClass:"img"},[a("img",{attrs:{src:t.config[t.type].img}})]),a("div",{staticClass:"content"},[a("h1",[t._v(t._s(t.config[t.type].title))]),a("div",{staticClass:"desc"},[t._v(t._s(t.config[t.type].desc))]),a("div",{staticClass:"action"},[a("a-button",{staticStyle:{"margin-right":"12px"},attrs:{type:"primary"},on:{click:t.handleToHome}},[t._v("返回后台")]),a("a-button",{on:{click:t.handleToWebsite}},[t._v("返回官网")])],1)])])},r=[],i={403:{img:"https://gw.alipayobjects.com/zos/rmsportal/wZcnGqRDyhPOEYFcZDnb.svg",title:"403",desc:"抱歉，你无权访问该页面"},404:{img:"https://gw.alipayobjects.com/zos/rmsportal/KpnpchXsobRgLElEozzI.svg",title:"404",desc:"抱歉，你访问的页面不存在或无权访问"},500:{img:"https://gw.alipayobjects.com/zos/rmsportal/RVRUAYdCGeYNBWoKiIwB.svg",title:"500",desc:"抱歉，服务器出错了"}},o=i,n={name:"Exception",props:{type:{type:String,default:"404"}},data:function(){return{config:o}},methods:{handleToHome:function(){this.$router.push("/dashboard/analysis")},handleToWebsite:function(){this.$router.push("/")}}},l=n,c=(a("fb08c"),a("2877")),d=Object(c["a"])(l,s,r,!1,null,"0d5803e9",null);e["default"]=d.exports},"0797":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form",{staticStyle:{"max-width":"500px",margin:"40px auto 0"}},[a("a-alert",{staticStyle:{"margin-bottom":"24px"},attrs:{closable:!0,message:"确认转账后，资金将直接打入对方账户，无法退回。"}}),a("a-form-item",{staticClass:"stepFormText",attrs:{label:"付款账户",labelCol:{span:5},wrapperCol:{span:19}}},[t._v("\n      <EMAIL>\n    ")]),a("a-form-item",{staticClass:"stepFormText",attrs:{label:"收款账户",labelCol:{span:5},wrapperCol:{span:19}}},[t._v("\n      <EMAIL>\n    ")]),a("a-form-item",{staticClass:"stepFormText",attrs:{label:"收款人姓名",labelCol:{span:5},wrapperCol:{span:19}}},[t._v("\n      Alex\n    ")]),a("a-form-item",{staticClass:"stepFormText",attrs:{label:"转账金额",labelCol:{span:5},wrapperCol:{span:19}}},[t._v("\n      ￥ 5,000.00\n    ")]),a("a-form-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("a-button",{attrs:{loading:t.loading,type:"primary"},on:{click:t.nextStep}},[t._v("提交")]),a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:t.prevStep}},[t._v("上一步")])],1)],1)],1)},r=[],i={name:"Step2",data:function(){return{loading:!1}},methods:{nextStep:function(){var t=this;t.loading=!0,setTimeout((function(){t.$emit("nextStep")}),1500)},prevStep:function(){this.$emit("prevStep")}}},o=i,n=(a("6e69"),a("2877")),l=Object(n["a"])(o,s,r,!1,null,"7a8374fd",null);e["default"]=l.exports},"0975":function(t,e,a){"use strict";var s=a("fd0e"),r=a.n(s);r.a},"0b56":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t._v("\n  Monitor\n")])},r=[],i={name:"Monitor"},o=i,n=a("2877"),l=Object(n["a"])(o,s,r,!1,null,"262f850e",null);e["default"]=l.exports},"0be2":function(t,e,a){},"0bf1":function(t,e,a){"use strict";var s=a("0be2"),r=a.n(s);r.a},"0d68":function(t,e,a){},"0ec6":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"index-container-ty"},[s("a-spin",{attrs:{spinning:t.loading}},[s("a-row",{attrs:{type:"flex",justify:"start",gutter:3}},[s("a-col",{attrs:{sm:24,lg:12}},[s("a-card",[s("div",{staticClass:"index-md-title",attrs:{slot:"title"},slot:"title"},[s("img",{attrs:{src:a("8cf3")}}),t._v("\n            我的待办【"+t._s(t.dataSource1.length)+"】\n          ")]),s("div",{attrs:{slot:"extra"},slot:"extra"},[t.dataSource1&&t.dataSource1.length>0?s("a",{attrs:{slot:"footer"},on:{click:t.goPage},slot:"footer"},[t._v("更多 "),s("a-icon",{attrs:{type:"double-right"}})],1):t._e()]),s("a-table",{ref:"table1",class:"my-index-table tytable1",attrs:{size:"small",rowKey:"id",columns:t.columns,dataSource:t.dataSource1,pagination:!1},scopedSlots:t._u([{key:"ellipsisText",fn:function(e){return[s("j-ellipsis",{attrs:{value:e,length:t.textMaxLength}})]}},{key:"dayWarnning",fn:function(e,a){return[s("a-icon",{staticStyle:{"font-size":"22px"},attrs:{type:"bulb",theme:"twoTone",twoToneColor:t.getTipColor(a)}})]}},{key:"action",fn:function(e,a){return s("span",{},[s("a",{on:{click:t.handleData}},[t._v("办理")])])}}])})],1)],1),s("a-col",{attrs:{sm:24,lg:12}},[s("a-card",[s("div",{staticClass:"index-md-title",attrs:{slot:"title"},slot:"title"},[s("img",{attrs:{src:a("c612")}}),t._v("\n            我的在办【"+t._s(t.dataSource2.length)+"】\n          ")]),s("div",{attrs:{slot:"extra"},slot:"extra"},[t.dataSource2&&t.dataSource2.length>0?s("a",{attrs:{slot:"footer"},on:{click:t.goPage},slot:"footer"},[t._v("更多 "),s("a-icon",{attrs:{type:"double-right"}})],1):t._e()]),s("a-table",{ref:"table2",class:"my-index-table tytable2",attrs:{size:"small",rowKey:"id",columns:t.columns,dataSource:t.dataSource2,pagination:!1},scopedSlots:t._u([{key:"ellipsisText",fn:function(e){return[s("j-ellipsis",{attrs:{value:e,length:t.textMaxLength}})]}},{key:"dayWarnning",fn:function(e,a){return[s("a-icon",{staticStyle:{"font-size":"22px"},attrs:{type:"bulb",theme:"twoTone",twoToneColor:t.getTipColor(a)}})]}},{key:"action",fn:function(e,a){return s("span",{},[s("a",{on:{click:t.handleData}},[t._v("办理")])])}}])})],1)],1),s("a-col",{attrs:{span:24}},[s("div",{staticStyle:{height:"5px"}})]),s("a-col",{attrs:{sm:24,lg:12}},[s("a-card",[s("div",{staticClass:"index-md-title",attrs:{slot:"title"},slot:"title"},[s("img",{attrs:{src:a("4a27")}}),t._v("\n            我的挂账【"+t._s(t.dataSource4.length)+"】\n          ")]),s("a-table",{ref:"table4",class:"my-index-table tytable4",attrs:{size:"small",rowKey:"id",columns:t.columns,dataSource:t.dataSource4,pagination:!1},scopedSlots:t._u([{key:"ellipsisText",fn:function(e){return[s("j-ellipsis",{attrs:{value:e,length:t.textMaxLength}})]}},{key:"dayWarnning",fn:function(e,a){return[s("a-icon",{staticStyle:{"font-size":"22px"},attrs:{type:"bulb",theme:"twoTone",twoToneColor:t.getTipColor(a)}})]}},{key:"action",fn:function(e,a){return s("span",{},[s("a",{on:{click:t.handleData}},[t._v("办理")])])}}])})],1)],1),s("a-col",{attrs:{sm:24,lg:12}},[s("a-card",[s("div",{staticClass:"index-md-title",attrs:{slot:"title"},slot:"title"},[s("img",{attrs:{src:a("5e65")}}),t._v("\n            我的督办【"+t._s(t.dataSource3.length)+"】\n          ")]),s("a-table",{ref:"table3",class:"my-index-table tytable3",attrs:{size:"small",rowKey:"id",columns:t.columns,dataSource:t.dataSource3,pagination:!1},scopedSlots:t._u([{key:"ellipsisText",fn:function(e){return[s("j-ellipsis",{attrs:{value:e,length:t.textMaxLength}})]}},{key:"dayWarnning",fn:function(e,a){return[s("a-icon",{staticStyle:{"font-size":"22px"},attrs:{type:"bulb",theme:"twoTone",twoToneColor:t.getTipColor(a)}})]}},{key:"action",fn:function(e,a){return s("span",{},[s("a",{on:{click:t.handleData}},[t._v("办理")])])}}])})],1)],1)],1)],1)],1)},r=[],i=a("4099"),o=a.n(i),n=a("d579"),l=[{id:"001",orderNo:"电[1]1267102",orderTitle:"药品出问题了",restDay:1},{id:"002",orderNo:"电[4]5967102",orderTitle:"吃了xxx医院的药，病情越来越严重",restDay:0},{id:"003",orderNo:"电[3]5988987",orderTitle:"今天去超市买鸡蛋，鸡蛋都是坏的",restDay:7},{id:"004",orderNo:"电[2]5213491",orderTitle:"xx宝实体店高价售卖xx",restDay:5},{id:"005",orderNo:"电[1]1603491",orderTitle:"以红利相诱，答应退保后扣一年费用",restDay:0}],c=[{id:"001",orderTitle:"我要投诉这个大超市",orderNo:"电[1]10299456",restDay:6},{id:"002",orderTitle:"xxx医院乱开药方,售卖假药",orderNo:"电[2]20235691",restDay:0},{id:"003",orderTitle:"我想问问这家店是干啥的",orderNo:"电[3]495867322",restDay:7},{id:"004",orderTitle:"我要举报朝阳区奥森公园酒店",orderNo:"电[2]1193849",restDay:3},{id:"005",orderTitle:"我今天吃饭吃到一个石头子",orderNo:"电[4]56782344",restDay:9}],d="rgba(0, 255, 0, 1)",u="rgba(255, 255, 0, 1)",p="rgba(255, 0, 0, 1)",m={name:"IndexTask",components:{JEllipsis:n["default"]},data:function(){return{loading:!1,textMaxLength:8,dataSource1:[],dataSource2:[],dataSource3:[],dataSource4:[],columns:[{title:"",dataIndex:"",key:"rowIndex",width:50,fixed:"left",align:"center",scopedSlots:{customRender:"dayWarnning"}},{title:"剩余天数",align:"center",dataIndex:"restDay",width:80},{title:"工单标题",align:"center",dataIndex:"orderTitle",scopedSlots:{customRender:"ellipsisText"}},{title:"工单编号",align:"center",dataIndex:"orderNo"},{title:"操作",dataIndex:"action",align:"center",scopedSlots:{customRender:"action"}}]}},created:function(){this.mock()},mounted:function(){},methods:{getTipColor:function(t){var e=t.restDay;return e<=0?p:e>=1&&e<4?u:e>=4?d:void 0},goPage:function(){this.$message.success("请根据具体业务跳转页面")},mock:function(){this.dataSource1=l,this.dataSource2=c,this.dataSource3=l,this.dataSource4=[],this.ifNullDataSource(this.dataSource4,".tytable4")},ifNullDataSource:function(t,e){this.$nextTick((function(){if(!t||0==t.length){var a=document.createElement("img");a.src=o.a,a.width=300;var s="".concat(e," .ant-table-placeholder");document.querySelector(s).innerHTML="",document.querySelector(s).appendChild(a)}}))},handleData:function(){this.$message.success("办理完成")}}},f=m,v=(a("84c5"),a("2877")),h=Object(v["a"])(f,s,r,!1,null,null,null);e["default"]=h.exports},"161b":function(t,e,a){},"1d37":function(t,e,a){"use strict";var s=a("e043"),r=a.n(s);r.a},2425:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("page-layout",{attrs:{title:"单号：234231029431",logo:"https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png"}},[s("detail-list",{staticClass:"detail-layout",attrs:{slot:"headerContent",size:"small",col:2},slot:"headerContent"},[s("detail-list-item",{attrs:{term:"创建人"}},[e._v("曲丽丽")]),s("detail-list-item",{attrs:{term:"订购产品"}},[e._v("XX服务")]),s("detail-list-item",{attrs:{term:"创建时间"}},[e._v("2018-08-07")]),s("detail-list-item",{attrs:{term:"关联单据"}},[s("a",[e._v("12421")])]),s("detail-list-item",{attrs:{term:"生效日期"}},[e._v("2018-08-07 ~ 2018-12-11")]),s("detail-list-item",{attrs:{term:"备注"}},[e._v("请于两个工作日内确认")])],1),s("a-row",{staticClass:"status-list",attrs:{slot:"extra"},slot:"extra"},[s("a-col",{attrs:{xs:12,sm:12}},[s("div",{staticClass:"text"},[e._v("状态")]),s("div",{staticClass:"heading"},[e._v("待审批")])]),s("a-col",{attrs:{xs:12,sm:12}},[s("div",{staticClass:"text"},[e._v("订单金额")]),s("div",{staticClass:"heading"},[e._v("¥ 568.08")])])],1),s("template",{slot:"action"},[s("a-button-group",{staticStyle:{"margin-right":"4px"}},[s("a-button",[e._v("操作")]),s("a-button",[e._v("操作")]),s("a-button",[s("a-icon",{attrs:{type:"ellipsis"}})],1)],1),s("a-button",{attrs:{type:"primary"}},[e._v("主操作")])],1),s("a-card",{attrs:{bordered:!1,title:"流程进度"}},[s("a-steps",{attrs:{direction:e.isMobile()?"vertical":"horizontal",current:1,progressDot:""}},[s("a-step",{attrs:{title:"创建项目"}}),s("a-step",{attrs:{title:"部门初审"}}),s("a-step",{attrs:{title:"财务复核"}}),s("a-step",{attrs:{title:"完成"}})],1)],1),s("a-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,title:"用户信息"}},[s("detail-list",[s("detail-list-item",{attrs:{term:"用户姓名"}},[e._v("付晓晓")]),s("detail-list-item",{attrs:{term:"会员卡号"}},[e._v("32943898021309809423")]),s("detail-list-item",{attrs:{term:"身份证"}},[e._v("3321944288191034921")]),s("detail-list-item",{attrs:{term:"联系方式"}},[e._v("18112345678")]),s("detail-list-item",{attrs:{term:"联系地址"}},[e._v("浙江省杭州市西湖区黄姑山路工专路交叉路口")])],1),s("detail-list",{attrs:{title:"信息组"}},[s("detail-list-item",{attrs:{term:"某某数据"}},[e._v("725")]),s("detail-list-item",{attrs:{term:"该数据更新时间"}},[e._v("2018-08-08")]),s("detail-list-item",[e._v(" ")]),s("detail-list-item",{attrs:{term:"某某数据"}},[e._v("725")]),s("detail-list-item",{attrs:{term:"该数据更新时间"}},[e._v("2018-08-08")]),s("detail-list-item",[e._v(" ")])],1),s("a-card",{attrs:{type:"inner",title:"多层信息组"}},[s("detail-list",{attrs:{title:"组名称",size:"small"}},[s("detail-list-item",{attrs:{term:"负责人"}},[e._v("林东东")]),s("detail-list-item",{attrs:{term:"角色码"}},[e._v("1234567")]),s("detail-list-item",{attrs:{term:"所属部门"}},[e._v("XX公司-YY部")]),s("detail-list-item",{attrs:{term:"过期时间"}},[e._v("2018-08-08")]),s("detail-list-item",{attrs:{term:"描述"}},[e._v("这段描述很长很长很长很长很长很长很长很长很长很长很长很长很长很长...")])],1),s("a-divider",{staticStyle:{margin:"16px 0"}}),s("detail-list",{attrs:{title:"组名称",size:"small",col:1}},[s("detail-list-item",{attrs:{term:"学名"}},[e._v("\tCitrullus lanatus (Thunb.) Matsum. et Nakai一年生蔓生藤本；茎、枝粗壮，具明显的棱。卷须较粗..")])],1),s("a-divider",{staticStyle:{margin:"16px 0"}}),s("detail-list",{attrs:{title:"组名称",size:"small",col:2}},[s("detail-list-item",{attrs:{term:"负责人"}},[e._v("付小小")]),s("detail-list-item",{attrs:{term:"角色码"}},[e._v("1234567")])],1)],1)],1),s("a-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,title:"用户近半年来电记录"}},[s("div",{staticClass:"no-data"},[s("a-icon",{attrs:{type:"frown-o"}}),e._v("暂无数据")],1)]),s("a-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,tabList:e.tabList,activeTabKey:e.activeTabKey},on:{tabChange:function(e){t.activeTabKey=e}}},["1"===e.activeTabKey?s("a-table",{attrs:{columns:e.operationColumns,dataSource:e.operation1,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("a-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,106314470)}):e._e(),"2"===e.activeTabKey?s("a-table",{attrs:{columns:e.operationColumns,dataSource:e.operation2,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("a-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,106314470)}):e._e(),"3"===e.activeTabKey?s("a-table",{attrs:{columns:e.operationColumns,dataSource:e.operation3,pagination:!1},scopedSlots:e._u([{key:"status",fn:function(t){return[s("a-badge",{attrs:{status:e._f("statusTypeFilter")(t),text:e._f("statusFilter")(t)}})]}}],null,!1,106314470)}):e._e()],1)],2)},r=[],i=a("ac0d"),o=a("b445"),n=a("c16f"),l=n["default"].Item,c={name:"Advanced",components:{PageLayout:o["default"],DetailList:n["default"],DetailListItem:l},mixins:[i["b"]],data:function(){return{tabList:[{key:"1",tab:"操作日志一"},{key:"2",tab:"操作日志二"},{key:"3",tab:"操作日志三"}],activeTabKey:"1",operationColumns:[{title:"操作类型",dataIndex:"type",key:"type"},{title:"操作人",dataIndex:"name",key:"name"},{title:"执行结果",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:"操作时间",dataIndex:"updatedAt",key:"updatedAt"},{title:"备注",dataIndex:"remark",key:"remark"}],operation1:[{key:"op1",type:"订购关系生效",name:"曲丽丽",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op4",type:"提交订单",name:"林东东",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"很棒"},{key:"op5",type:"创建订单",name:"汗牙牙",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"}],operation2:[{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"},{key:"op4",type:"提交订单",name:"林东东",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"很棒"}],operation3:[{key:"op2",type:"财务复审",name:"付小小",status:"reject",updatedAt:"2017-10-03  19:23:12",remark:"不通过原因"},{key:"op3",type:"部门初审",name:"周毛毛",status:"agree",updatedAt:"2017-10-03  19:23:12",remark:"-"}]}},filters:{statusFilter:function(t){var e={agree:"成功",reject:"驳回"};return e[t]},statusTypeFilter:function(t){var e={agree:"success",reject:"error"};return e[t]}}},d=c,u=(a("0bf1"),a("2877")),p=Object(u["a"])(d,s,r,!1,null,"3f7cc586",null);e["default"]=p.exports},"24f5":function(t,e,a){"use strict";var s=a("471ef"),r=a.n(s);r.a},"264e":function(t,e,a){},27694:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{"body-style":{padding:"24px 32px"},bordered:!1}},[a("a-form",{attrs:{form:t.form},on:{submit:t.handleSubmit}},[a("a-form-item",{attrs:{label:"标题",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}}}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"请输入标题"}]}],expression:"[\n          'name',\n          {rules: [{ required: true, message: '请输入标题' }]}\n        ]"}],attrs:{name:"name",placeholder:"给目标起个名字"}})],1),a("a-form-item",{attrs:{label:"起止日期",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}}}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["buildTime",{rules:[{required:!0,message:"请选择起止日期"}]}],expression:"[\n          'buildTime',\n          {rules: [{ required: true, message: '请选择起止日期' }]}\n        ]"}],staticStyle:{width:"100%"},attrs:{name:"buildTime"}})],1),a("a-form-item",{attrs:{label:"目标描述",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}}}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["description",{rules:[{required:!0,message:"请输入目标描述"}]}],expression:"[\n          'description',\n          {rules: [{ required: true, message: '请输入目标描述' }]}\n        ]"}],attrs:{rows:"4",placeholder:"请输入你阶段性工作目标"}})],1),a("a-form-item",{attrs:{label:"衡量标准",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}}}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["type",{rules:[{required:!0,message:"请输入衡量标准"}]}],expression:"[\n          'type',\n          {rules: [{ required: true, message: '请输入衡量标准' }]}\n        ]"}],attrs:{rows:"4",placeholder:"请输入衡量标准"}})],1),a("a-form-item",{attrs:{label:"客户",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}}}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["customer",{rules:[{required:!0,message:"请描述你服务的客户"}]}],expression:"[\n          'customer',\n          {rules: [{ required: true, message: '请描述你服务的客户' }]}\n        ]"}],attrs:{placeholder:"请描述你服务的客户，内部客户直接 @姓名／工号"}})],1),a("a-form-item",{attrs:{label:"邀评人",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}},required:!1}},[a("a-input",{attrs:{placeholder:"请直接 @姓名／工号，最多可邀请 5 人"}})],1),a("a-form-item",{attrs:{label:"权重",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}},required:!1}},[a("a-input-number",{attrs:{min:0,max:100}}),a("span",[t._v(" %")])],1),a("a-form-item",{attrs:{label:"目标公开",labelCol:{lg:{span:7},sm:{span:7}},wrapperCol:{lg:{span:10},sm:{span:17}},required:!1,help:"客户、邀评人默认被分享"}},[a("a-radio-group",{model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[a("a-radio",{attrs:{value:1}},[t._v("公开")]),a("a-radio",{attrs:{value:2}},[t._v("部分公开")]),a("a-radio",{attrs:{value:3}},[t._v("不公开")])],1),a("a-form-item",[2===t.value?a("a-select",{attrs:{mode:"multiple"}},[a("a-select-option",{attrs:{value:"4"}},[t._v("同事一")]),a("a-select-option",{attrs:{value:"5"}},[t._v("同事二")]),a("a-select-option",{attrs:{value:"6"}},[t._v("同事三")])],1):t._e()],1)],1),a("a-form-item",{staticStyle:{"text-align":"center"},attrs:{wrapperCol:{span:24}}},[a("a-button",{attrs:{htmlType:"submit",type:"primary"}},[t._v("提交")]),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v("保存")])],1)],1)],1)},r=[],i={name:"BaseForm",data:function(){return{description:"表单页用于向用户收集或验证信息，基础表单常见于数据项较少的表单场景。",value:1,form:this.$form.createForm(this)}},methods:{handleSubmit:function(t){t.preventDefault(),this.form.validateFields((function(t,e){}))}}},o=i,n=a("2877"),l=Object(n["a"])(o,s,r,!1,null,null,null);e["default"]=l.exports},"2cfd":function(t,e,a){"use strict";var s=a("5184"),r=a.n(s);r.a},"2eda":function(t,e,a){},"2f3a":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"aigc-dashboard"},[a("a-spin",{attrs:{spinning:t.loading,tip:"加载仪表板数据中..."}},[a("div",{staticClass:"welcome-section"},[a("a-card",{staticClass:"welcome-card",attrs:{bordered:!1}},[a("div",{staticClass:"welcome-content"},[a("div",{staticClass:"welcome-left"},[a("h1",{staticClass:"welcome-title"},[t._v("欢迎回来，"+t._s(t.userInfo.nickname||"用户")+"！")]),a("p",{staticClass:"welcome-subtitle"},[t._v(t._s(t.getCurrentTimeGreeting()))]),a("div",{staticClass:"user-stats"},[a("a-tag",{staticClass:"user-level-tag",attrs:{color:"blue"}},[t._v("\n              "+t._s(t.getUserLevelText(t.userInfo.memberLevel))+"\n            ")]),t.isAdmin?a("a-tag",{staticClass:"admin-tag",attrs:{color:"red"}},[t._v("\n              系统管理员\n            ")]):t._e(),a("span",{staticClass:"balance-info"},[t._v("\n              账户余额："),a("span",{staticClass:"balance-amount"},[t._v("¥"+t._s(t.userInfo.accountBalance||0))])])],1)]),a("div",{staticClass:"welcome-right"},[a("div",{staticClass:"avatar-section"},[a("a-avatar",{staticClass:"user-avatar",attrs:{size:80,src:t.userInfo.avatar}},[t._v("\n              "+t._s(t.userInfo.nickname?t.userInfo.nickname.charAt(0):"U")+"\n            ")]),a("div",{staticClass:"online-status"},[a("a-badge",{attrs:{status:"processing",text:"在线"}})],1)],1),a("div",{staticClass:"quick-actions"},[a("a-button",{staticClass:"website-btn",attrs:{type:"primary",size:"large"},on:{click:t.goToWebsite}},[a("a-icon",{attrs:{type:"home"}}),t._v("\n              进入官网首页\n            ")],1)],1)])])])],1),a("div",{staticClass:"stats-section"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:6,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticClass:"stat-card",attrs:{bordered:!1}},[a("a-statistic",{attrs:{title:t.isAdmin?"今日系统调用":"今日API调用",value:t.apiStats.todayCalls,precision:0,suffix:"次","value-style":{color:"#3f8600"}},scopedSlots:t._u([{key:"prefix",fn:function(){return[a("a-icon",{attrs:{type:"api"}})]},proxy:!0}])}),a("div",{staticClass:"stat-trend"},[a("a-icon",{staticStyle:{color:"#3f8600"},attrs:{type:"arrow-up"}}),a("span",{staticStyle:{color:"#3f8600"}},[t._v(t._s(t.apiStats.todayGrowth)+"%")]),a("span",{staticClass:"trend-text"},[t._v("较昨日")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticClass:"stat-card",attrs:{bordered:!1}},[a("a-statistic",{attrs:{title:t.isAdmin?"本月系统调用":"本月总调用",value:t.apiStats.monthCalls,precision:0,suffix:"次","value-style":{color:"#1890ff"}},scopedSlots:t._u([{key:"prefix",fn:function(){return[a("a-icon",{attrs:{type:"bar-chart"}})]},proxy:!0}])}),a("div",{staticClass:"stat-trend"},[a("a-icon",{staticStyle:{color:"#1890ff"},attrs:{type:"arrow-up"}}),a("span",{staticStyle:{color:"#1890ff"}},[t._v(t._s(t.apiStats.monthGrowth)+"%")]),a("span",{staticClass:"trend-text"},[t._v("较上月")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticClass:"stat-card",attrs:{bordered:!1}},[a("a-statistic",{attrs:{title:"成功率",value:t.apiStats.successRate,precision:2,suffix:"%","value-style":{color:"#722ed1"}},scopedSlots:t._u([{key:"prefix",fn:function(){return[a("a-icon",{attrs:{type:"check-circle"}})]},proxy:!0}])}),a("div",{staticClass:"stat-trend"},[a("a-icon",{staticStyle:{color:"#722ed1"},attrs:{type:"arrow-up"}}),a("span",{staticStyle:{color:"#722ed1"}},[t._v(t._s(t.apiStats.successRateGrowth)+"%")]),a("span",{staticClass:"trend-text"},[t._v("较昨日")])],1)],1)],1),a("a-col",{attrs:{xl:6,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticClass:"stat-card",attrs:{bordered:!1}},[a("a-statistic",{attrs:{title:t.isAdmin?"系统总收入":"总消费金额",value:t.userInfo.totalConsumption||0,precision:2,prefix:"¥","value-style":{color:"#fa8c16"}},scopedSlots:t._u([{key:"prefix",fn:function(){return[a("a-icon",{attrs:{type:"dollar"}})]},proxy:!0}])}),a("div",{staticClass:"stat-trend"},[a("a-icon",{staticStyle:{color:"#fa8c16"},attrs:{type:"arrow-up"}}),a("span",{staticStyle:{color:"#fa8c16"}},[t._v(t._s(t.isAdmin?"系统累计":"本月累计"))])],1)],1)],1),t.isAdmin?a("a-col",{attrs:{xl:6,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticClass:"stat-card",attrs:{bordered:!1}},[a("a-statistic",{attrs:{title:"今日应用下载",value:t.downloadStats.todayTotal,precision:0,suffix:"次","value-style":{color:"#722ed1"}},scopedSlots:t._u([{key:"prefix",fn:function(){return[a("a-icon",{attrs:{type:"download"}})]},proxy:!0}],null,!1,3253357727)}),a("div",{staticClass:"stat-trend"},[a("a-icon",{staticStyle:{color:"#1890ff","margin-right":"4px"},attrs:{type:"windows"}}),a("span",{staticStyle:{color:"#1890ff"}},[t._v(t._s(t.downloadStats.todayWindows))]),a("a-icon",{staticStyle:{color:"#52c41a","margin-left":"8px","margin-right":"4px"},attrs:{type:"apple"}}),a("span",{staticStyle:{color:"#52c41a"}},[t._v(t._s(t.downloadStats.todayMac))])],1)],1)],1):t._e()],1)],1),a("div",{staticClass:"charts-section"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:16,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:t.isAdmin?"系统API调用趋势":"API调用趋势"},scopedSlots:t._u([{key:"extra",fn:function(){return[a("a-radio-group",{on:{change:t.onTimeRangeChange},model:{value:t.chartTimeRange,callback:function(e){t.chartTimeRange=e},expression:"chartTimeRange"}},[a("a-radio-button",{attrs:{value:"today"}},[t._v("今日")]),a("a-radio-button",{attrs:{value:"week"}},[t._v("本周")]),a("a-radio-button",{attrs:{value:"month"}},[t._v("本月")])],1)]},proxy:!0}])},[a("div",{staticStyle:{height:"400px"},attrs:{id:"apiTrendChart"}})])],1),a("a-col",{attrs:{xl:8,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"monitor-card",attrs:{bordered:!1,title:"系统状态"},scopedSlots:t._u([{key:"extra",fn:function(){return[a("a-badge",{attrs:{status:t.monitorStatus,text:t.monitorStatusText}})]},proxy:!0}])},[a("div",{staticClass:"monitor-item"},[a("div",{staticClass:"monitor-label"},[t._v("系统状态")]),a("div",{staticClass:"monitor-value"},[a("a-tag",{attrs:{color:"green"}},[t._v("正常运行")])],1)]),a("div",{staticClass:"monitor-item"},[a("div",{staticClass:"monitor-label"},[t._v(t._s(t.isAdmin?"系统在线用户":"在线用户"))]),a("div",{staticClass:"monitor-value"},[t._v("\n              "+t._s(t.realTimeStats.onlineUsers||0)+" 人\n            ")])]),a("div",{staticClass:"monitor-item"},[a("div",{staticClass:"monitor-label"},[t._v(t._s(t.isAdmin?"系统今日活跃":"今日活跃"))]),a("div",{staticClass:"monitor-value"},[a("span",{staticClass:"activity-value"},[t._v(t._s(t.realTimeStats.todayActiveUsers||0))]),a("span",{staticClass:"activity-unit"},[t._v("人次")])])]),a("div",{staticClass:"recent-calls"},[a("h4",[t._v(t._s(t.isAdmin?"系统最近调用记录":"最近调用记录"))]),a("div",{staticClass:"call-list"},t._l(t.recentCalls,(function(e){return a("div",{key:e.id,staticClass:"call-item",class:{"call-success":e.success,"call-error":!e.success}},[a("div",{staticClass:"call-time"},[t._v(t._s(t.formatTime(e.time)))]),a("div",{staticClass:"call-api"},[t._v(t._s(e.apiType))]),t.isAdmin&&e.userId?a("div",{staticClass:"call-user"},[t._v(t._s(e.userId))]):t._e(),a("div",{staticClass:"call-status"},[a("a-icon",{attrs:{type:e.success?"check-circle":"close-circle"}})],1)])})),0)])])],1)],1)],1),a("div",{staticClass:"detail-stats-section"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:12,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:"API使用分布"}},[a("div",{staticStyle:{height:"300px"},attrs:{id:"apiDistributionChart"}})])],1),a("a-col",{attrs:{xl:12,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:"错误统计"}},[a("div",{staticStyle:{height:"300px"},attrs:{id:"errorStatsChart"}})])],1)],1)],1),t.isAdmin?a("div",{staticClass:"download-stats-section"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{attrs:{xl:16,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:"桌面应用下载趋势"},scopedSlots:t._u([{key:"extra",fn:function(){return[a("a-radio-group",{on:{change:t.onDownloadTimeRangeChange},model:{value:t.downloadTimeRange,callback:function(e){t.downloadTimeRange=e},expression:"downloadTimeRange"}},[a("a-radio-button",{attrs:{value:"7"}},[t._v("最近7天")]),a("a-radio-button",{attrs:{value:"14"}},[t._v("最近14天")]),a("a-radio-button",{attrs:{value:"30"}},[t._v("最近30天")])],1)]},proxy:!0}],null,!1,1734605644)},[a("div",{staticStyle:{height:"400px"},attrs:{id:"downloadTrendChart"}})])],1),a("a-col",{attrs:{xl:8,lg:24,md:24,sm:24,xs:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:"下载统计详情"}},[a("div",{staticClass:"download-stats-detail"},[a("div",{staticClass:"platform-stats"},[a("h4",[t._v("平台分布")]),a("div",{staticClass:"platform-item"},[a("a-icon",{staticStyle:{color:"#1890ff","margin-right":"8px"},attrs:{type:"windows"}}),a("span",[t._v("Windows")]),a("span",{staticClass:"platform-count"},[t._v(t._s(t.downloadStats.totalWindows)+"次")])],1),a("div",{staticClass:"platform-item"},[a("a-icon",{staticStyle:{color:"#52c41a","margin-right":"8px"},attrs:{type:"apple"}}),a("span",[t._v("Mac")]),a("span",{staticClass:"platform-count"},[t._v(t._s(t.downloadStats.totalMac)+"次")])],1)]),a("div",{staticClass:"success-rate-stats"},[a("h4",[t._v("下载成功率")]),a("a-progress",{attrs:{percent:t.downloadStats.successRate,"stroke-color":t.downloadStats.successRate>=95?"#52c41a":"#faad14",format:function(t){return t+"%"}}}),a("p",{staticClass:"success-detail"},[t._v("\n                成功："+t._s(t.downloadStats.successCount)+"次 /\n                失败："+t._s(t.downloadStats.failCount)+"次\n              ")])],1),a("div",{staticClass:"ip-stats"},[a("h4",[t._v("独立访问IP")]),a("div",{staticClass:"ip-count"},[t._v(t._s(t.downloadStats.uniqueIpCount))]),a("p",{staticClass:"ip-detail"},[t._v("今日独立下载用户数")])])])])],1)],1),a("a-row",{staticStyle:{"margin-top":"24px"},attrs:{gutter:24}},[a("a-col",{attrs:{span:24}},[a("a-card",{staticClass:"chart-card",attrs:{bordered:!1,title:"最近下载记录"}},[a("a-table",{attrs:{columns:t.downloadRecordColumns,"data-source":t.downloadStats.recentDownloads,pagination:t.downloadRecordPagination,size:"small"},on:{change:t.handleDownloadRecordTableChange},scopedSlots:t._u([{key:"platform",fn:function(e){return[a("a-tag",{attrs:{color:"windows"===e?"blue":"green"}},[a("a-icon",{attrs:{type:"windows"===e?"windows":"apple"}}),t._v("\n                "+t._s("windows"===e?"Windows":"Mac")+"\n              ")],1)]}},{key:"downloadStatus",fn:function(e){return[a("a-tag",{attrs:{color:1===e?"green":"red"}},[a("a-icon",{attrs:{type:1===e?"check-circle":"close-circle"}}),t._v("\n                "+t._s(1===e?"成功":"失败")+"\n              ")],1)]}},{key:"requestTime",fn:function(e){return[t._v("\n              "+t._s(t.formatDateTime(e))+"\n            ")]}},{key:"responseTime",fn:function(e){return[a("span",{style:{color:e>1e3?"#ff4d4f":"#52c41a"}},[t._v("\n                "+t._s(e)+"ms\n              ")])]}}],null,!1,1041193423)})],1)],1)],1)],1):t._e()])],1)},r=[],i=a("a34a"),o=a.n(i),n=a("313e"),l=a("77ea"),c=a("a73df"),d=a("8d13");function u(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,s)}return a}function p(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach((function(e){m(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}function m(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function f(t,e,a,s,r,i,o){try{var n=t[i](o),l=n.value}catch(c){return void a(c)}n.done?e(l):Promise.resolve(l).then(s,r)}function v(t){return function(){var e=this,a=arguments;return new Promise((function(s,r){var i=t.apply(e,a);function o(t){f(i,s,r,o,n,"next",t)}function n(t){f(i,s,r,o,n,"throw",t)}o(void 0)}))}}var h={name:"Analysis",mixins:[c["a"]],data:function(){return{heartbeatConfig:Object(d["a"])("admin",{apiKey:"admin-dashboard-heartbeat-key",enableDebugLog:!1}),userInfo:{nickname:"智界用户",memberLevel:1,accountBalance:0,avatar:""},apiStats:{todayCalls:0,todayGrowth:0,monthCalls:0,monthGrowth:0,successRate:99,successRateGrowth:0},realTimeStats:{onlineUsers:0,todayActiveUsers:0},monitorStatus:"processing",monitorStatusText:"正常运行",chartTimeRange:"today",recentCalls:[],chartData:{trendData:null,distributionData:null,errorStatsData:null},loading:!1,refreshTimer:null,chartInstances:{},isAdmin:!1,userRole:"user",downloadStats:{todayTotal:0,todayWindows:0,todayMac:0,totalWindows:0,totalMac:0,successRate:100,successCount:0,failCount:0,uniqueIpCount:0,recentDownloads:[]},downloadTimeRange:"7",downloadRecordPagination:{current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:function(t,e){return"共 ".concat(t," 条记录")},pageSizeOptions:["10","20","50","100"]},downloadRecordColumns:[{title:"平台",dataIndex:"platform",key:"platform",scopedSlots:{customRender:"platform"},width:100},{title:"文件名",dataIndex:"fileName",key:"fileName",ellipsis:!0},{title:"客户端IP",dataIndex:"clientIp",key:"clientIp",width:120},{title:"状态",dataIndex:"downloadStatus",key:"downloadStatus",scopedSlots:{customRender:"downloadStatus"},width:80},{title:"响应时间",dataIndex:"responseTime",key:"responseTime",scopedSlots:{customRender:"responseTime"},width:100},{title:"下载时间",dataIndex:"requestTime",key:"requestTime",scopedSlots:{customRender:"requestTime"},width:160}]}},mounted:function(){this.initUserRole(),this.loadDashboardData()},beforeDestroy:function(){this.refreshTimer&&clearInterval(this.refreshTimer),Object.values(this.chartInstances).forEach((function(t){t&&t.dispose()}))},methods:{initUserRole:function(){var t=this;try{var e=localStorage.getItem("userRole");this.userRole=e||"user",this.isAdmin=e&&"admin"===e.toLowerCase(),this.isAdmin&&this.$nextTick((function(){t.loadDownloadStats()}))}catch(a){this.userRole="user",this.isAdmin=!1}},getCurrentTimeGreeting:function(){var t=(new Date).getHours();return t<6?"夜深了，注意休息":t<12?"早上好，新的一天开始了":t<18?"下午好，工作顺利":"晚上好，辛苦了"},getUserLevelText:function(t){var e={1:"普通用户",2:"VIP用户",3:"SVIP用户"};return e[t]||"普通用户"},goToWebsite:function(){this.$router.push("/home")},formatDateTime:function(t){if(!t)return"-";var e=new Date(t);return e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},onDownloadTimeRangeChange:function(){this.downloadRecordPagination.current=1,this.loadDownloadStats()},handleDownloadRecordTableChange:function(t){this.downloadRecordPagination.current=t.current,this.downloadRecordPagination.pageSize=t.pageSize,this.loadDownloadStats()},loadDownloadStats:function(){var t=v(o.a.mark((function t(){var e,a,s,r,i,n,c,d,u,m,f;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isAdmin){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,Object(l["j"])({days:parseInt(this.downloadTimeRange),pageNo:this.downloadRecordPagination.current,pageSize:this.downloadRecordPagination.pageSize});case 5:e=t.sent,e.success&&e.result&&(a=e.result,a.todayStats&&a.todayStats.length>0&&(s=0,r=0,i=0,a.todayStats.forEach((function(t){"windows"===t.platform?s=t.downloadCount||0:"mac"===t.platform&&(r=t.downloadCount||0),i+=t.downloadCount||0})),this.downloadStats.todayWindows=s,this.downloadStats.todayMac=r,this.downloadStats.todayTotal=i),a.platformStats&&a.platformStats.length>0&&(n=0,c=0,a.platformStats.forEach((function(t){"windows"===t.platform?n=t.totalCount||0:"mac"===t.platform&&(c=t.totalCount||0)})),this.downloadStats.totalWindows=n,this.downloadStats.totalMac=c),a.todayStats&&a.todayStats.length>0&&(d=0,u=0,m=new Set,a.todayStats.forEach((function(t){d+=t.successCount||0,u+=t.failCount||0,t.uniqueIpCount&&m.add(t.platform)})),this.downloadStats.successCount=d,this.downloadStats.failCount=u,this.downloadStats.uniqueIpCount=a.todayStats.reduce((function(t,e){return t+(e.uniqueIpCount||0)}),0),f=d+u,this.downloadStats.successRate=f>0?Math.round(d/f*100):100),a.recentDownloads&&(this.downloadStats.recentDownloads=a.recentDownloads.map((function(t){return p(p({},t),{},{key:t.id})})),this.downloadRecordPagination.total=a.recentDownloadsTotal||0,this.downloadRecordPagination.current=a.recentDownloadsCurrent||1,this.downloadRecordPagination.pageSize=a.recentDownloadsSize||10),a.trendData&&this.updateDownloadTrendChart(a.trendData)),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](2),this.$message.error("加载下载统计数据失败");case 13:case"end":return t.stop()}}),t,this,[[2,9]])})));function e(){return t.apply(this,arguments)}return e}(),updateDownloadTrendChart:function(t){var e=this;this.$nextTick((function(){var a=document.getElementById("downloadTrendChart");if(a){var s=e.chartInstances.downloadTrendChart;s||(s=n["a"](a),e.chartInstances.downloadTrendChart=s);var r=[],i=[],o=[],l=[],c={};t.forEach((function(t){var e=t.downloadDate;c[e]||(c[e]={windows:0,mac:0}),"windows"===t.platform?c[e].windows=t.downloadCount||0:"mac"===t.platform&&(c[e].mac=t.downloadCount||0)})),Object.keys(c).sort().forEach((function(t){r.push(t);var e=c[t].windows,a=c[t].mac;i.push(e),o.push(a),l.push(e+a)}));var d={title:{text:"桌面应用下载趋势",left:"center",textStyle:{fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(t){var e='<div style="font-weight: bold;">'.concat(t[0].axisValue,"</div>");return t.forEach((function(t){e+='<div style="margin: 4px 0;">\n                  <span style="display: inline-block; width: 10px; height: 10px; background: '.concat(t.color,'; margin-right: 8px;"></span>\n                  ').concat(t.seriesName,": ").concat(t.value,"次\n                </div>")})),e}},legend:{data:["Windows","Mac","总计"],bottom:10},grid:{left:"3%",right:"4%",bottom:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:r,axisLabel:{formatter:function(t){return t.substring(5)}}},yAxis:{type:"value",name:"下载次数",nameTextStyle:{color:"#666"}},series:[{name:"Windows",type:"line",data:i,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3},itemStyle:{color:"#1890ff"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.1)"}]}}},{name:"Mac",type:"line",data:o,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3},itemStyle:{color:"#52c41a"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(82, 196, 26, 0.3)"},{offset:1,color:"rgba(82, 196, 26, 0.1)"}]}}},{name:"总计",type:"line",data:l,smooth:!0,symbol:"diamond",symbolSize:8,lineStyle:{width:2,type:"dashed"},itemStyle:{color:"#722ed1"}}]};s.setOption(d)}}))},formatTime:function(t){return new Date(t).toLocaleTimeString()},onTimeRangeChange:function(){var t=v(o.a.mark((function t(){var e;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["i"])({timeRange:this.chartTimeRange});case 3:e=t.sent,e.success&&e.result.chartData&&e.result.chartData.trendData&&(this.chartData.trendData=e.result.chartData.trendData,this.updateTrendChart()),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),initCharts:function(){var t=this;this.$nextTick((function(){t.initTrendChart(),t.initDistributionChart(),t.initErrorStatsChart()}))},initTrendChart:function(){var t=document.getElementById("apiTrendChart");if(t){var e=n["a"](t);this.chartInstances.trendChart=e;var a=this.chartData.trendData;if(a){var s={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["API调用次数","成功次数","失败次数"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:a.timeLabels||[]},yAxis:{type:"value"},series:[{name:"API调用次数",type:"line",smooth:!0,data:a.callCounts||[]},{name:"成功次数",type:"line",smooth:!0,data:a.successCounts||[]},{name:"失败次数",type:"line",smooth:!0,data:a.errorCounts||[]}]};e.setOption(s)}}},initDistributionChart:function(){var t=document.getElementById("apiDistributionChart");if(t){var e=n["a"](t);this.chartInstances.distributionChart=e;var a=this.chartData.distributionData;if(a){var s={tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"API使用分布",type:"pie",radius:"50%",data:a.data||[],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};e.setOption(s)}}},initErrorStatsChart:function(){var t=document.getElementById("errorStatsChart");if(t){var e=n["a"](t);this.chartInstances.errorStatsChart=e;var a=this.chartData.errorStatsData;if(a){var s={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["4xx错误","5xx错误","超时错误"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:a.categories||[]},yAxis:{type:"value"},series:[{name:"4xx错误",type:"bar",stack:"total",data:a.error4xx||[]},{name:"5xx错误",type:"bar",stack:"total",data:a.error5xx||[]},{name:"超时错误",type:"bar",stack:"total",data:a.timeoutErrors||[]}]};e.setOption(s)}}},updateTrendChart:function(){if(this.chartInstances.trendChart&&this.chartData.trendData){var t=this.chartData.trendData,e={xAxis:{data:t.timeLabels||[]},series:[{data:t.callCounts||[]},{data:t.successCounts||[]},{data:t.errorCounts||[]}]};this.chartInstances.trendChart.setOption(e)}},startRealTimeMonitoring:function(){var t=this;this.refreshTimer=setInterval((function(){t.refreshDashboardData()}),6e4)},refreshDashboardData:function(){var t=v(o.a.mark((function t(){var e,a;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(l["i"])();case 3:e=t.sent,e.success&&(a=e.result,a.realTimeStats&&(this.realTimeStats={onlineUsers:a.realTimeStats.onlineUsers||0,todayActiveUsers:a.realTimeStats.todayActiveUsers||0}),a.recentCalls&&(this.recentCalls=a.recentCalls.map((function(t){return{id:t.id,time:new Date(t.time),apiType:t.apiType,success:t.success,userId:t.userId}}))),a.chartData&&a.chartData.trendData&&(this.chartData.trendData=a.chartData.trendData,this.updateTrendChart())),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));function e(){return t.apply(this,arguments)}return e}(),loadDashboardData:function(){var t=v(o.a.mark((function t(){var e,a,s=this;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,this.loading=!0,t.next=4,Object(l["i"])({timeRange:this.chartTimeRange});case 4:e=t.sent,e.success?(a=e.result,void 0!==a.isAdmin&&(this.isAdmin=a.isAdmin),a.userRole&&(this.userRole=a.userRole),a.userInfo&&(this.userInfo={nickname:a.userInfo.nickname||"智界用户",memberLevel:a.userInfo.memberLevel||1,accountBalance:a.userInfo.accountBalance||0,avatar:a.userInfo.avatar||"",totalConsumption:a.userInfo.totalConsumption||0,totalRecharge:a.userInfo.totalRecharge||0}),a.apiStats&&(this.apiStats={todayCalls:a.apiStats.todayCalls||0,todayGrowth:a.apiStats.todayGrowth||0,monthCalls:a.apiStats.monthCalls||0,monthGrowth:a.apiStats.monthGrowth||0,successRate:a.apiStats.successRate||99,successRateGrowth:a.apiStats.successRateGrowth||0}),a.realTimeStats&&(this.realTimeStats={onlineUsers:a.realTimeStats.onlineUsers||0,todayActiveUsers:a.realTimeStats.todayActiveUsers||0}),a.recentCalls&&(this.recentCalls=a.recentCalls.map((function(t){return{id:t.id,time:new Date(t.time),apiType:t.apiType,success:t.success,userId:t.userId}}))),a.chartData&&(this.chartData=a.chartData),this.$nextTick((function(){s.initCharts()}))):this.$message.error("加载仪表板数据失败："+e.message),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),this.$message.error("加载仪表板数据失败，请稍后重试");case 12:return t.prev=12,this.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,this,[[0,8,12,15]])})));function e(){return t.apply(this,arguments)}return e}()}},y=h,b=(a("382f"),a("2877")),g=Object(b["a"])(y,s,r,!1,null,"e6620974",null);e["default"]=g.exports},3264:function(t,e,a){"use strict";var s=a("e5b2"),r=a.n(s);r.a},3559:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header-index-wide"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"受理量",total:t._f("NumberFormat")(t.cardCount.sll)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-area",{attrs:{"data-source":t.chartData.sll}})],1),a("template",{slot:"footer"},[t._v("今日受理量："),a("span",[t._v(t._s(t.todaySll))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"办结量",total:t._f("NumberFormat")(t.cardCount.bjl)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-area",{attrs:{"data-source":t.chartData.bjl}})],1),a("template",{slot:"footer"},[t._v("今日办结量："),a("span",[t._v(t._s(t.todayBjl))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"用户受理量",total:t._f("NumberFormat")(t.cardCount.isll)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-bar",{attrs:{datasource:t.chartData.isll,height:50}})],1),a("template",{slot:"footer"},[t._v("用户今日受理量："),a("span",[t._v(t._s(t.todayISll))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"用户办结量",total:t._f("NumberFormat")(t.cardCount.ibjl)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-bar",{attrs:{datasource:t.chartData.ibjl,height:50}})],1),a("template",{slot:"footer"},[t._v("用户今日办结量："),a("span",[t._v(t._s(t.todayIBjl))])])],2)],1)],1),a("a-card",{attrs:{loading:t.loading,bordered:!1,"body-style":{padding:"0"}}},[a("div",{staticClass:"salesCard"},[a("a-tabs",{attrs:{"default-active-key":"1",size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}}},[a("div",{staticClass:"extra-wrapper",attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[a("div",{staticClass:"extra-item"},[a("a",[t._v("今日")]),a("a",[t._v("本周")]),a("a",[t._v("本月")]),a("a",[t._v("本年")])]),a("a-range-picker",{style:{width:"256px"}})],1),a("a-tab-pane",{key:"1",attrs:{loading:"true",tab:"受理监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("index-bar",{attrs:{title:"受理量统计"}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",t._l(t.registerTypeList,(function(e,s){return a("a-col",{key:"registerType"+s,class:"more-btn",attrs:{span:12}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(e){return t.goPage(s)}}},[t._v(t._s(e.text))])],1)})),1)],1)])],1)],1)],1),a("a-tab-pane",{key:"2",attrs:{tab:"交互监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("bar-multid",{attrs:{sourceData:t.jhjgData,fields:t.jhjgFields,title:"平台与部门交互量统计"}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",t._l(t.registerTypeList,(function(e,s){return a("a-col",{key:"registerType"+s,class:"more-btn",attrs:{span:12}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(e){return t.goPage(s)}}},[t._v(t._s(e.text))])],1)})),1)],1)])],1)],1)],1),a("a-tab-pane",{key:"4",attrs:{tab:"存储监管"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("a-row",[t.diskInfo&&t.diskInfo.length>0?t._l(t.diskInfo,(function(t,e){return a("a-col",{key:"diskInfo"+e,attrs:{span:12}},[a("dash-chart-demo",{attrs:{title:t.name,datasource:t.restPPT}})],1)})):t._e()],2)],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("a-card",{staticStyle:{"margin-bottom":"24px"},attrs:{title:"快速开始 / 便捷导航",bordered:!1,"body-style":{padding:0}}},[a("div",{staticClass:"item-group"},[a("a-row",t._l(t.registerTypeList,(function(e,s){return a("a-col",{key:"registerType"+s,class:"more-btn",attrs:{span:10}},[a("a-button",{staticStyle:{"margin-bottom":"10px"},attrs:{size:"small",type:"primary",ghost:""},on:{click:function(e){return t.goPage(s)}}},[t._v(t._s(e.text))])],1)})),1)],1)])],1)],1)],1)],1)],1)]),a("a-row",{attrs:{gutter:12}},[a("a-card",{class:{"anty-list-cust":!0},style:{marginTop:"24px"},attrs:{loading:t.loading,bordered:!1}},[a("a-tabs",{attrs:{size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}},model:{value:t.indexBottomTab,callback:function(e){t.indexBottomTab=e},expression:"indexBottomTab"}},[a("div",{staticClass:"extra-wrapper",attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[a("a-radio-group",{on:{change:t.changeRegisterType},model:{value:t.indexRegisterType,callback:function(e){t.indexRegisterType=e},expression:"indexRegisterType"}},[a("a-radio-button",{attrs:{value:"转移登记"}},[t._v("转移登记")]),a("a-radio-button",{attrs:{value:"抵押登记"}},[t._v("抵押登记")]),a("a-radio-button",{attrs:{value:""}},[t._v("所有")])],1)],1),a("a-tab-pane",{key:"1",attrs:{loading:"true",tab:"业务流程限时监管"}},[a("a-table",{attrs:{dataSource:t.dataSource1,size:"default",rowKey:"id",columns:t.columns,pagination:t.ipagination1},on:{change:t.tableChange1},scopedSlots:t._u([{key:"flowRate",fn:function(e,s,r){return[a("a-progress",{staticStyle:{width:"80px"},attrs:{strokeColor:t.getPercentColor(s.flowRate),format:t.getPercentFormat,percent:t.getFlowRateNumber(s.flowRate)}})]}}])})],1),a("a-tab-pane",{key:"2",attrs:{loading:"true",tab:"业务节点限时监管"}},[a("a-table",{attrs:{dataSource:t.dataSource2,size:"default",rowKey:"id",columns:t.columns2,pagination:t.ipagination2},on:{change:t.tableChange2},scopedSlots:t._u([{key:"flowRate",fn:function(e,s,r){return[a("span",{staticStyle:{color:"red"}},[t._v(t._s(s.flowRate)+"小时")])]}}])})],1)],1)],1)],1)],1)},r=[],i=a("da05"),o=a("3896"),n=a("05ed"),l=a("942d"),c=a("1d43"),d=a("0096"),u=a("8191"),p=a("972f"),m=[{type:"房管","1月":900,"2月":1120,"3月":1380,"4月":1480,"5月":1450,"6月":1100,"7月":1300,"8月":900,"9月":1e3,"10月":1200,"11月":600,"12月":900},{type:"税务","1月":1200,"2月":1500,"3月":1980,"4月":2e3,"5月":1e3,"6月":600,"7月":900,"8月":1100,"9月":1300,"10月":2e3,"11月":900,"12月":1100},{type:"不动产","1月":2e3,"2月":1430,"3月":1300,"4月":1400,"5月":900,"6月":500,"7月":600,"8月":1e3,"9月":600,"10月":1e3,"11月":1500,"12月":1200}],f=["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],v=[{type:"一月","房管":1.12,"税务":1.55,"不动产":1.2},{type:"二月","房管":1.65,"税务":1.32,"不动产":1.42},{type:"三月","房管":1.85,"税务":1.1,"不动产":1.5},{type:"四月","房管":1.33,"税务":1.63,"不动产":1.4},{type:"五月","房管":1.63,"税务":1.8,"不动产":1.7},{type:"六月","房管":1.85,"税务":1.98,"不动产":1.8},{type:"七月","房管":1.98,"税务":1.5,"不动产":1.76},{type:"八月","房管":1.48,"税务":1.2,"不动产":1.3},{type:"九月","房管":1.41,"税务":1.9,"不动产":1.6},{type:"十月","房管":1.1,"税务":1.1,"不动产":1.4},{type:"十一月","房管":1.85,"税务":1.6,"不动产":1.5},{type:"十二月","房管":1.5,"税务":1.4,"不动产":1.3}],h=["房管","税务","不动产"],y=[{title:"业务号",align:"center",dataIndex:"reBizCode"},{title:"业务类型",align:"center",dataIndex:"type"},{title:"受理人",align:"center",dataIndex:"acceptBy"},{title:"受理时间",align:"center",dataIndex:"acceptDate"},{title:"当前节点",align:"center",dataIndex:"curNode"},{title:"办理时长",align:"center",dataIndex:"flowRate",scopedSlots:{customRender:"flowRate"}}],b=[{reBizCode:"1",type:"转移登记",acceptBy:"张三",acceptDate:"2019-01-22",curNode:"任务分派",flowRate:60},{reBizCode:"2",type:"抵押登记",acceptBy:"李四",acceptDate:"2019-01-23",curNode:"领导审核",flowRate:30},{reBizCode:"3",type:"转移登记",acceptBy:"王武",acceptDate:"2019-01-25",curNode:"任务处理",flowRate:20},{reBizCode:"4",type:"转移登记",acceptBy:"赵楼",acceptDate:"2019-11-22",curNode:"部门审核",flowRate:80},{reBizCode:"5",type:"转移登记",acceptBy:"钱就",acceptDate:"2019-12-12",curNode:"任务分派",flowRate:90},{reBizCode:"6",type:"转移登记",acceptBy:"孙吧",acceptDate:"2019-03-06",curNode:"任务处理",flowRate:10},{reBizCode:"7",type:"抵押登记",acceptBy:"周大",acceptDate:"2019-04-13",curNode:"任务分派",flowRate:100},{reBizCode:"8",type:"抵押登记",acceptBy:"吴二",acceptDate:"2019-05-09",curNode:"任务上报",flowRate:50},{reBizCode:"9",type:"抵押登记",acceptBy:"郑爽",acceptDate:"2019-07-12",curNode:"任务处理",flowRate:63},{reBizCode:"20",type:"抵押登记",acceptBy:"林有",acceptDate:"2019-12-12",curNode:"任务打回",flowRate:59},{reBizCode:"11",type:"转移登记",acceptBy:"码云",acceptDate:"2019-09-10",curNode:"任务签收",flowRate:87}],g=[{title:"业务号",align:"center",dataIndex:"reBizCode"},{title:"受理人",align:"center",dataIndex:"acceptBy"},{title:"发起时间",align:"center",dataIndex:"acceptDate"},{title:"当前节点",align:"center",dataIndex:"curNode"},{title:"超时时间",align:"center",dataIndex:"flowRate",scopedSlots:{customRender:"flowRate"}}],C=[{reBizCode:"A001",type:"转移登记",acceptBy:"张四",acceptDate:"2019-01-22",curNode:"任务分派",flowRate:12},{reBizCode:"A002",type:"抵押登记",acceptBy:"李吧",acceptDate:"2019-01-23",curNode:"任务签收",flowRate:3},{reBizCode:"A003",type:"转移登记",acceptBy:"王三",acceptDate:"2019-01-25",curNode:"任务处理",flowRate:24},{reBizCode:"A004",type:"转移登记",acceptBy:"赵二",acceptDate:"2019-11-22",curNode:"部门审核",flowRate:10},{reBizCode:"A005",type:"转移登记",acceptBy:"钱大",acceptDate:"2019-12-12",curNode:"任务签收",flowRate:8},{reBizCode:"A006",type:"转移登记",acceptBy:"孙就",acceptDate:"2019-03-06",curNode:"任务处理",flowRate:10},{reBizCode:"A007",type:"抵押登记",acceptBy:"周晕",acceptDate:"2019-04-13",curNode:"部门审核",flowRate:24},{reBizCode:"A008",type:"抵押登记",acceptBy:"吴有",acceptDate:"2019-05-09",curNode:"部门审核",flowRate:30},{reBizCode:"A009",type:"抵押登记",acceptBy:"郑武",acceptDate:"2019-07-12",curNode:"任务分派",flowRate:1},{reBizCode:"A0010",type:"抵押登记",acceptBy:"林爽",acceptDate:"2019-12-12",curNode:"部门审核",flowRate:16},{reBizCode:"A0011",type:"转移登记",acceptBy:"码楼",acceptDate:"2019-09-10",curNode:"部门审核",flowRate:7}],w={name:"IndexBdc",components:{ATooltip:o["a"],ACol:i["b"],ChartCard:n["default"],MiniArea:c["default"],MiniBar:l["default"],DashChartDemo:p["default"],BarMultid:u["default"],IndexBar:d["default"]},data:function(){return{loading:!0,cardCount:{sll:100,bjl:87,isll:15,ibjl:9},todaySll:60,todayBjl:54,todayISll:13,todayIBjl:7,chartData:{sll:[],bjl:[],isll:[],ibjl:[]},jhjgFields:f,jhjgData:m,xljgData:v,xljgFields:h,diskInfo:[{name:"C盘",restPPT:7},{name:"D盘",restPPT:5}],registerTypeList:[{text:"业务受理"},{text:"业务管理"},{text:"文件管理"},{text:"信息查询"}],dataSource1:[],dataSource2:[],columns:y,columns2:g,ipagination1:{current:1,pageSize:5,pageSizeOptions:["10","20","30"],showTotal:function(t,e){return e[0]+"-"+e[1]+" 共"+t+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},ipagination2:{current:1,pageSize:5,pageSizeOptions:["10","20","30"],showTotal:function(t,e){return e[0]+"-"+e[1]+" 共"+t+"条"},showQuickJumper:!0,showSizeChanger:!0,total:0},indexRegisterType:"转移登记",indexBottomTab:"1"}},methods:{goPage:function(){this.$message.success("根据业务自行处理跳转页面!")},changeRegisterType:function(t){this.indexRegisterType=t.target.value,"1"==this.indexBottomTab?this.loadDataSource1():this.loadDataSource2()},tableChange1:function(t){this.ipagination1.current=t.current,this.ipagination1.pageSize=t.pageSize,this.queryTimeoutInfo()},tableChange2:function(t){this.ipagination2.current=t.current,this.ipagination2.pageSize=t.pageSize,this.queryNodeTimeoutInfo()},getFlowRateNumber:function(t){return Number(t)},getPercentFormat:function(t){return 100==t?"超时":t+"%"},getPercentColor:function(t){var e=Number(t);return e>=90&&e<100?"rgb(244, 240, 89)":e>=100?"red":"rgb(16, 142, 233)"},loadDataSource1:function(){var t=this;this.dataSource1=b.filter((function(e){return!t.indexRegisterType||e.type==t.indexRegisterType}))},loadDataSource2:function(){var t=this;this.dataSource2=C.filter((function(e){return!t.indexRegisterType||e.type==t.indexRegisterType}))}},created:function(){var t=this;this.loadDataSource1(),this.loadDataSource2(),setTimeout((function(){t.loading=!t.loading}),1e3)}},x=w,_=(a("1d37"),a("2877")),S=Object(_["a"])(x,s,r,!1,null,"b542bbde",null);e["default"]=S.exports},36545:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("a-card",{attrs:{bordered:!1}},[s("div",{staticClass:"table-page-search-wrapper"},[s("a-form",{attrs:{layout:"inline"}},[s("a-row",{attrs:{gutter:48}},[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"角色ID"}},[s("a-input",{attrs:{placeholder:"请输入"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("span",{staticClass:"table-page-search-submitButtons"},[s("a-button",{attrs:{type:"primary"}},[e._v("查询")]),s("a-button",{staticStyle:{"margin-left":"8px"}},[e._v("重置")])],1)])],1)],1)],1),s("s-table",{attrs:{size:"default",columns:e.columns,data:e.loadData},scopedSlots:e._u([{key:"expandedRowRender",fn:function(t){return s("div",{staticStyle:{margin:"0"}},[s("a-row",{style:{marginBottom:"12px"},attrs:{gutter:24}},e._l(t.permissions,(function(t,a){return s("a-col",{key:a,style:{marginBottom:"12px"},attrs:{span:12}},[s("a-col",{attrs:{lg:4,md:24}},[s("span",[e._v(e._s(t.permissionName)+"：")])]),t.actionEntitySet.length>0?s("a-col",{attrs:{lg:20,md:24}},e._l(t.actionEntitySet,(function(t,a){return s("a-tag",{key:a,attrs:{color:"cyan"}},[e._v(e._s(t.describe))])})),1):s("a-col",{attrs:{span:20}},[e._v("-")])],1)})),1)],1)}},{key:"action",fn:function(t,a){return s("span",{},[s("a",{on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-dropdown",[s("a",{staticClass:"ant-dropdown-link"},[e._v("\n          更多 "),s("a-icon",{attrs:{type:"down"}})],1),s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("详情")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("禁用")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("删除")])])],1)],1)],1)}}])}),s("a-modal",{staticStyle:{top:"20px"},attrs:{title:"操作",width:800},on:{ok:e.handleOk},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[s("a-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"唯一识别码",hasFeedback:"",validateStatus:"success"}},[s("a-input",{attrs:{placeholder:"唯一识别码",id:"no",disabled:"disabled"},model:{value:e.mdl.id,callback:function(t){e.$set(e.mdl,"id",t)},expression:"mdl.id"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"角色名称",hasFeedback:"",validateStatus:"success"}},[s("a-input",{attrs:{placeholder:"起一个名字",id:"role_name"},model:{value:e.mdl.name,callback:function(t){e.$set(e.mdl,"name",t)},expression:"mdl.name"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态",hasFeedback:"",validateStatus:"warning"}},[s("a-select",{model:{value:e.mdl.status,callback:function(t){e.$set(e.mdl,"status",t)},expression:"mdl.status"}},[s("a-select-option",{attrs:{value:"1"}},[e._v("正常")]),s("a-select-option",{attrs:{value:"2"}},[e._v("禁用")])],1)],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述",hasFeedback:""}},[s("a-textarea",{attrs:{rows:5,placeholder:"...",id:"describe"},model:{value:e.mdl.describe,callback:function(t){e.$set(e.mdl,"describe",t)},expression:"mdl.describe"}})],1),s("a-divider"),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"拥有权限",hasFeedback:""}},e._l(e.mdl.permissions,(function(t,a){return s("a-row",{key:a,attrs:{gutter:16}},[s("a-col",{attrs:{span:4}},[e._v("\n            "+e._s(t.permissionName)+"：\n          ")]),s("a-col",{attrs:{span:20}},[s("a-checkbox-group",{attrs:{options:t.actionsOptions}})],1)],1)})),1)],1)],1)],1)},r=[],i=a("e8c4"),o=a("0fea"),n={name:"TableList",components:{STable:i["a"]},data:function(){return{description:"列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。",visible:!1,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},form:null,mdl:{},advanced:!1,queryParam:{},columns:[{title:"唯一识别码",dataIndex:"id"},{title:"角色名称",dataIndex:"name"},{title:"状态",dataIndex:"status"},{title:"创建时间",dataIndex:"createTime",sorter:!0},{title:"操作",width:"150px",dataIndex:"action",scopedSlots:{customRender:"action"}}],loadData:function(t){return Object(o["f"])(t).then((function(t){return t.result}))},selectedRowKeys:[],selectedRows:[]}},created:function(){Object(o["g"])().then((function(t){})),Object(o["f"])().then((function(t){}))},methods:{handleEdit:function(t){this.mdl=Object.assign({},t),this.mdl.permissions.forEach((function(t){t.actionsOptions=t.actionEntitySet.map((function(t){return{label:t.describe,value:t.action,defaultCheck:t.defaultCheck}}))})),this.visible=!0},handleOk:function(){},onChange:function(t,e){this.selectedRowKeys=t,this.selectedRows=e},toggleAdvanced:function(){this.advanced=!this.advanced}},watch:{}},l=n,c=a("2877"),d=Object(c["a"])(l,s,r,!1,null,null,null);e["default"]=d.exports},"382f":function(t,e,a){"use strict";var s=a("2eda"),r=a.n(s);r.a},3840:function(t,e,a){},"471ef":function(t,e,a){},"47d2":function(t,e,a){"use strict";var s=a("264e"),r=a.n(s);r.a},"494a":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("div",{staticClass:"table-page-search-wrapper"},[a("a-form",{attrs:{layout:"inline"}},[a("a-row",{attrs:{gutter:48}},[a("a-col",{attrs:{md:8,sm:24}},[a("a-form-item",{attrs:{label:"角色ID"}},[a("a-input",{attrs:{placeholder:"请输入"}})],1)],1),a("a-col",{attrs:{md:8,sm:24}},[a("a-form-item",{attrs:{label:"状态"}},[a("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[a("a-select-option",{attrs:{value:"0"}},[t._v("全部")]),a("a-select-option",{attrs:{value:"1"}},[t._v("正常")]),a("a-select-option",{attrs:{value:"2"}},[t._v("禁用")])],1)],1)],1),a("a-col",{attrs:{md:8,sm:24}},[a("span",{staticClass:"table-page-search-submitButtons"},[a("a-button",{attrs:{type:"primary"}},[t._v("查询")]),a("a-button",{staticStyle:{"margin-left":"8px"}},[t._v("重置")])],1)])],1)],1)],1),a("s-table",{ref:"table",attrs:{size:"default",columns:t.columns,data:t.loadData},scopedSlots:t._u([{key:"expandedRowRender",fn:function(e){return a("div",{staticStyle:{margin:"0"}},[a("a-row",{style:{marginBottom:"12px"},attrs:{gutter:24}},t._l(e.permissions,(function(e,s){return a("a-col",{key:s,style:{marginBottom:"12px"},attrs:{span:12}},[a("a-col",{attrs:{span:4}},[a("span",[t._v(t._s(e.permissionName)+"：")])]),e.actionEntitySet.length>0?a("a-col",{attrs:{span:20}},t._l(e.actionEntitySet,(function(e,s){return a("a-tag",{key:s,attrs:{color:"cyan"}},[t._v(t._s(e.describe))])})),1):a("a-col",{attrs:{span:20}},[t._v("-")])],1)})),1)],1)}},{key:"action",fn:function(e,s){return a("span",{},[a("a",{on:{click:function(e){return t.$refs.modal.edit(s)}}},[t._v("编辑")]),a("a-divider",{attrs:{type:"vertical"}}),a("a-dropdown",[a("a",{staticClass:"ant-dropdown-link"},[t._v("\n          更多 "),a("a-icon",{attrs:{type:"down"}})],1),a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",{attrs:{href:"javascript:;"}},[t._v("详情")])]),a("a-menu-item",[a("a",{attrs:{href:"javascript:;"}},[t._v("禁用")])]),a("a-menu-item",[a("a",{attrs:{href:"javascript:;"}},[t._v("删除")])])],1)],1)],1)}}])}),a("role-modal",{ref:"modal",on:{ok:t.handleOk}})],1)},r=[],i=a("e8c4"),o=a("b7ac"),n={name:"TableList",components:{STable:i["a"],RoleModal:o["default"]},data:function(){var t=this;return{description:"列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。",visible:!1,form:null,mdl:{},advanced:!1,queryParam:{},columns:[{title:"唯一识别码",dataIndex:"id"},{title:"角色名称",dataIndex:"name"},{title:"状态",dataIndex:"status"},{title:"创建时间",dataIndex:"createTime",sorter:!0},{title:"操作",width:"150px",dataIndex:"action",scopedSlots:{customRender:"action"}}],loadData:function(e){return t.$http.get("/mock/api/role",{params:Object.assign(e,t.queryParam)}).then((function(t){return t.result}))},selectedRowKeys:[],selectedRows:[]}},methods:{handleEdit:function(t){this.mdl=Object.assign({},t),this.mdl.permissions.forEach((function(t){t.actionsOptions=t.actionEntitySet.map((function(t){return{label:t.describe,value:t.action,defaultCheck:t.defaultCheck}}))})),this.visible=!0},handleOk:function(){this.$refs.table.refresh()},onChange:function(t,e){this.selectedRowKeys=t,this.selectedRows=e},toggleAdvanced:function(){this.advanced=!this.advanced}},watch:{}},l=n,c=a("2877"),d=Object(c["a"])(l,s,r,!1,null,null,null);e["default"]=d.exports},5184:function(t,e,a){},"60df":function(t,e,a){"use strict";var s=a("a90c"),r=a.n(s);r.a},"6c05":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("exception-page",{attrs:{type:"500"}})},r=[],i=a("0673"),o={components:{ExceptionPage:i["default"]}},n=o,l=a("2877"),c=Object(l["a"])(n,s,r,!1,null,"58acec66",null);e["default"]=c.exports},"6e69":function(t,e,a){"use strict";var s=a("851e"),r=a.n(s);r.a},7311:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-card",{attrs:{bordered:!1}},[a("a-steps",{staticClass:"steps",attrs:{current:t.currentTab}},[a("a-step",{attrs:{title:"填写转账信息"}}),a("a-step",{attrs:{title:"确认转账信息"}}),a("a-step",{attrs:{title:"完成"}})],1),a("div",{staticClass:"content"},[0===t.currentTab?a("step1",{on:{nextStep:t.nextStep}}):t._e(),1===t.currentTab?a("step2",{on:{nextStep:t.nextStep,prevStep:t.prevStep}}):t._e(),2===t.currentTab?a("step3",{on:{prevStep:t.prevStep,finish:t.finish}}):t._e()],1)],1)},r=[],i=a("819f"),o=a("0797"),n=a("876f"),l={name:"StepForm",components:{Step1:i["default"],Step2:o["default"],Step3:n["default"]},data:function(){return{description:"将一个冗长或用户不熟悉的表单任务分成多个步骤，指导用户完成。",currentTab:0,form:null}},methods:{nextStep:function(){this.currentTab<2&&(this.currentTab+=1)},prevStep:function(){this.currentTab>0&&(this.currentTab-=1)},finish:function(){this.currentTab=0}}},c=l,d=(a("3264"),a("2877")),u=Object(d["a"])(c,s,r,!1,null,"e8c4fee2",null);e["default"]=u.exports},"744a":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-card",{attrs:{bordered:!1}},[a("a-row",[a("a-col",{attrs:{sm:8,xs:24}},[a("head-info",{attrs:{title:"我的待办",content:"8个任务",bordered:!0}})],1),a("a-col",{attrs:{sm:8,xs:24}},[a("head-info",{attrs:{title:"本周任务平均处理时间",content:"32分钟",bordered:!0}})],1),a("a-col",{attrs:{sm:8,xs:24}},[a("head-info",{attrs:{title:"本周完成任务数",content:"24个"}})],1)],1)],1),a("a-card",{staticStyle:{"margin-top":"24px"},attrs:{bordered:!1,title:"标准列表"}},[a("div",{attrs:{slot:"extra"},slot:"extra"},[a("a-radio-group",[a("a-radio-button",[t._v("全部")]),a("a-radio-button",[t._v("进行中")]),a("a-radio-button",[t._v("等待中")])],1),a("a-input-search",{staticStyle:{"margin-left":"16px",width:"272px"}})],1),a("div",{staticClass:"operate"},[a("a-button",{staticStyle:{width:"100%"},attrs:{type:"dashed",icon:"plus"}},[t._v("添加")])],1),a("a-list",{attrs:{size:"large",pagination:{showSizeChanger:!0,showQuickJumper:!0,pageSize:5,total:50}}},t._l(t.data,(function(e,s){return a("a-list-item",{key:s},[a("a-list-item-meta",{attrs:{description:e.description}},[a("a-avatar",{attrs:{slot:"avatar",size:"large",shape:"square",src:e.avatar},slot:"avatar"}),a("a",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(e.title))])],1),a("div",{attrs:{slot:"actions"},slot:"actions"},[a("a",[t._v("编辑")])]),a("div",{attrs:{slot:"actions"},slot:"actions"},[a("a-dropdown",[a("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[a("a-menu-item",[a("a",[t._v("编辑")])]),a("a-menu-item",[a("a",[t._v("删除")])])],1),a("a",[t._v("更多"),a("a-icon",{attrs:{type:"down"}})],1)],1)],1),a("div",{staticClass:"list-content"},[a("div",{staticClass:"list-content-item"},[a("span",[t._v("Owner")]),a("p",[t._v(t._s(e.owner))])]),a("div",{staticClass:"list-content-item"},[a("span",[t._v("开始时间")]),a("p",[t._v(t._s(e.startAt))])]),a("div",{staticClass:"list-content-item"},[a("a-progress",{staticStyle:{width:"180px"},attrs:{percent:e.progress.value,status:e.progress.status?e.progress.status:null}})],1)])],1)})),1)],1)],1)},r=[],i=a("81d1"),o=[];o.push({title:"Alipay",avatar:"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png",description:"那是一种内在的东西， 他们到达不了，也无法触及的",owner:"付晓晓",startAt:"2018-07-26 22:44",progress:{value:90}}),o.push({title:"Angular",avatar:"https://gw.alipayobjects.com/zos/rmsportal/zOsKZmFRdUtvpqCImOVY.png",description:"希望是一个好东西，也许是最好的，好东西是不会消亡的",owner:"曲丽丽",startAt:"2018-07-26 22:44",progress:{value:54}}),o.push({title:"Ant Design",avatar:"https://gw.alipayobjects.com/zos/rmsportal/dURIMkkrRFpPgTuzkwnB.png",description:"生命就像一盒巧克力，结果往往出人意料",owner:"林东东",startAt:"2018-07-26 22:44",progress:{value:66}}),o.push({title:"Ant Design Pro",avatar:"https://gw.alipayobjects.com/zos/rmsportal/sfjbOqnsXXJgNCjCzDBL.png",description:"城镇中有那么多的酒馆，她却偏偏走进了我的酒馆",owner:"周星星",startAt:"2018-07-26 22:44",progress:{value:30}}),o.push({title:"Bootstrap",avatar:"https://gw.alipayobjects.com/zos/rmsportal/siCrBXXhmvTQGWPNLBow.png",description:"那时候我只会想自己想要什么，从不想自己拥有什么",owner:"吴加好",startAt:"2018-07-26 22:44",progress:{status:"exception",value:100}});var n={name:"StandardList",components:{HeadInfo:i["default"]},data:function(){return{data:o}}},l=n,c=(a("0314"),a("2877")),d=Object(c["a"])(l,s,r,!1,null,"2d517bef",null);e["default"]=d.exports},"819f":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form",{staticStyle:{"max-width":"500px",margin:"40px auto 0"}},[a("a-form-item",{attrs:{label:"付款账户",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-select",{attrs:{value:"1",placeholder:"<EMAIL>"}},[a("a-select-option",{attrs:{value:"1"}},[t._v("<EMAIL>")])],1)],1),a("a-form-item",{attrs:{label:"收款账户",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-input-group",{staticStyle:{display:"inline-block","vertical-align":"middle"},attrs:{compact:!0}},[a("a-select",{staticStyle:{width:"100px"},attrs:{defaultValue:"alipay"}},[a("a-select-option",{attrs:{value:"alipay"}},[t._v("支付宝")]),a("a-select-option",{attrs:{value:"wexinpay"}},[t._v("微信")])],1),a("a-input",{style:{width:"calc(100% - 100px)"},attrs:{value:"<EMAIL>"}})],1)],1),a("a-form-item",{attrs:{label:"收款人姓名",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-input",{attrs:{value:"Alex"}})],1),a("a-form-item",{attrs:{label:"转账金额",labelCol:{span:5},wrapperCol:{span:19}}},[a("a-input",{attrs:{prefix:"￥",value:"5000"}})],1),a("a-form-item",{attrs:{wrapperCol:{span:19,offset:5}}},[a("a-button",{attrs:{type:"primary"},on:{click:t.nextStep}},[t._v("下一步")])],1)],1)],1)},r=[],i={name:"Step1",methods:{nextStep:function(){this.$emit("nextStep")}}},o=i,n=a("2877"),l=Object(n["a"])(o,s,r,!1,null,"12db3e54",null);e["default"]=l.exports},"84c5":function(t,e,a){"use strict";var s=a("bfad"),r=a.n(s);r.a},"851e":function(t,e,a){},"85f1":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("page-layout",{attrs:{title:t.title}},[a("a-card",{attrs:{bordered:!1}},[a("detail-list",{attrs:{title:"退款申请"}},[a("detail-list-item",{attrs:{term:"取货单号"}},[t._v("1000000000")]),a("detail-list-item",{attrs:{term:"状态"}},[t._v("已取货")]),a("detail-list-item",{attrs:{term:"销售单号"}},[t._v("1234123421")]),a("detail-list-item",{attrs:{term:"子订单"}},[t._v("3214321432")])],1),a("a-divider",{staticStyle:{"margin-bottom":"32px"}}),a("detail-list",{attrs:{title:"用户信息"}},[a("detail-list-item",{attrs:{term:"用户姓名"}},[t._v("付小小")]),a("detail-list-item",{attrs:{term:"联系电话"}},[t._v("18100000000")]),a("detail-list-item",{attrs:{term:"常用快递"}},[t._v("菜鸟仓储")]),a("detail-list-item",{attrs:{term:"取货地址"}},[t._v("浙江省杭州市西湖区万塘路18号")]),a("detail-list-item",{attrs:{term:"备注"}},[t._v("\t无")])],1),a("a-divider",{staticStyle:{"margin-bottom":"32px"}}),a("div",{staticClass:"title"},[t._v("退货商品")]),a("s-table",{staticStyle:{"margin-bottom":"24px"},attrs:{columns:t.goodsColumns,data:t.loadGoodsData}}),a("div",{staticClass:"title"},[t._v("退货进度")]),a("s-table",{staticStyle:{"margin-bottom":"24px"},attrs:{columns:t.scheduleColumns,data:t.loadScheduleData},scopedSlots:t._u([{key:"status",fn:function(e){return[a("a-badge",{attrs:{status:e,text:t._f("statusFilter")(e)}})]}}])})],1)],1)},r=[],i=a("b445"),o=a("e8c4"),n=a("c16f"),l=a("2985"),c=n["default"].Item,d={components:{PageLayout:i["default"],ABadge:l["a"],DetailList:n["default"],DetailListItem:c,STable:o["a"]},data:function(){return{goodsColumns:[{title:"商品编号",dataIndex:"id",key:"id"},{title:"商品名称",dataIndex:"name",key:"name"},{title:"商品条码",dataIndex:"barcode",key:"barcode"},{title:"单价",dataIndex:"price",key:"price",align:"right"},{title:"数量（件）",dataIndex:"num",key:"num",align:"right"},{title:"金额",dataIndex:"amount",key:"amount",align:"right"}],loadGoodsData:function(){return new Promise((function(t){t({data:[{id:"1234561",name:"矿泉水 550ml",barcode:"12421432143214321",price:"2.00",num:"1",amount:"2.00"},{id:"1234562",name:"凉茶 300ml",barcode:"12421432143214322",price:"3.00",num:"2",amount:"6.00"},{id:"1234563",name:"好吃的薯片",barcode:"12421432143214323",price:"7.00",num:"4",amount:"28.00"},{id:"1234564",name:"特别好吃的蛋卷",barcode:"12421432143214324",price:"8.50",num:"3",amount:"25.50"}],pageSize:10,pageNo:1,totalPage:1,totalCount:10})})).then((function(t){return t}))},scheduleColumns:[{title:"时间",dataIndex:"time",key:"time"},{title:"当前进度",dataIndex:"rate",key:"rate"},{title:"状态",dataIndex:"status",key:"status",scopedSlots:{customRender:"status"}},{title:"操作员ID",dataIndex:"operator",key:"operator"},{title:"耗时",dataIndex:"cost",key:"cost"}],loadScheduleData:function(){return new Promise((function(t){t({data:[{key:"1",time:"2017-10-01 14:10",rate:"联系客户",status:"processing",operator:"取货员 ID1234",cost:"5mins"},{key:"2",time:"2017-10-01 14:05",rate:"取货员出发",status:"success",operator:"取货员 ID1234",cost:"1h"},{key:"3",time:"2017-10-01 13:05",rate:"取货员接单",status:"success",operator:"取货员 ID1234",cost:"5mins"},{key:"4",time:"2017-10-01 13:00",rate:"申请审批通过",status:"success",operator:"系统",cost:"1h"},{key:"5",time:"2017-10-01 12:00",rate:"发起退货申请",status:"success",operator:"用户",cost:"5mins"}],pageSize:10,pageNo:1,totalPage:1,totalCount:10})})).then((function(t){return t}))}}},filters:{statusFilter:function(t){var e={processing:"进行中",success:"完成",failed:"失败"};return e[t]}},computed:{title:function(){return this.$route.meta.title}}},u=d,p=(a("ae96"),a("2877")),m=Object(p["a"])(u,s,r,!1,null,"eaa54f7a",null);e["default"]=m.exports},"876f":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("a-form",{staticStyle:{margin:"40px auto 0"}},[a("result",{attrs:{title:"操作成功","is-success":!0,description:"预计两小时内到账"}},[a("div",{staticClass:"information"},[a("a-row",[a("a-col",{attrs:{sm:8,xs:24}},[t._v("付款账户：")]),a("a-col",{attrs:{sm:16,xs:24}},[t._v("<EMAIL>")])],1),a("a-row",[a("a-col",{attrs:{sm:8,xs:24}},[t._v("收款账户：")]),a("a-col",{attrs:{sm:16,xs:24}},[t._v("<EMAIL>")])],1),a("a-row",[a("a-col",{attrs:{sm:8,xs:24}},[t._v("收款人姓名：")]),a("a-col",{attrs:{sm:16,xs:24}},[t._v("辉夜")])],1),a("a-row",[a("a-col",{attrs:{sm:8,xs:24}},[t._v("转账金额：")]),a("a-col",{attrs:{sm:16,xs:24}},[a("span",{staticClass:"money"},[t._v("500")]),t._v(" 元")])],1)],1),a("div",{attrs:{slot:"action"},slot:"action"},[a("a-button",{attrs:{type:"primary"},on:{click:t.finish}},[t._v("再转一笔")]),a("a-button",{staticStyle:{"margin-left":"8px"},on:{click:t.toOrderList}},[t._v("查看账单")])],1)])],1)],1)},r=[],i=a("9a3d"),o={name:"Step3",components:{Result:i["default"]},data:function(){return{loading:!1}},methods:{finish:function(){this.$emit("finish")},toOrderList:function(){this.$router.push("/list/query-list")}}},n=o,l=(a("0975"),a("2877")),c=Object(l["a"])(n,s,r,!1,null,"9bb78af4",null);e["default"]=c.exports},8925:function(t,e,a){"use strict";var s=a("f4a1"),r=a.n(s);r.a},"923e":function(t,e,a){},"93c8":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("div",[s("a-card",{staticClass:"card",attrs:{title:"仓库管理",bordered:!1}},[s("repository-form",{ref:"repository",attrs:{showSubmit:!1}})],1),s("a-card",{staticClass:"card",attrs:{title:"任务管理",bordered:!1}},[s("task-form",{ref:"task",attrs:{showSubmit:!1}})],1),s("a-card",[s("form",{attrs:{autoFormCreate:function(e){return t.form=e}}},[s("a-table",{attrs:{columns:e.columns,dataSource:e.data,pagination:!1},scopedSlots:e._u([e._l(["name","workId","department"],(function(t,a){return{key:t,fn:function(r,i,o){return[i.editable?s("a-input",{key:t,staticStyle:{margin:"-5px 0"},attrs:{value:r,placeholder:e.columns[a].title},on:{change:function(a){return e.handleChange(a.target.value,i.key,t)}}}):[e._v(e._s(r))]]}}})),{key:"operation",fn:function(t,a,r){return[a.editable?[a.isNew?s("span",[s("a",{on:{click:function(t){return e.saveRow(a.key)}}},[e._v("添加")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-popconfirm",{attrs:{title:"是否要删除此行？"},on:{confirm:function(t){return e.remove(a.key)}}},[s("a",[e._v("删除")])])],1):s("span",[s("a",{on:{click:function(t){return e.saveRow(a.key)}}},[e._v("保存")]),s("a-divider",{attrs:{type:"vertical"}}),s("a",{on:{click:function(t){return e.cancel(a.key)}}},[e._v("取消")])],1)]:s("span",[s("a",{on:{click:function(t){return e.toggle(a.key)}}},[e._v("编辑")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-popconfirm",{attrs:{title:"是否要删除此行？"},on:{confirm:function(t){return e.remove(a.key)}}},[s("a",[e._v("删除")])])],1)]}}],null,!0)}),s("a-button",{staticStyle:{width:"100%","margin-top":"16px","margin-bottom":"8px"},attrs:{type:"dashed",icon:"plus"},on:{click:e.newMember}},[e._v("新增成员")])],1)]),s("footer-tool-bar",[s("a-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.validate}},[e._v("提交")])],1)],1)},r=[],i=a("ab9f"),o=a("bd8e"),n=a("c984");function l(t){return p(t)||u(t)||d(t)||c()}function c(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){if(t){if("string"===typeof t)return m(t,e);var a=Object.prototype.toString.call(t).slice(8,-1);return"Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?m(t,e):void 0}}function u(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}function p(t){if(Array.isArray(t))return m(t)}function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,s=new Array(e);a<e;a++)s[a]=t[a];return s}var f={name:"AdvancedForm",components:{FooterToolBar:n["default"],RepositoryForm:i["default"],TaskForm:o["default"]},data:function(){return{description:"高级表单常见于一次性输入和提交大批量数据的场景。",loading:!1,columns:[{title:"成员姓名",dataIndex:"name",key:"name",width:"20%",scopedSlots:{customRender:"name"}},{title:"工号",dataIndex:"workId",key:"workId",width:"20%",scopedSlots:{customRender:"workId"}},{title:"所属部门",dataIndex:"department",key:"department",width:"40%",scopedSlots:{customRender:"department"}},{title:"操作",key:"action",scopedSlots:{customRender:"operation"}}],data:[{key:"1",name:"小明",workId:"001",editable:!1,department:"行政部"},{key:"2",name:"李莉",workId:"002",editable:!1,department:"IT部"},{key:"3",name:"王小帅",workId:"003",editable:!1,department:"财务部"}]}},methods:{handleSubmit:function(t){t.preventDefault()},newMember:function(){this.data.push({key:"-1",name:"",workId:"",department:"",editable:!0,isNew:!0})},remove:function(t){var e=this.data.filter((function(e){return e.key!==t}));this.data=e},saveRow:function(t){var e=this.data.filter((function(e){return e.key===t}))[0];e.editable=!1,e.isNew=!1},toggle:function(t){var e=this.data.filter((function(e){return e.key===t}))[0];e.editable=!e.editable},getRowByKey:function(t,e){var a=this.data;return(e||a).filter((function(e){return e.key===t}))[0]},cancel:function(t){var e=this.data.filter((function(e){return e.key===t}))[0];e.editable=!1},handleChange:function(t,e,a){var s=l(this.data),r=s.filter((function(t){return e===t.key}))[0];r&&(r[a]=t,this.data=s)},validate:function(){var t=this;this.$refs.repository.form.validateFields((function(e,a){e||t.$notification["error"]({message:"Received values of form:",description:a})})),this.$refs.task.form.validateFields((function(e,a){e||t.$notification["error"]({message:"Received values of form:",description:a})}))}}},v=f,h=(a("2cfd"),a("2877")),y=Object(h["a"])(v,s,r,!1,null,"cf568c84",null);e["default"]=y.exports},"94bb":function(t,e,a){"use strict";a.r(e);for(var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-header-index-wide"},[a("a-row",{attrs:{gutter:24}},[a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"总销售额",total:"￥126,560"}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("trend",{staticStyle:{"margin-right":"16px"},attrs:{flag:"up"}},[a("span",{attrs:{slot:"term"},slot:"term"},[t._v("周同比")]),t._v("\n            12%\n          ")]),a("trend",{attrs:{flag:"down"}},[a("span",{attrs:{slot:"term"},slot:"term"},[t._v("日同比")]),t._v("\n            11%\n          ")])],1),a("template",{slot:"footer"},[t._v("日均销售额"),a("span",[t._v("￥ 234.56")])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"订单量",total:t._f("NumberFormat")(8846)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-area")],1),a("template",{slot:"footer"},[t._v("日订单量"),a("span",[t._v(" "+t._s(t._f("NumberFormat")("1234")))])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"支付笔数",total:t._f("NumberFormat")(6560)}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-bar",{attrs:{height:40}})],1),a("template",{slot:"footer"},[t._v("转化率 "),a("span",[t._v("60%")])])],2)],1),a("a-col",{style:{marginBottom:"24px"},attrs:{sm:24,md:12,xl:6}},[a("chart-card",{attrs:{loading:t.loading,title:"运营活动效果",total:"78%"}},[a("a-tooltip",{attrs:{slot:"action",title:"指标说明"},slot:"action"},[a("a-icon",{attrs:{type:"info-circle-o"}})],1),a("div",[a("mini-progress",{attrs:{color:"rgb(19, 194, 194)",target:80,percentage:78,height:8}})],1),a("template",{slot:"footer"},[a("trend",{staticStyle:{"margin-right":"16px"},attrs:{flag:"down"}},[a("span",{attrs:{slot:"term"},slot:"term"},[t._v("同周比")]),t._v("\n            12%\n          ")]),a("trend",{attrs:{flag:"up"}},[a("span",{attrs:{slot:"term"},slot:"term"},[t._v("日环比")]),t._v("\n            80%\n          ")])],1)],2)],1)],1),a("a-card",{attrs:{loading:t.loading,bordered:!1,"body-style":{padding:"0"}}},[a("div",{staticClass:"salesCard"},[a("a-tabs",{attrs:{"default-active-key":"1",size:"large","tab-bar-style":{marginBottom:"24px",paddingLeft:"16px"}}},[a("div",{staticClass:"extra-wrapper",attrs:{slot:"tabBarExtraContent"},slot:"tabBarExtraContent"},[a("div",{staticClass:"extra-item"},[a("a",[t._v("今日")]),a("a",[t._v("本周")]),a("a",[t._v("本月")]),a("a",[t._v("本年")])]),a("a-range-picker",{style:{width:"256px"}})],1),a("a-tab-pane",{key:"1",attrs:{loading:"true",tab:"销售额"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("bar",{attrs:{title:"销售额排行",dataSource:t.barData}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("rank-list",{attrs:{title:"门店销售排行榜",list:t.rankList}})],1)],1)],1),a("a-tab-pane",{key:"2",attrs:{tab:"销售趋势"}},[a("a-row",[a("a-col",{attrs:{xl:16,lg:12,md:12,sm:24,xs:24}},[a("bar",{attrs:{title:"销售额趋势",dataSource:t.barData}})],1),a("a-col",{attrs:{xl:8,lg:12,md:12,sm:24,xs:24}},[a("rank-list",{attrs:{title:"门店销售排行榜",list:t.rankList}})],1)],1)],1)],1)],1)]),a("a-row",[a("a-col",{attrs:{span:24}},[a("a-card",{style:{marginTop:"24px"},attrs:{loading:t.loading,bordered:!1,title:"最近一周访问量统计"}},[a("a-row",[a("a-col",{attrs:{span:6}},[a("head-info",{attrs:{title:"今日IP",content:t.loginfo.todayIp}})],1),a("a-col",{attrs:{span:2}},[a("a-spin",{staticClass:"circle-cust"},[a("a-icon",{staticStyle:{"font-size":"24px"},attrs:{slot:"indicator",type:"environment"},slot:"indicator"})],1)],1),a("a-col",{attrs:{span:6}},[a("head-info",{attrs:{title:"今日访问",content:t.loginfo.todayVisitCount}})],1),a("a-col",{attrs:{span:2}},[a("a-spin",{staticClass:"circle-cust"},[a("a-icon",{staticStyle:{"font-size":"24px"},attrs:{slot:"indicator",type:"team"},slot:"indicator"})],1)],1),a("a-col",{attrs:{span:6}},[a("head-info",{attrs:{title:"总访问量",content:t.loginfo.totalVisitCount}})],1),a("a-col",{attrs:{span:2}},[a("a-spin",{staticClass:"circle-cust"},[a("a-icon",{staticStyle:{"font-size":"24px"},attrs:{slot:"indicator",type:"rise"},slot:"indicator"})],1)],1)],1),a("line-chart-multid",{attrs:{fields:t.visitFields,dataSource:t.visitInfo}})],1)],1)],1)],1)},r=[],i=a("05ed"),o=a("da05"),n=a("3896"),l=a("1d43"),c=a("942d"),d=a("bf13"),u=a("0923"),p=a("edd9"),m=a("4ec6"),f=a("81d1"),v=a("611e"),h=a("4ec3"),y=[],b=0;b<7;b++)y.push({name:"白鹭岛 "+(b+1)+" 号店",total:1234.56-100*b});for(var g=[],C=0;C<12;C+=1)g.push({x:"".concat(C+1,"月"),y:Math.floor(1e3*Math.random())+200});var w={name:"IndexChart",components:{ATooltip:n["a"],ACol:o["b"],ChartCard:i["default"],MiniArea:l["default"],MiniBar:c["default"],MiniProgress:d["default"],RankList:u["default"],Bar:p["default"],Trend:v["a"],LineChartMultid:m["default"],HeadInfo:f["default"]},data:function(){var t=this.$createElement;return{loading:!0,center:null,rankList:y,barData:g,loginfo:{},visitFields:["ip","visit"],visitInfo:[],indicator:t("a-icon",{attrs:{type:"loading",spin:!0},style:"font-size: 24px"})}},created:function(){var t=this;setTimeout((function(){t.loading=!t.loading}),1e3),this.initLogInfo()},methods:{initLogInfo:function(){var t=this;Object(h["u"])(null).then((function(e){e.success&&(Object.keys(e.result).forEach((function(t){e.result[t]=e.result[t]+""})),t.loginfo=e.result)})),Object(h["B"])().then((function(e){e.success&&(t.visitInfo=e.result)}))}}},x=w,_=(a("60df"),a("2877")),S=Object(_["a"])(x,s,r,!1,null,"0d7d1549",null);e["default"]=S.exports},a1e6:function(t,e,a){"use strict";var s=a("161b"),r=a.n(s);r.a},a896:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("a-card",{attrs:{bordered:!1}},[s("div",{staticClass:"table-page-search-wrapper"},[s("a-form",{attrs:{layout:"inline"}},[s("a-row",{attrs:{gutter:48}},[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"规则编号"}},[s("a-input",{attrs:{placeholder:""},model:{value:e.queryParam.id,callback:function(t){e.$set(e.queryParam,"id",t)},expression:"queryParam.id"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"},model:{value:e.queryParam.status,callback:function(t){e.$set(e.queryParam,"status",t)},expression:"queryParam.status"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),e.advanced?[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"调用次数"}},[s("a-input-number",{staticStyle:{width:"100%"},model:{value:e.queryParam.callNo,callback:function(t){e.$set(e.queryParam,"callNo",t)},expression:"queryParam.callNo"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"更新日期"}},[s("a-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入更新日期"},model:{value:e.queryParam.date,callback:function(t){e.$set(e.queryParam,"date",t)},expression:"queryParam.date"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"},model:{value:e.queryParam.useStatus,callback:function(t){e.$set(e.queryParam,"useStatus",t)},expression:"queryParam.useStatus"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1)]:e._e(),s("a-col",{attrs:{md:e.advanced?24:8,sm:24}},[s("span",{staticClass:"table-page-search-submitButtons",style:e.advanced&&{float:"right",overflow:"hidden"}||{}},[s("a-button",{attrs:{type:"primary"}},[e._v("查询")]),s("a-button",{staticStyle:{"margin-left":"8px"},on:{click:e.resetSearchForm}},[e._v("重置")]),s("a",{staticStyle:{"margin-left":"8px"},on:{click:e.toggleAdvanced}},[e._v("\n              "+e._s(e.advanced?"收起":"展开")+"\n              "),s("a-icon",{attrs:{type:e.advanced?"up":"down"}})],1)],1)])],2)],1)],1),s("div",{staticClass:"table-operator"},[s("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(){return t.handleModalVisible(!0)}}},[e._v("新建")]),e.selectedRowKeys.length>0?s("a-dropdown",[s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("a-menu-item",{key:"1"},[s("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1),s("a-menu-item",{key:"2"},[s("a-icon",{attrs:{type:"lock"}}),e._v("锁定")],1)],1),s("a-button",{staticStyle:{"margin-left":"8px"}},[e._v("\n        批量操作 "),s("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),s("s-table",{ref:"table",attrs:{size:"default",columns:e.columns,data:e.loadData,showAlertInfo:!0},on:{onSelect:e.onChange},scopedSlots:e._u([{key:"action",fn:function(t,a){return s("span",{},[s("a",{on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-dropdown",[s("a",{staticClass:"ant-dropdown-link"},[e._v("\n          更多 "),s("a-icon",{attrs:{type:"down"}})],1),s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("详情")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("禁用")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("删除")])])],1)],1)],1)}}])}),s("a-modal",{attrs:{title:"操作",width:800},on:{ok:e.handleOk},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[s("a-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"规则编号",hasFeedback:"",validateStatus:"success"}},[s("a-input",{attrs:{placeholder:"规则编号",id:"no"},model:{value:e.mdl.no,callback:function(t){e.$set(e.mdl,"no",t)},expression:"mdl.no"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"服务调用次数",hasFeedback:"",validateStatus:"success"}},[s("a-input-number",{staticStyle:{width:"100%"},attrs:{min:1,id:"callNo"},model:{value:e.mdl.callNo,callback:function(t){e.$set(e.mdl,"callNo",t)},expression:"mdl.callNo"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态",hasFeedback:"",validateStatus:"warning"}},[s("a-select",{attrs:{defaultValue:"1"},model:{value:e.mdl.status,callback:function(t){e.$set(e.mdl,"status",t)},expression:"mdl.status"}},[s("a-select-option",{attrs:{value:"1"}},[e._v("Option 1")]),s("a-select-option",{attrs:{value:"2"}},[e._v("Option 2")]),s("a-select-option",{attrs:{value:"3"}},[e._v("Option 3")])],1)],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述",hasFeedback:"",help:"请填写一段描述"}},[s("a-textarea",{attrs:{rows:5,placeholder:"...",id:"description"},model:{value:e.mdl.description,callback:function(t){e.$set(e.mdl,"description",t)},expression:"mdl.description"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"更新时间",hasFeedback:"",validateStatus:"error"}},[s("a-date-picker",{staticStyle:{width:"100%"},attrs:{showTime:"",format:"YYYY-MM-DD HH:mm:ss",placeholder:"Select Time"}})],1)],1)],1),s("a-modal",{attrs:{title:"新建规则",destroyOnClose:"",visible:e.visibleCreateModal},on:{ok:e.handleCreateModalOk,cancel:e.handleCreateModalCancel}},[s("a-form",{staticStyle:{"margin-top":"8px"},attrs:{autoFormCreate:function(e){t.createForm=e}}},[s("a-form-item",{attrs:{labelCol:{span:5},wrapperCol:{span:15},label:"描述",fieldDecoratorId:"description",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入至少五个字符的规则描述！",min:5}]}}},[s("a-input",{attrs:{placeholder:"请输入"}})],1)],1)],1)],1)},r=[],i=a("e8c4"),o=a("261e"),n=a("27e3"),l=a("c1df"),c=a.n(l),d=a("bc3a"),u=a.n(d),p=a("0fea"),m={name:"TableList",components:{AInput:n["a"],ATextarea:o["a"],STable:i["a"]},data:function(){var t=this;return{visibleCreateModal:!1,visible:!1,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:12}},form:null,mdl:{},advanced:!0,queryParam:{},columns:[{title:"规则编号",dataIndex:"no"},{title:"描述",dataIndex:"description"},{title:"服务调用次数",dataIndex:"callNo",sorter:!0,needTotal:!0,customRender:function(t){return t+" 次"}},{title:"状态",dataIndex:"status",needTotal:!0},{title:"更新时间",dataIndex:"updatedAt",sorter:!0},{table:"操作",dataIndex:"action",width:"150px",scopedSlots:{customRender:"action"}}],loadData:function(e){return Object(p["g"])(Object.assign(e,t.queryParam)).then((function(t){return t.result}))},selectedRowKeys:[],selectedRows:[]}},created:function(){Object(p["f"])({t:new Date})},methods:{handleEdit:function(t){this.mdl=Object.assign({},t),this.visible=!0},handleOk:function(){},handleModalVisible:function(t){this.visibleCreateModal=t},handleCreateModalOk:function(){var t=this;this.createForm.validateFields((function(e,a){if(!e){var s=t.createForm.getFieldValue("description");u.a.post("/saveRule",{desc:s}).then((function(e){t.createForm.resetFields(),t.visibleCreateModal=!1,t.loadRuleData()}))}}))},handleCreateModalCancel:function(){this.visibleCreateModal=!1},onChange:function(t){this.selectedRowKeys=t.selectedRowKeys,this.selectedRows=t.selectedRows},toggleAdvanced:function(){this.advanced=!this.advanced},resetSearchForm:function(){this.queryParam={date:c()(new Date)}}},watch:{}},f=m,v=a("2877"),h=Object(v["a"])(f,s,r,!1,null,null,null);e["default"]=h.exports},a90c:function(t,e,a){},aa07:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"search-content"},[a("router-view")],1)},r=[],i={name:"SearchLayout",data:function(){var t=this;return{tabs:{items:[{key:"1",title:"文章"},{key:"2",title:"项目"},{key:"3",title:"应用"}],active:function(){switch(t.$route.path){case"/list/search/article":return"1";case"/list/search/project":return"2";case"/list/search/application":return"3";default:return"1"}},callback:function(e){switch(e){case"1":t.$router.push("/list/search/article");break;case"2":t.$router.push("/list/search/project");break;case"3":t.$router.push("/list/search/application");break;default:t.$router.push("/workplace")}}},search:!0}},computed:{},methods:{}},o=i,n=(a("e85c"),a("2877")),l=Object(n["a"])(o,s,r,!1,null,"939525ce",null);e["default"]=l.exports},aac8:function(t,e,a){"use strict";a.r(e);for(var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"content",staticClass:"card-list"},[a("a-list",{attrs:{grid:{gutter:24,lg:3,md:2,sm:1,xs:1},dataSource:t.dataSource},scopedSlots:t._u([{key:"renderItem",fn:function(e,s){return a("a-list-item",{},[0===s?[a("a-button",{staticClass:"new-btn",attrs:{type:"dashed"}},[a("a-icon",{attrs:{type:"plus"}}),t._v("\n          新增产品\n        ")],1)]:[a("a-card",{attrs:{hoverable:!0}},[a("a-card-meta",[a("div",{staticStyle:{"margin-bottom":"3px"},attrs:{slot:"title"},slot:"title"},[t._v(t._s(e.title))]),a("a-avatar",{staticClass:"card-avatar",attrs:{slot:"avatar",src:e.avatar,size:"large"},slot:"avatar"}),a("div",{staticClass:"meta-content",attrs:{slot:"description"},slot:"description"},[t._v(t._s(e.content))])],1),a("template",{staticClass:"ant-card-actions",slot:"actions"},[a("a",[t._v("操作一")]),a("a",[t._v("操作二")])])],2)]],2)}}])})],1)},r=[],i=[],o=0;o<12;o++)i.push({title:"Alipay",avatar:"https://gw.alipayobjects.com/zos/rmsportal/WdGqmHpayyMjiEhcKoVE.png",content:"在中台产品的研发过程中，会出现不同的设计规范和实现方式，但其中往往存在很多类似的页面和组件，这些类似的组件会被抽离成一套标准规范。"});var n={name:"CardList",data:function(){return{description:"段落示意：蚂蚁金服务设计平台 ant.design，用最小的工作量，无缝接入蚂蚁金服生态， 提供跨越设计与开发的体验解决方案。",linkList:[{icon:"rocket",href:"#",title:"快速开始"},{icon:"info-circle-o",href:"#",title:"产品简介"},{icon:"file-text",href:"#",title:"产品文档"}],extraImage:"https://gw.alipayobjects.com/zos/rmsportal/RzwpdLnhmvDJToTdfDPe.png",dataSource:i}}},l=n,c=(a("47d2"),a("2877")),d=Object(c["a"])(l,s,r,!1,null,"738591f9",null);e["default"]=d.exports},ab9f:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-form",{staticClass:"form",attrs:{form:t.form},on:{submit:t.handleSubmit}},[a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:6,md:12,sm:24}},[a("a-form-item",{attrs:{label:"仓库名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.name",{rules:[{required:!0,message:"请输入仓库名称",whitespace:!0}]}],expression:"[\n            'repository.name',\n            {rules: [{ required: true, message: '请输入仓库名称', whitespace: true}]}\n          ]"}],attrs:{placeholder:"请输入仓库名称"}})],1)],1),a("a-col",{attrs:{xl:{span:7,offset:1},lg:{span:8},md:{span:12},sm:24}},[a("a-form-item",{attrs:{label:"仓库域名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.domain",{rules:[{required:!0,message:"请输入仓库域名",whitespace:!0},{validator:t.validate}]}],expression:"[\n            'repository.domain',\n            {rules: [{ required: true, message: '请输入仓库域名', whitespace: true}, {validator: validate}]}\n          ]"}],attrs:{addonBefore:"http://",addonAfter:".com",placeholder:"请输入"}})],1)],1),a("a-col",{attrs:{xl:{span:9,offset:1},lg:{span:10},md:{span:24},sm:24}},[a("a-form-item",{attrs:{label:"仓库管理员"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.manager",{rules:[{required:!0,message:"请选择管理员"}]}],expression:"[ 'repository.manager', {rules: [{ required: true, message: '请选择管理员'}]} ]"}],attrs:{placeholder:"请选择管理员"}},[a("a-select-option",{attrs:{value:"王同学"}},[t._v("王同学")]),a("a-select-option",{attrs:{value:"李同学"}},[t._v("李同学")]),a("a-select-option",{attrs:{value:"黄同学"}},[t._v("黄同学")])],1)],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:6,md:12,sm:24}},[a("a-form-item",{attrs:{label:"审批人"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.auditor",{rules:[{required:!0,message:"请选择审批员"}]}],expression:"[ 'repository.auditor', {rules: [{ required: true, message: '请选择审批员'}]} ]"}],attrs:{placeholder:"请选择审批员"}},[a("a-select-option",{attrs:{value:"王晓丽"}},[t._v("王晓丽")]),a("a-select-option",{attrs:{value:"李军"}},[t._v("李军")])],1)],1)],1),a("a-col",{attrs:{xl:{span:7,offset:1},lg:{span:8},md:{span:12},sm:24}},[a("a-form-item",{attrs:{label:"生效日期"}},[a("a-range-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.effectiveDate",{rules:[{required:!0,message:"请选择生效日期"}]}],expression:"[\n            'repository.effectiveDate',\n            {rules: [{ required: true, message: '请选择生效日期'}]}\n          ]"}],staticStyle:{width:"100%"}})],1)],1),a("a-col",{attrs:{xl:{span:9,offset:1},lg:{span:10},md:{span:24},sm:24}},[a("a-form-item",{attrs:{label:"仓库类型"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["repository.type",{rules:[{required:!0,message:"请选择仓库类型"}]}],expression:"[\n            'repository.type',\n            {rules: [{ required: true, message: '请选择仓库类型'}]}\n          ]"}],attrs:{placeholder:"请选择仓库类型"}},[a("a-select-option",{attrs:{value:"公开"}},[t._v("公开")]),a("a-select-option",{attrs:{value:"私密"}},[t._v("私密")])],1)],1)],1)],1),t.showSubmit?a("a-form-item",[a("a-button",{attrs:{htmlType:"submit"}},[t._v("Submit")])],1):t._e()],1)},r=[],i={name:"RepositoryForm",props:{showSubmit:{type:Boolean,default:!1}},data:function(){return{form:this.$form.createForm(this)}},methods:{handleSubmit:function(t){var e=this;t.preventDefault(),this.form.validateFields((function(t,a){t||e.$notification["error"]({message:"Received values of form:",description:a})}))},validate:function(t,e,a){var s=/^user-(.*)$/;s.test(e)||a("需要以 user- 开头"),a()}}},o=i,n=a("2877"),l=Object(n["a"])(o,s,r,!1,null,"9198d6ee",null);e["default"]=l.exports},ae96:function(t,e,a){"use strict";var s=a("b225"),r=a.n(s);r.a},b225:function(t,e,a){},b7ac:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-modal",{attrs:{title:"操作",width:800,visible:t.visible,confirmLoading:t.confirmLoading},on:{ok:t.handleOk,cancel:t.handleCancel}},[a("a-spin",{attrs:{spinning:t.confirmLoading}},[a("a-form",{attrs:{form:t.form}},[a("a-form-item",{attrs:{labelCol:t.labelCol,wrapperCol:t.wrapperCol,label:"唯一识别码",hasFeedback:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["id",{rules:[]}],expression:"[ 'id', {rules: []} ]"}],attrs:{placeholder:"唯一识别码",disabled:"disabled"}})],1),a("a-form-item",{attrs:{labelCol:t.labelCol,wrapperCol:t.wrapperCol,label:"角色名称",hasFeedback:""}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["name",{rules:[{required:!0,message:"不起一个名字吗？"}]}],expression:"[ 'name', {rules: [{ required: true, message: '不起一个名字吗？' }] }]"}],attrs:{placeholder:"起一个名字"}})],1),a("a-form-item",{attrs:{labelCol:t.labelCol,wrapperCol:t.wrapperCol,label:"状态",hasFeedback:""}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["status",{rules:[]}],expression:"[ 'status', {rules: []} ]"}]},[a("a-select-option",{attrs:{value:1}},[t._v("正常")]),a("a-select-option",{attrs:{value:2}},[t._v("禁用")])],1)],1),a("a-form-item",{attrs:{labelCol:t.labelCol,wrapperCol:t.wrapperCol,label:"描述",hasFeedback:""}},[a("a-textarea",{directives:[{name:"decorator",rawName:"v-decorator",value:["describe",{rules:[]}],expression:"[ 'describe', { rules: [] } ]"}],attrs:{rows:5,placeholder:"..."}})],1),a("a-divider"),a("a-form-item",{attrs:{labelCol:t.labelCol,wrapperCol:t.wrapperCol,label:"拥有权限",hasFeedback:""}},t._l(t.permissions,(function(e,s){return a("a-row",{key:s,attrs:{gutter:16}},[a("a-col",{attrs:{span:4}},[t._v("\n            "+t._s(e.name)+"：\n          ")]),a("a-col",{attrs:{span:20}},[e.actionsOptions.length>0?a("a-checkbox",{attrs:{indeterminate:e.indeterminate,checked:e.checkedAll},on:{change:function(a){return t.onChangeCheckAll(a,e)}}},[t._v("\n              全选\n            ")]):t._e(),a("a-checkbox-group",{attrs:{options:e.actionsOptions},on:{change:function(a){return t.onChangeCheck(e)}},model:{value:e.selected,callback:function(a){t.$set(e,"selected",a)},expression:"permission.selected"}})],1)],1)})),1)],1)],1)],1)},r=[],i=a("0fea"),o=a("bade"),n=a("88bc"),l=a.n(n),c={name:"RoleModal",data:function(){return{labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},visible:!1,confirmLoading:!1,mdl:{},form:this.$form.createForm(this),permissions:[]}},created:function(){this.loadPermissions()},methods:{add:function(){this.edit({id:0})},edit:function(t){var e=this;if(this.mdl=Object.assign({},t),this.visible=!0,this.mdl.permissions&&this.permissions){var a={};this.mdl.permissions.forEach((function(t){a[t.permissionId]=t.actionEntitySet.map((function(t){return t.action}))})),this.permissions.forEach((function(t){t.selected=a[t.id]}))}this.$nextTick((function(){e.form.setFieldsValue(l()(e.mdl,"id","name","status","describe"))}))},close:function(){this.$emit("close"),this.visible=!1},handleOk:function(){var t=this;this.form.validateFields((function(e,a){e||(t.confirmLoading=!0,new Promise((function(t){setTimeout((function(){return t()}),2e3)})).then((function(){t.$message.success("保存成功"),t.$emit("ok")})).catch((function(){})).finally((function(){t.confirmLoading=!1,t.close()})))}))},handleCancel:function(){this.close()},onChangeCheck:function(t){t.indeterminate=!!t.selected.length&&t.selected.length<t.actionsOptions.length,t.checkedAll=t.selected.length===t.actionsOptions.length},onChangeCheckAll:function(t,e){Object.assign(e,{selected:t.target.checked?e.actionsOptions.map((function(t){return t.value})):[],indeterminate:!1,checkedAll:t.target.checked})},loadPermissions:function(){var t=this;Object(i["e"])().then((function(e){var a=e.result;t.permissions=a.map((function(t){var e=Object(o["a"])(t.actionData);return t.checkedAll=!1,t.selected=[],t.indeterminate=!1,t.actionsOptions=e.map((function(t){return{label:t.describe,value:t.action}})),t}))}))}}},d=c,u=a("2877"),p=Object(u["a"])(d,s,r,!1,null,"46bbfa96",null);e["default"]=p.exports},bd8e:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("a-form",{staticClass:"form",attrs:{form:t.form},on:{submit:t.handleSubmit}},[a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:6,md:12,sm:24}},[a("a-form-item",{attrs:{label:"任务名"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.name",{rules:[{required:!0,message:"请输入任务名称",whitespace:!0}]}],expression:"[ 'task.name', {rules: [{ required: true, message: '请输入任务名称', whitespace: true}]} ]"}],attrs:{placeholder:"请输入任务名称"}})],1)],1),a("a-col",{attrs:{xl:{span:7,offset:1},lg:{span:8},md:{span:12},sm:24}},[a("a-form-item",{attrs:{label:"任务描述"}},[a("a-input",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.description",{rules:[{required:!0,message:"请输入任务描述",whitespace:!0}]}],expression:"[ 'task.description', {rules: [{ required: true, message: '请输入任务描述', whitespace: true}]} ]"}],attrs:{placeholder:"请输入任务描述"}})],1)],1),a("a-col",{attrs:{xl:{span:9,offset:1},lg:{span:10},md:{span:24},sm:24}},[a("a-form-item",{attrs:{label:"执行人"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.executor",{rules:[{required:!0,message:"请选择执行人"}]}],expression:"[\n            'task.executor',\n            {rules: [{ required: true, message: '请选择执行人'}]}\n          ]"}],attrs:{placeholder:"请选择执行人"}},[a("a-select-option",{attrs:{value:"黄丽丽"}},[t._v("黄丽丽")]),a("a-select-option",{attrs:{value:"李大刀"}},[t._v("李大刀")])],1)],1)],1)],1),a("a-row",{staticClass:"form-row",attrs:{gutter:16}},[a("a-col",{attrs:{lg:6,md:12,sm:24}},[a("a-form-item",{attrs:{label:"责任人"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.manager",{rules:[{required:!0,message:"请选择责任人"}]}],expression:"[\n            'task.manager',\n            {rules: [{ required: true, message: '请选择责任人'}]}\n          ]"}],attrs:{placeholder:"请选择责任人"}},[a("a-select-option",{attrs:{value:"王伟"}},[t._v("王伟")]),a("a-select-option",{attrs:{value:"李红军"}},[t._v("李红军")])],1)],1)],1),a("a-col",{attrs:{xl:{span:7,offset:1},lg:{span:8},md:{span:12},sm:24}},[a("a-form-item",{attrs:{label:"提醒时间"}},[a("a-time-picker",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.time",{rules:[{required:!0,message:"请选择提醒时间"}]}],expression:"[\n            'task.time',\n            {rules: [{ required: true, message: '请选择提醒时间'}]}\n          ]"}],staticStyle:{width:"100%"}})],1)],1),a("a-col",{attrs:{xl:{span:9,offset:1},lg:{span:10},md:{span:24},sm:24}},[a("a-form-item",{attrs:{label:"任务类型"}},[a("a-select",{directives:[{name:"decorator",rawName:"v-decorator",value:["task.type",{rules:[{required:!0,message:"请选择任务类型"}]}],expression:"[ 'task.type', {rules: [{ required: true, message: '请选择任务类型'}]} ]"}],attrs:{placeholder:"请选择任务类型"}},[a("a-select-option",{attrs:{value:"定时执行"}},[t._v("定时执行")]),a("a-select-option",{attrs:{value:"周期执行"}},[t._v("周期执行")])],1)],1)],1)],1),t.showSubmit?a("a-form-item",[a("a-button",{attrs:{htmlType:"submit"}},[t._v("Submit")])],1):t._e()],1)},r=[],i={name:"TaskForm",props:{showSubmit:{type:Boolean,default:!1}},data:function(){return{form:this.$form.createForm(this)}},methods:{handleSubmit:function(t){var e=this;t.preventDefault(),this.form.validateFields((function(t,a){t||e.$notification["error"]({message:"Received values of form:",description:a})}))}}},o=i,n=a("2877"),l=Object(n["a"])(o,s,r,!1,null,"067739de",null);e["default"]=l.exports},bfad:function(t,e,a){},cc89:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("exception-page",{attrs:{type:"404"}})},r=[],i=a("0673"),o={components:{ExceptionPage:i["default"]}},n=o,l=a("2877"),c=Object(l["a"])(n,s,r,!1,null,"ec864426",null);e["default"]=c.exports},cce1:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"server-error-container"},[a("div",{staticClass:"grid-background"}),t._m(0),t._m(1),t._m(2),a("div",{staticClass:"error-card card-primary"},[t._m(3),a("div",{staticClass:"error-info"},[a("h1",{staticClass:"error-title"},[t._v("服务器连接异常")]),a("p",{staticClass:"error-subtitle"},[t._v("Server Connection Failed")]),t._m(4),a("div",{staticClass:"contact-card card-secondary"},[a("h3",{staticClass:"contact-title"},[t._v("需要帮助？请联系我们：")]),a("div",{staticClass:"contact-methods"},[a("div",{staticClass:"contact-item"},[a("a-icon",{staticClass:"contact-icon",attrs:{type:"phone"}}),a("span",[t._v("技术支持热线：************")])],1),a("div",{staticClass:"contact-item"},[a("a-icon",{staticClass:"contact-icon",attrs:{type:"mail"}}),a("span",[t._v("邮箱：<EMAIL>")])],1),a("div",{staticClass:"contact-item"},[a("a-icon",{staticClass:"contact-icon",attrs:{type:"wechat"}}),a("span",[t._v("微信客服：ZhiJie_AIGC")])],1),a("div",{staticClass:"contact-item"},[a("a-icon",{staticClass:"contact-icon",attrs:{type:"qq"}}),a("span",[t._v("QQ群：123456789")])],1)])]),a("div",{staticClass:"action-buttons"},[a("button",{staticClass:"btn-primary",attrs:{disabled:t.retrying},on:{click:t.retryConnection}},[a("a-icon",{attrs:{type:"reload",spin:t.retrying}}),t._v("\n          "+t._s(t.retrying?"重新连接中...":"重新连接")+"\n        ")],1),a("button",{staticClass:"btn-secondary",on:{click:t.goHome}},[a("a-icon",{attrs:{type:"home"}}),t._v("\n          返回首页\n        ")],1),a("button",{staticClass:"btn-secondary",on:{click:t.contactSupport}},[a("a-icon",{attrs:{type:"customer-service"}}),t._v("\n          联系客服\n        ")],1)]),a("div",{staticClass:"error-code"},[a("p",[t._v("错误代码："+t._s(t.errorCode))]),a("p",[t._v("时间："+t._s(t.errorTime))])])])]),t._m(5)])},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"floating-elements"},[a("div",{staticClass:"element element-1"},[a("div",{staticClass:"element-core"})]),a("div",{staticClass:"element element-2"},[a("div",{staticClass:"element-core"})]),a("div",{staticClass:"element element-3"},[a("div",{staticClass:"element-core"})]),a("div",{staticClass:"element element-4"},[a("div",{staticClass:"element-core"})])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"scan-lines"},[a("div",{staticClass:"scan-line scan-line-1"}),a("div",{staticClass:"scan-line scan-line-2"}),a("div",{staticClass:"scan-line scan-line-3"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"data-streams"},[a("div",{staticClass:"stream stream-1"}),a("div",{staticClass:"stream stream-2"}),a("div",{staticClass:"stream stream-3"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"error-icon"},[a("div",{staticClass:"server-icon"},[a("div",{staticClass:"server-body"},[a("div",{staticClass:"server-light red"}),a("div",{staticClass:"server-light yellow"}),a("div",{staticClass:"server-light green"}),a("div",{staticClass:"server-screen"},[a("div",{staticClass:"screen-line"}),a("div",{staticClass:"screen-line"}),a("div",{staticClass:"screen-line"})])]),a("div",{staticClass:"server-base"}),a("div",{staticClass:"server-glow"})]),a("div",{staticClass:"error-symbol"},[t._v("⚠")]),a("div",{staticClass:"error-particles"},[a("div",{staticClass:"particle particle-1"}),a("div",{staticClass:"particle particle-2"}),a("div",{staticClass:"particle particle-3"}),a("div",{staticClass:"particle particle-4"})])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"error-details"},[a("p",{staticClass:"main-message"},[t._v("\n          抱歉，智界AIGC服务暂时无法访问\n        ")]),a("p",{staticClass:"sub-message"},[t._v("\n          我们的技术团队正在努力修复此问题，请稍后再试\n        ")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"footer-info"},[a("p",[t._v("© 2025 "),a("span",{staticClass:"brand-text"},[t._v("智界AIGC")]),t._v(" - 专业的AI内容生成平台")]),a("p",[t._v("如问题持续存在，请保存错误代码并联系技术支持")])])}],i=a("a34a"),o=a.n(i),n=a("cffa"),l=a("33a0"),c=a("0a2e");function d(t,e,a,s,r,i,o){try{var n=t[i](o),l=n.value}catch(c){return void a(c)}n.done?e(l):Promise.resolve(l).then(s,r)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(s,r){var i=t.apply(e,a);function o(t){d(i,s,r,o,n,"next",t)}function n(t){d(i,s,r,o,n,"throw",t)}o(void 0)}))}}n["a"].registerPlugin(l["a"],c["a"]);var p={name:"ServerError",data:function(){return{retrying:!1,errorCode:"SRV_CONNECTION_REFUSED_001",errorTime:(new Date).toLocaleString("zh-CN"),animationTimeline:null}},mounted:function(){var t=this;this.$nextTick((function(){setTimeout((function(){t.initAnimations()}),100)})),setTimeout((function(){var t=[".contact-card",".error-card",".action-buttons",".action-buttons .btn-primary",".action-buttons .btn-secondary",".error-title",".error-subtitle",".error-details"];t.forEach((function(t){var e=document.querySelectorAll(t);e.forEach((function(t){if(t){var e=getComputedStyle(t);"0"!==e.opacity&&"hidden"!==e.visibility||(t.style.opacity="1",t.style.visibility="visible",t.style.transform="none",t.style.display="BUTTON"===t.tagName?"inline-block":"block")}}))}))}),1500)},beforeDestroy:function(){this.animationTimeline&&this.animationTimeline.kill()},methods:{initAnimations:function(){var t=[".error-card",".error-icon",".error-title",".error-subtitle",".error-details",".contact-card",".action-buttons",".error-code"];t.forEach((function(t){document.querySelector(t)})),n["a"].set([".error-card",".error-icon",".error-title",".error-subtitle",".error-details",".contact-card",".action-buttons",".action-buttons button",".error-code"],{opacity:1,transform:"none",clearProps:"all"}),this.animationTimeline=n["a"].timeline({onComplete:function(){n["a"].set([".contact-card",".action-buttons",".action-buttons button"],{opacity:1,clearProps:"transform"})}}),n["a"].from(".error-card",{duration:1,scale:.8,opacity:0,y:50,ease:"back.out(1.7)",onComplete:function(){n["a"].set(".error-card *",{opacity:1})}}),n["a"].from(".error-icon",{duration:.8,scale:0,rotation:180,ease:"elastic.out(1, 0.5)",delay:.3,onComplete:function(){}}),n["a"].from(".error-title",{duration:.8,y:30,opacity:0,ease:"power2.out",delay:.5,onComplete:function(){}}),n["a"].from(".error-subtitle",{duration:.6,y:20,opacity:0,ease:"power2.out",delay:.7,onComplete:function(){}}),n["a"].from(".error-details",{duration:.8,y:30,opacity:0,ease:"power2.out",delay:.9,onComplete:function(){}}),n["a"].from(".contact-card",{duration:.8,y:40,opacity:0,ease:"power2.out",delay:1.1,onComplete:function(){var t=document.querySelector(".contact-card");t&&(t.style.opacity="1",t.style.visibility="visible")}}),n["a"].from(".btn-primary",{duration:.6,y:30,opacity:0,ease:"back.out(1.7)",delay:1.3,onComplete:function(){var t=document.querySelector(".btn-primary");t&&(t.style.opacity="1",t.style.visibility="visible")}}),n["a"].from(".btn-secondary",{duration:.6,y:30,opacity:0,ease:"back.out(1.7)",delay:1.4,stagger:.1,onComplete:function(){var t=document.querySelectorAll(".btn-secondary");t.forEach((function(t){t.style.opacity="1",t.style.visibility="visible"}))}}),n["a"].from(".error-code",{duration:.6,opacity:0,ease:"power2.out",delay:1.6,onComplete:function(){}}),this.initServerLightAnimation(),this.initFloatingAnimation(),this.initGridAnimation(),this.initErrorSymbolAnimation()},initServerLightAnimation:function(){n["a"].to(".server-light.red",{opacity:.3,duration:.8,repeat:-1,yoyo:!0,ease:"power2.inOut"}),n["a"].to(".server-light.yellow",{opacity:.6,duration:1.2,repeat:-1,yoyo:!0,ease:"power2.inOut",delay:.3}),n["a"].to(".server-light.green",{opacity:.2,duration:2,repeat:-1,yoyo:!0,ease:"power2.inOut",delay:.6})},initFloatingAnimation:function(){n["a"].to(".element-1",{y:-20,rotation:360,duration:8,repeat:-1,yoyo:!0,ease:"sine.inOut"}),n["a"].to(".element-2",{y:-30,rotation:-360,duration:12,repeat:-1,yoyo:!0,ease:"sine.inOut",delay:2}),n["a"].to(".element-3",{y:-15,rotation:180,duration:6,repeat:-1,yoyo:!0,ease:"sine.inOut",delay:4}),n["a"].to(".element-4",{y:-25,rotation:-180,duration:10,repeat:-1,yoyo:!0,ease:"sine.inOut",delay:1})},initGridAnimation:function(){n["a"].to(".grid-background",{backgroundPosition:"60px 60px",duration:20,repeat:-1,ease:"none"})},initErrorSymbolAnimation:function(){n["a"].to(".error-symbol",{scale:1.2,duration:1.5,repeat:-1,yoyo:!0,ease:"power2.inOut"}),n["a"].to(".particle",{y:-60,opacity:0,duration:4,repeat:-1,stagger:1,ease:"power2.out"}),n["a"].to(".server-glow",{scale:1.1,opacity:.8,duration:2,repeat:-1,yoyo:!0,ease:"sine.inOut"}),n["a"].to(".scan-line",{x:"100vw",duration:4,repeat:-1,stagger:1.5,ease:"none"}),n["a"].to(".stream",{y:"100vh",duration:3,repeat:-1,stagger:1,ease:"none"}),n["a"].to(".element-core",{scale:1.5,opacity:.8,duration:2,repeat:-1,yoyo:!0,stagger:.5,ease:"sine.inOut"})},retryConnection:function(){var t=u(o.a.mark((function t(){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.retrying=!0,n["a"].to(".btn-primary",{scale:.95,duration:.1,yoyo:!0,repeat:1}),t.prev=2,t.next=5,new Promise((function(t){return setTimeout(t,1e3)}));case 5:this.$router.push("/home"),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](2),this.$message.error("跳转失败，请稍后再试");case 11:return t.prev=11,this.retrying=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,this,[[2,8,11,14]])})));function e(){return t.apply(this,arguments)}return e}(),goHome:function(){var t=this;n["a"].to(".btn-secondary",{scale:.95,duration:.1,yoyo:!0,repeat:1,onComplete:function(){t.$router.push("/home")}})},contactSupport:function(){var t=this;n["a"].to(".btn-secondary",{scale:.95,duration:.1,yoyo:!0,repeat:1,onComplete:function(){t.$message.info("客服系统暂未接入，请稍后再试或通过其他方式联系我们")}})}}},m=p,f=(a("a1e6"),a("2877")),v=Object(f["a"])(m,s,r,!1,null,"0acba9ee",null);e["default"]=v.exports},de15:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("a-card",{attrs:{bordered:!1}},[s("div",{staticClass:"table-page-search-wrapper"},[s("a-form",{attrs:{layout:"inline"}},[s("a-row",{attrs:{gutter:48}},[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"角色ID"}},[s("a-input",{attrs:{placeholder:"请输入"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("span",{staticClass:"table-page-search-submitButtons"},[s("a-button",{attrs:{type:"primary"}},[e._v("查询")]),s("a-button",{staticStyle:{"margin-left":"8px"}},[e._v("重置")])],1)])],1)],1)],1),s("s-table",{attrs:{columns:e.columns,data:e.loadData},scopedSlots:e._u([{key:"actions",fn:function(t,a){return s("span",{},e._l(a.actionList,(function(t,a){return s("a-tag",{key:a},[e._v(e._s(t.describe))])})),1)}},{key:"status",fn:function(t){return s("span",{},[e._v("\n      "+e._s(e._f("statusFilter")(t))+"\n    ")])}},{key:"action",fn:function(t,a){return s("span",{},[s("a",{on:{click:function(t){return e.handleEdit(a)}}},[e._v("编辑")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-dropdown",[s("a",{staticClass:"ant-dropdown-link"},[e._v("\n          更多 "),s("a-icon",{attrs:{type:"down"}})],1),s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("详情")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("禁用")])]),s("a-menu-item",[s("a",{attrs:{href:"javascript:;"}},[e._v("删除")])])],1)],1)],1)}}])}),s("a-modal",{attrs:{title:"操作",width:800},on:{ok:e.handleOk},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[s("a-form",{attrs:{autoFormCreate:function(e){t.form=e}}},[s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"唯一识别码",hasFeedback:"",validateStatus:"success"}},[s("a-input",{attrs:{placeholder:"唯一识别码",id:"no",disabled:"disabled"},model:{value:e.mdl.id,callback:function(t){e.$set(e.mdl,"id",t)},expression:"mdl.id"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"权限名称",hasFeedback:"",validateStatus:"success"}},[s("a-input",{attrs:{placeholder:"起一个名字",id:"permission_name"},model:{value:e.mdl.name,callback:function(t){e.$set(e.mdl,"name",t)},expression:"mdl.name"}})],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"状态",hasFeedback:"",validateStatus:"warning"}},[s("a-select",{model:{value:e.mdl.status,callback:function(t){e.$set(e.mdl,"status",t)},expression:"mdl.status"}},[s("a-select-option",{attrs:{value:"1"}},[e._v("正常")]),s("a-select-option",{attrs:{value:"2"}},[e._v("禁用")])],1)],1),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"描述",hasFeedback:""}},[s("a-textarea",{attrs:{rows:5,placeholder:"...",id:"describe"},model:{value:e.mdl.describe,callback:function(t){e.$set(e.mdl,"describe",t)},expression:"mdl.describe"}})],1),s("a-divider"),s("a-form-item",{attrs:{labelCol:e.labelCol,wrapperCol:e.wrapperCol,label:"赋予权限",hasFeedback:""}},[s("a-select",{staticStyle:{width:"100%"},attrs:{mode:"multiple",allowClear:!0},model:{value:e.mdl.actions,callback:function(t){e.$set(e.mdl,"actions",t)},expression:"mdl.actions"}},e._l(e.permissionList,(function(t,a){return s("a-select-option",{key:a,attrs:{value:t.value}},[e._v(e._s(t.label))])})),1)],1)],1)],1)],1)},r=[],i=a("e8c4"),o={name:"TableList",components:{STable:i["a"]},data:function(){var t=this;return{description:"列表使用场景：后台管理中的权限管理以及角色管理，可用于基于 RBAC 设计的角色权限控制，颗粒度细到每一个操作类型。",visible:!1,labelCol:{xs:{span:24},sm:{span:5}},wrapperCol:{xs:{span:24},sm:{span:16}},form:null,mdl:{},advanced:!1,queryParam:{},columns:[{title:"唯一识别码",dataIndex:"id"},{title:"权限名称",dataIndex:"name"},{title:"可操作权限",dataIndex:"actions",scopedSlots:{customRender:"actions"}},{title:"状态",dataIndex:"status",scopedSlots:{customRender:"status"}},{title:"操作",width:"150px",dataIndex:"action",scopedSlots:{customRender:"action"}}],permissionList:null,loadData:function(e){return t.$http.get("/mock/api/permission",{params:Object.assign(e,t.queryParam)}).then((function(t){var e=t.result;return e.data.map((function(t){return t.actionList=JSON.parse(t.actionData),t})),e}))},selectedRowKeys:[],selectedRows:[]}},filters:{statusFilter:function(t){var e={1:"正常",2:"禁用"};return e[t]}},created:function(){this.loadPermissionList()},methods:{loadPermissionList:function(){var t=this;new Promise((function(t){var e=[{label:"新增",value:"add",defaultChecked:!1},{label:"查询",value:"get",defaultChecked:!1},{label:"修改",value:"update",defaultChecked:!1},{label:"列表",value:"query",defaultChecked:!1},{label:"删除",value:"delete",defaultChecked:!1},{label:"导入",value:"import",defaultChecked:!1},{label:"导出",value:"export",defaultChecked:!1}];setTimeout(t(e),1500)})).then((function(e){t.permissionList=e}))},handleEdit:function(t){this.mdl=Object.assign({},t),this.visible=!0},handleOk:function(){},onChange:function(t,e){this.selectedRowKeys=t,this.selectedRows=e},toggleAdvanced:function(){this.advanced=!this.advanced}},watch:{}},n=o,l=a("2877"),c=Object(l["a"])(n,s,r,!1,null,null,null);e["default"]=c.exports},e043:function(t,e,a){},e409:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("exception-page",{attrs:{type:"403"}})},r=[],i=a("0673"),o={components:{ExceptionPage:i["default"]}},n=o,l=a("2877"),c=Object(l["a"])(n,s,r,!1,null,"e1fa96e6",null);e["default"]=c.exports},e5b2:function(t,e,a){},e85c:function(t,e,a){"use strict";var s=a("923e"),r=a.n(s);r.a},f4a1:function(t,e,a){},f4b5:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=this,a=e.$createElement,s=e._self._c||a;return s("a-card",{attrs:{bordered:!1}},[s("div",{staticClass:"table-page-search-wrapper"},[s("a-form",{attrs:{layout:"inline"}},[s("a-row",{attrs:{gutter:48}},[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"规则编号"}},[s("a-input",{attrs:{placeholder:""}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),e.advanced?[s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"调用次数"}},[s("a-input-number",{staticStyle:{width:"100%"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"更新日期"}},[s("a-date-picker",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入更新日期"}})],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1),s("a-col",{attrs:{md:8,sm:24}},[s("a-form-item",{attrs:{label:"使用状态"}},[s("a-select",{attrs:{placeholder:"请选择","default-value":"0"}},[s("a-select-option",{attrs:{value:"0"}},[e._v("全部")]),s("a-select-option",{attrs:{value:"1"}},[e._v("关闭")]),s("a-select-option",{attrs:{value:"2"}},[e._v("运行中")])],1)],1)],1)]:e._e(),s("a-col",{attrs:{md:e.advanced?24:8,sm:24}},[s("span",{staticClass:"table-page-search-submitButtons",style:e.advanced&&{float:"right",overflow:"hidden"}||{}},[s("a-button",{attrs:{type:"primary"}},[e._v("查询")]),s("a-button",{staticStyle:{"margin-left":"8px"}},[e._v("重置")]),s("a",{staticStyle:{"margin-left":"8px"},on:{click:e.toggleAdvanced}},[e._v("\n              "+e._s(e.advanced?"收起":"展开")+"\n              "),s("a-icon",{attrs:{type:e.advanced?"up":"down"}})],1)],1)])],2)],1)],1),s("div",{staticClass:"table-operator"},[s("a-button",{attrs:{type:"primary",icon:"plus"},on:{click:function(){return t.handleModalVisible(!0)}}},[e._v("新建")]),e.selectedRowKeys.length>0?s("a-dropdown",[s("a-menu",{attrs:{slot:"overlay"},slot:"overlay"},[s("a-menu-item",{key:"1"},[s("a-icon",{attrs:{type:"delete"}}),e._v("删除")],1),s("a-menu-item",{key:"2"},[s("a-icon",{attrs:{type:"lock"}}),e._v("锁定")],1)],1),s("a-button",{staticStyle:{"margin-left":"8px"}},[e._v("\n        批量操作 "),s("a-icon",{attrs:{type:"down"}})],1)],1):e._e()],1),s("s-table",{ref:"table",attrs:{size:"default",columns:e.columns,data:e.loadData,showAlertInfo:!0},on:{onSelect:e.onChange},scopedSlots:e._u([e._l(e.columns,(function(t,a){return{key:t.dataIndex,fn:function(a,r,i){return t.scopedSlots?[s("div",{key:i},[r.editable?s("a-input",{staticStyle:{margin:"-5px 0"},attrs:{value:a},on:{change:function(a){return e.handleChange(a.target.value,r.key,t)}}}):[e._v(e._s(a))]],2)]:void 0}}})),{key:"action",fn:function(t,a,r){return[s("div",{staticClass:"editable-row-operations"},[a.editable?s("span",[s("a",{on:{click:function(){return e.save(a)}}},[e._v("保存")]),s("a-divider",{attrs:{type:"vertical"}}),s("a-popconfirm",{attrs:{title:"真的放弃编辑吗?"},on:{confirm:function(){return e.cancel(a)}}},[s("a",[e._v("取消")])])],1):s("span",[s("a",{staticClass:"edit",on:{click:function(){return e.edit(a)}}},[e._v("修改")]),s("a-divider",{attrs:{type:"vertical"}}),s("a",{staticClass:"delete",on:{click:function(){return e.del(a)}}},[e._v("删除")])],1)])]}}],null,!0)}),s("a-modal",{attrs:{title:"新建规则",destroyOnClose:"",visible:e.visibleCreateModal},on:{ok:e.handleCreateModalOk,cancel:e.handleCreateModalCancel}},[s("a-form",{staticStyle:{"margin-top":"8px"},attrs:{autoFormCreate:function(e){t.createForm=e}}},[s("a-form-item",{attrs:{labelCol:{span:5},wrapperCol:{span:15},label:"描述",fieldDecoratorId:"description",fieldDecoratorOptions:{rules:[{required:!0,message:"请输入至少五个字符的规则描述！",min:5}]}}},[s("a-input",{attrs:{placeholder:"请输入"}})],1)],1)],1)],1)},r=[],i=a("e8c4"),o={name:"TableList",components:{STable:i["a"]},data:function(){var t=this;return{advanced:!1,queryParam:{},columns:[{title:"规则编号",dataIndex:"no",width:90},{title:"描述",dataIndex:"description",scopedSlots:{customRender:"description"}},{title:"服务调用次数",dataIndex:"callNo",width:"150px",sorter:!0,needTotal:!0,scopedSlots:{customRender:"callNo"}},{title:"状态",dataIndex:"status",width:"100px",needTotal:!0,scopedSlots:{customRender:"status"}},{title:"更新时间",dataIndex:"updatedAt",width:"150px",sorter:!0,scopedSlots:{customRender:"updatedAt"}},{table:"操作",dataIndex:"action",width:"120px",scopedSlots:{customRender:"action"}}],loadData:function(e){return t.$http.get("/mock/api/service",{params:Object.assign(e,t.queryParam)}).then((function(t){return t.result}))},selectedRowKeys:[],selectedRows:[],visibleCreateModal:!1}},methods:{handleChange:function(t,e,a){},edit:function(t){t.editable=!0,this.$refs.table.updateEdit()},del:function(t){this.$confirm({title:"警告",content:"真的要删除吗?",okText:"删除",okType:"danger",cancelText:"取消",onOk:function(){return new Promise((function(t,e){setTimeout(Math.random()>.5?t:e,1e3)})).catch((function(){}))},onCancel:function(){}})},save:function(t){delete t.editable,this.$refs.table.updateEdit()},cancel:function(t){delete t.editable,this.$refs.table.updateEdit()},onChange:function(t){this.selectedRowKeys=t.selectedRowKeys,this.selectedRows=t.selectedRows},toggleAdvanced:function(){this.advanced=!this.advanced},handleModalVisible:function(t){this.visibleCreateModal=t},handleCreateModalCancel:function(){this.visibleCreateModal=!1},handleCreateModalOk:function(){this.visibleCreateModal=!1}},watch:{}},n=o,l=(a("24f5"),a("2877")),c=Object(l["a"])(n,s,r,!1,null,"60e2b552",null);e["default"]=c.exports},fb08c:function(t,e,a){"use strict";var s=a("0d68"),r=a.n(s);r.a},fd0e:function(t,e,a){}}]);