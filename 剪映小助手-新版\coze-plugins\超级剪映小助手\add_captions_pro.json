{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加字幕", "description": "批量添加字幕", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.cn"}], "paths": {"/jeecg-boot/api/jianyingpro/add_captions": {"post": {"summary": "批量添加字幕", "description": "批量添加字幕", "operationId": "add_captions_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "texts": {"type": "array", "description": "文本列表（必填）", "items": {"type": "string"}, "example": ["这是第一段字幕", "这是第二段字幕"]}, "timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从timelines_pro节点的输出获取（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}}}, "example": [{"start": 0, "end": 3000000}, {"start": 3000000, "end": 6000000}]}, "font": {"type": "string", "description": "字体名称（可选，例如：青禾体）", "example": "微软雅黑"}, "font_size": {"type": "integer", "description": "文字大小", "example": 30}, "keyword_color": {"type": "string", "description": "关键词颜色", "example": "#ff0000"}, "keyword_font_size": {"type": "integer", "description": "关键词字大小", "example": 35}, "keywords": {"type": "array", "description": "文本里面的重点词列表（可选）", "items": {"type": "string"}, "example": ["重点", "关键词"]}, "in_animation": {"type": "string", "description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "example": "飞入|放大"}, "out_animation": {"type": "string", "description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "example": "消散|闭幕"}, "loop_animation": {"type": "string", "description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "example": "扫光|晃动"}, "in_animation_duration": {"type": "integer", "description": "入场动画时长", "example": 1000000}, "out_animation_duration": {"type": "integer", "description": "出场动画时长", "example": 1000000}, "loop_animation_duration": {"type": "integer", "description": "循环动画时长", "example": 2000000}, "text_color": {"type": "string", "description": "文字颜色（可选）", "example": "#ff1837"}, "border_color": {"type": "string", "description": "边框颜色（可选）", "example": "#fe8a80"}, "line_spacing": {"type": "number", "description": "行间距（可选，默认0）", "example": 0.0}, "letter_spacing": {"type": "number", "description": "字符间距（可选）", "example": 0.0}, "alignment": {"type": "integer", "description": "字幕对齐方式（可选，0左对齐，1居中对齐，2右对齐，3竖排居顶，4竖排居中，5竖排居底）", "enum": [0, 1, 2, 3, 4, 5], "example": 1}, "style_text": {"type": "integer", "description": "文本样式（可选，0默认，1富文本样式）", "enum": [0, 1], "example": 0}, "alpha": {"type": "number", "description": "透明度（可选，0.0-1.0）", "example": 1.0, "minimum": 0.0, "maximum": 1.0}, "scale_x": {"type": "string", "description": "水平缩放（可选），多个值请用英文|分割进行循环分配，比如：1.0|1.5|0.8", "example": "1.0|1.5|0.8"}, "scale_y": {"type": "string", "description": "垂直缩放（可选），多个值请用英文|分割进行循环分配，比如：1.0|1.5|0.8", "example": "1.0|1.5|0.8"}, "transform_x": {"type": "string", "description": "水平位置（可选），多个值请用英文|分割进行循环分配，比如：0|100|-100", "example": "0|100|-100"}, "transform_y": {"type": "string", "description": "垂直位置（可选），多个值请用英文|分割进行循环分配，比如：0|-50|50", "example": "0|-50|50"}}, "required": ["access_key", "draft_url", "texts", "timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加字幕", "content": {"application/json": {"schema": {"type": "object", "properties": {"text_ids": {"type": "array", "description": "字幕文本ID列表", "items": {"type": "string"}}, "track_id": {"type": "string", "description": "字幕轨道ID"}, "draft_url": {"type": "string", "description": "更新后的草稿地址"}, "segment_ids": {"type": "array", "description": "字幕段ID列表", "items": {"type": "string"}}, "segment_infos": {"type": "array", "description": "片段详细信息，包含时间和字幕属性信息", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "片段ID"}, "start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}, "duration": {"type": "integer", "description": "持续时间（微秒）"}, "font_size": {"type": "integer", "description": "字体大小"}, "text_color": {"type": "string", "description": "文字颜色"}, "alpha": {"type": "number", "description": "透明度（0.0-1.0）"}, "scale_x": {"type": "number", "description": "水平缩放比例"}, "scale_y": {"type": "number", "description": "垂直缩放比例"}, "text_content": {"type": "string", "description": "字幕文本内容"}}}, "example": [{"segment_id": "segment1", "start": 0, "end": 3000000, "duration": 3000000, "font_size": 24, "text_color": "#ffffff", "alpha": 1.0, "scale_x": 1.0, "scale_y": 1.0, "text_content": "这是第一段字幕"}, {"segment_id": "segment2", "start": 3000000, "end": 6000000, "duration": 3000000, "font_size": 24, "text_color": "#ffffff", "alpha": 1.0, "scale_x": 1.0, "scale_y": 1.0, "text_content": "这是第二段字幕"}]}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.cn/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["text_ids", "track_id", "draft_url", "segment_ids", "segment_infos", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加字幕失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}