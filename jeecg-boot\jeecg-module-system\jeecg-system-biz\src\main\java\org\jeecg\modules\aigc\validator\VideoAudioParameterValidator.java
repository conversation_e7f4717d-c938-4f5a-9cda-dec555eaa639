package org.jeecg.modules.aigc.validator;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 视频添加音频参数验证器
 * 
 * 功能说明：
 * 1. 验证所有输入参数的合法性和完整性
 * 2. 标准化参数格式和默认值设置
 * 3. 确保与Coze插件JSON配置完全一致
 * 4. 提供详细的错误信息和日志记录
 * 
 * 支持的参数：
 * - apiKey: 用户API密钥（必填）
 * - video_url: 视频URL地址（必填）
 * - prompt: 音频生成提示词（可选，默认空字符串）
 * - seed: 随机种子（可选，默认-1）
 * - num_inference_steps: 推理步数（可选，默认24）
 * - cfg_scale: CFG缩放系数（可选，默认5.0）
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
public class VideoAudioParameterValidator {

    // 参数常量定义
    private static final String PARAM_API_KEY = "apiKey";
    private static final String PARAM_VIDEO_URL = "video_url";
    private static final String PARAM_PROMPT = "prompt";
    private static final String PARAM_SEED = "seed";
    private static final String PARAM_NUM_INFERENCE_STEPS = "num_inference_steps";
    private static final String PARAM_CFG_SCALE = "cfg_scale";

    // 默认值常量
    private static final String DEFAULT_PROMPT = "";
    private static final int DEFAULT_SEED = -1;
    private static final int DEFAULT_NUM_INFERENCE_STEPS = 24;
    private static final double DEFAULT_CFG_SCALE = 5.0;

    // 参数范围常量（移除提示词长度限制）
    private static final int MIN_SEED = -1;
    private static final int MAX_SEED = 2147483647;
    private static final int MIN_INFERENCE_STEPS = 1;
    private static final int MAX_INFERENCE_STEPS = 50;
    private static final double MIN_CFG_SCALE = 1.0;
    private static final double MAX_CFG_SCALE = 20.0;

    /**
     * 验证所有参数的合法性和完整性
     * 
     * @param params 参数Map，包含所有输入参数
     * @throws IllegalArgumentException 当参数验证失败时抛出异常
     */
    public static void validateAllParameters(Map<String, Object> params) {
        try {
            log.info("🔥 开始验证视频添加音频参数: {}", params);

            // 验证参数Map不为空
            if (params == null || params.isEmpty()) {
                throw new IllegalArgumentException("参数不能为空");
            }

            // 验证必填参数：API密钥
            validateApiKey(params);

            // 验证必填参数：视频URL
            validateVideoUrl(params);

            // 验证可选参数：提示词
            validatePrompt(params);

            // 验证可选参数：随机种子
            validateSeed(params);

            // 验证可选参数：推理步数
            validateNumInferenceSteps(params);

            // 验证可选参数：CFG缩放系数
            validateCfgScale(params);

            log.info("✅ 视频添加音频参数验证通过");

        } catch (Exception e) {
            log.error("❌ 视频添加音频参数验证失败: {}", e.getMessage(), e);
            throw new IllegalArgumentException("参数验证失败: " + e.getMessage());
        }
    }

    /**
     * 标准化所有参数格式和默认值
     * 
     * @param params 参数Map，会直接修改传入的Map
     */
    public static void normalizeParameters(Map<String, Object> params) {
        try {
            log.info("🔥 开始标准化视频添加音频参数: {}", params);

            // 标准化提示词
            normalizePrompt(params);

            // 标准化随机种子
            normalizeSeed(params);

            // 标准化推理步数
            normalizeNumInferenceSteps(params);

            // 标准化CFG缩放系数
            normalizeCfgScale(params);

            log.info("✅ 视频添加音频参数标准化完成: {}", params);

        } catch (Exception e) {
            log.error("❌ 视频添加音频参数标准化失败: {}", e.getMessage(), e);
            throw new IllegalArgumentException("参数标准化失败: " + e.getMessage());
        }
    }

    /**
     * 验证API密钥参数
     */
    private static void validateApiKey(Map<String, Object> params) {
        String apiKey = (String) params.get(PARAM_API_KEY);
        if (StringUtils.isBlank(apiKey)) {
            throw new IllegalArgumentException("API密钥不能为空");
        }
        if (apiKey.trim().length() < 10) {
            throw new IllegalArgumentException("API密钥格式不正确，长度过短");
        }
        log.debug("✅ API密钥验证通过");
    }

    /**
     * 验证视频URL参数
     */
    private static void validateVideoUrl(Map<String, Object> params) {
        String videoUrl = (String) params.get(PARAM_VIDEO_URL);
        if (StringUtils.isBlank(videoUrl)) {
            throw new IllegalArgumentException("视频URL不能为空");
        }
        if (!isValidVideoURL(videoUrl.trim())) {
            throw new IllegalArgumentException("视频URL格式不正确，必须是有效的HTTP/HTTPS地址");
        }
        log.debug("✅ 视频URL验证通过: {}", videoUrl);
    }

    /**
     * 验证提示词参数（只检查存在性，长度限制由外部接口校验）
     */
    private static void validatePrompt(Map<String, Object> params) {
        if (!params.containsKey(PARAM_PROMPT)) {
            return; // 可选参数，不存在时跳过验证
        }

        // 移除长度限制，由外部接口处理
        log.debug("✅ 提示词验证通过");
    }

    /**
     * 验证随机种子参数
     */
    private static void validateSeed(Map<String, Object> params) {
        if (!params.containsKey(PARAM_SEED)) {
            return; // 可选参数，不存在时跳过验证
        }

        Object seedObj = params.get(PARAM_SEED);
        if (seedObj != null) {
            Integer seed = parseIntegerParameter(seedObj, PARAM_SEED);
            if (seed < MIN_SEED || seed > MAX_SEED) {
                throw new IllegalArgumentException(
                    String.format("随机种子取值范围为[%d, %d]，当前值：%d", MIN_SEED, MAX_SEED, seed)
                );
            }
        }
        log.debug("✅ 随机种子验证通过");
    }

    /**
     * 验证推理步数参数
     */
    private static void validateNumInferenceSteps(Map<String, Object> params) {
        if (!params.containsKey(PARAM_NUM_INFERENCE_STEPS)) {
            return; // 可选参数，不存在时跳过验证
        }

        Object stepsObj = params.get(PARAM_NUM_INFERENCE_STEPS);
        if (stepsObj != null) {
            Integer steps = parseIntegerParameter(stepsObj, PARAM_NUM_INFERENCE_STEPS);
            if (steps < MIN_INFERENCE_STEPS || steps > MAX_INFERENCE_STEPS) {
                throw new IllegalArgumentException(
                    String.format("推理步数取值范围为[%d, %d]，当前值：%d", 
                        MIN_INFERENCE_STEPS, MAX_INFERENCE_STEPS, steps)
                );
            }
        }
        log.debug("✅ 推理步数验证通过");
    }

    /**
     * 验证CFG缩放系数参数
     */
    private static void validateCfgScale(Map<String, Object> params) {
        if (!params.containsKey(PARAM_CFG_SCALE)) {
            return; // 可选参数，不存在时跳过验证
        }

        Object cfgObj = params.get(PARAM_CFG_SCALE);
        if (cfgObj != null) {
            Double cfg = parseDoubleParameter(cfgObj, PARAM_CFG_SCALE);
            if (cfg < MIN_CFG_SCALE || cfg > MAX_CFG_SCALE) {
                throw new IllegalArgumentException(
                    String.format("CFG缩放系数取值范围为[%.1f, %.1f]，当前值：%.2f", 
                        MIN_CFG_SCALE, MAX_CFG_SCALE, cfg)
                );
            }
        }
        log.debug("✅ CFG缩放系数验证通过");
    }

    /**
     * 标准化提示词参数
     */
    private static void normalizePrompt(Map<String, Object> params) {
        String prompt = (String) params.get(PARAM_PROMPT);
        if (prompt == null || StringUtils.isBlank(prompt)) {
            params.put(PARAM_PROMPT, DEFAULT_PROMPT);
            log.debug("🔧 提示词设置为默认值: '{}'", DEFAULT_PROMPT);
        } else {
            params.put(PARAM_PROMPT, prompt.trim());
            log.debug("🔧 提示词已标准化: '{}'", prompt.trim());
        }
    }

    /**
     * 标准化随机种子参数
     */
    private static void normalizeSeed(Map<String, Object> params) {
        if (!params.containsKey(PARAM_SEED) || params.get(PARAM_SEED) == null) {
            params.put(PARAM_SEED, DEFAULT_SEED);
            log.debug("🔧 随机种子设置为默认值: {}", DEFAULT_SEED);
        } else {
            Object seedObj = params.get(PARAM_SEED);
            Integer seed = parseIntegerParameter(seedObj, PARAM_SEED);
            params.put(PARAM_SEED, seed);
            log.debug("🔧 随机种子已标准化: {}", seed);
        }
    }

    /**
     * 标准化推理步数参数
     */
    private static void normalizeNumInferenceSteps(Map<String, Object> params) {
        if (!params.containsKey(PARAM_NUM_INFERENCE_STEPS) || params.get(PARAM_NUM_INFERENCE_STEPS) == null) {
            params.put(PARAM_NUM_INFERENCE_STEPS, DEFAULT_NUM_INFERENCE_STEPS);
            log.debug("🔧 推理步数设置为默认值: {}", DEFAULT_NUM_INFERENCE_STEPS);
        } else {
            Object stepsObj = params.get(PARAM_NUM_INFERENCE_STEPS);
            Integer steps = parseIntegerParameter(stepsObj, PARAM_NUM_INFERENCE_STEPS);
            params.put(PARAM_NUM_INFERENCE_STEPS, steps);
            log.debug("🔧 推理步数已标准化: {}", steps);
        }
    }

    /**
     * 标准化CFG缩放系数参数
     */
    private static void normalizeCfgScale(Map<String, Object> params) {
        if (!params.containsKey(PARAM_CFG_SCALE) || params.get(PARAM_CFG_SCALE) == null) {
            params.put(PARAM_CFG_SCALE, DEFAULT_CFG_SCALE);
            log.debug("🔧 CFG缩放系数设置为默认值: {}", DEFAULT_CFG_SCALE);
        } else {
            Object cfgObj = params.get(PARAM_CFG_SCALE);
            Double cfg = parseDoubleParameter(cfgObj, PARAM_CFG_SCALE);
            params.put(PARAM_CFG_SCALE, cfg);
            log.debug("🔧 CFG缩放系数已标准化: {}", cfg);
        }
    }

    /**
     * 解析整数参数，支持字符串和数字类型
     */
    private static Integer parseIntegerParameter(Object obj, String paramName) {
        if (obj instanceof Integer) {
            return (Integer) obj;
        } else if (obj instanceof String) {
            try {
                return Integer.parseInt((String) obj);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException(paramName + "必须是整数");
            }
        } else if (obj instanceof Number) {
            return ((Number) obj).intValue();
        } else {
            throw new IllegalArgumentException(paramName + "类型不正确，必须是整数");
        }
    }

    /**
     * 解析浮点数参数，支持字符串和数字类型
     */
    private static Double parseDoubleParameter(Object obj, String paramName) {
        if (obj instanceof Double) {
            return (Double) obj;
        } else if (obj instanceof String) {
            try {
                return Double.parseDouble((String) obj);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException(paramName + "必须是数字");
            }
        } else if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        } else {
            throw new IllegalArgumentException(paramName + "类型不正确，必须是数字");
        }
    }

    /**
     * 验证视频URL格式（不依赖文件后缀）
     * 
     * @param url 视频URL
     * @return 是否为有效的视频URL
     */
    private static boolean isValidVideoURL(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        
        // 基础格式检查：支持http/https协议
        String urlPattern = "^https?://.*";
        if (!url.matches(urlPattern)) {
            return false;
        }
        
        // 检查URL长度合理性
        if (url.length() > 2048) {
            return false;
        }
        
        // 检查是否包含基本的域名结构
        if (!url.contains(".")) {
            return false;
        }
        
        return true;
    }
}
