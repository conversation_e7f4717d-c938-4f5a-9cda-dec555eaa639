package org.jeecg.modules.api.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.exception.VideoGenerationException;

import java.util.Map;

/**
 * 视频生成参数验证工具类
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
public class VideoParameterValidator {

    // 支持的模型列表
    private static final String[] SUPPORTED_MODELS = {
        "doubao-seedance-1-0-lite-i2v-250428",
        "doubao-seedance-1-0-pro-250528"
    };

    // 支持的分辨率列表
    private static final String[] SUPPORTED_RESOLUTIONS = {
        "480p", "720p", "1080p"
    };

    // 支持的时长列表
    private static final Integer[] SUPPORTED_DURATIONS = {
        5, 10
    };

    // URL格式校验已移除，由豆包插件自行处理

    /**
     * 验证所有参数
     */
    public static void validateAllParameters(Map<String, Object> params) {
        log.debug("开始验证视频生成参数: {}", params);

        // 验证必填参数
        validateRequiredParameters(params);

        // 验证可选参数
        validateOptionalParameters(params);

        // 验证参数组合规则
        validateParameterCombinations(params);

        log.debug("参数验证通过");
    }

    /**
     * 验证必填参数
     */
    private static void validateRequiredParameters(Map<String, Object> params) {
        // 验证API密钥
        String apiKey = (String) params.get("apiKey");
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw VideoGenerationException.invalidParameter("API密钥不能为空");
        }
        if (!apiKey.startsWith("ak_")) {
            throw VideoGenerationException.invalidParameter("API密钥格式错误，应以'ak_'开头");
        }

        // 验证提示词
        String prompt = (String) params.get("prompt");
        if (prompt == null || prompt.trim().isEmpty()) {
            throw VideoGenerationException.invalidParameter("提示词不能为空");
        }
        if (prompt.length() > 1000) {
            throw VideoGenerationException.invalidParameter("提示词长度不能超过1000个字符");
        }

        // 验证首帧图片URL（只检查非空，格式由豆包插件校验）
        String imageUrl = (String) params.get("image_url");
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw VideoGenerationException.invalidParameter("首帧图片链接不能为空");
        }
        // 移除URL格式校验，直接传给豆包插件处理
    }

    /**
     * 验证可选参数
     */
    private static void validateOptionalParameters(Map<String, Object> params) {
        // 验证模型
        String model = (String) params.get("model");
        if (model != null && !isValidModel(model)) {
            throw VideoGenerationException.modelNotSupported("不支持的模型: " + model);
        }

        // 验证分辨率
        String resolution = (String) params.get("resolution");
        if (resolution != null && !isValidResolution(resolution)) {
            throw VideoGenerationException.resolutionNotSupported("不支持的分辨率: " + resolution);
        }

        // 验证时长
        Object durationObj = params.get("duration");
        if (durationObj != null) {
            Integer duration = null;
            if (durationObj instanceof Integer) {
                duration = (Integer) durationObj;
            } else if (durationObj instanceof String) {
                try {
                    duration = Integer.parseInt((String) durationObj);
                } catch (NumberFormatException e) {
                    throw VideoGenerationException.invalidParameter("时长必须是数字");
                }
            }
            
            if (duration != null && !isValidDuration(duration)) {
                throw VideoGenerationException.durationNotSupported("不支持的时长: " + duration + "秒");
            }
        }

        // 验证尾帧图片URL（如果提供，只检查非空，格式由豆包插件校验）
        String endImageUrl = (String) params.get("end_image_url");
        // 移除URL格式校验，直接传给豆包插件处理
    }

    /**
     * 验证参数组合规则（已通过normalizeParameters智能修正，这里只做最终检查）
     */
    private static void validateParameterCombinations(Map<String, Object> params) {
        String model = (String) params.getOrDefault("model", "doubao-seedance-1-0-lite-i2v-250428");
        String resolution = (String) params.getOrDefault("resolution", "480p");
        String endImageUrl = (String) params.get("end_image_url");

        // 🔥 注意：pro模型的限制已在normalizeParameters中自动修正
        // 这里只做最终的安全检查，理论上不应该再出现冲突

        // 最终检查1：确保pro模型不会有720p（应该已被修正为480p）
        if ("doubao-seedance-1-0-pro-250528".equals(model) && "720p".equals(resolution)) {
            log.warn("⚠️ 参数修正失败：pro模型仍然包含720p分辨率");
            throw VideoGenerationException.invalidParameter("pro模型不支持720p分辨率");
        }

        // 最终检查2：确保pro模型不会有尾帧（应该已被移除）
        if ("doubao-seedance-1-0-pro-250528".equals(model) && endImageUrl != null && !endImageUrl.trim().isEmpty()) {
            log.warn("⚠️ 参数修正失败：pro模型仍然包含尾帧图片");
            throw VideoGenerationException.invalidParameter("pro模型不支持尾帧图片功能");
        }
    }

    /**
     * 验证模型是否支持
     */
    public static boolean isValidModel(String model) {
        if (model == null) return false;
        for (String supportedModel : SUPPORTED_MODELS) {
            if (supportedModel.equals(model)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证分辨率是否支持
     */
    public static boolean isValidResolution(String resolution) {
        if (resolution == null) return false;
        for (String supportedResolution : SUPPORTED_RESOLUTIONS) {
            if (supportedResolution.equals(resolution)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证时长是否支持
     */
    public static boolean isValidDuration(Integer duration) {
        if (duration == null) return false;
        for (Integer supportedDuration : SUPPORTED_DURATIONS) {
            if (supportedDuration.equals(duration)) {
                return true;
            }
        }
        return false;
    }

    // isValidUrl方法已移除，URL格式校验由豆包插件处理

    /**
     * 获取支持的模型列表
     */
    public static String[] getSupportedModels() {
        return SUPPORTED_MODELS.clone();
    }

    /**
     * 获取支持的分辨率列表
     */
    public static String[] getSupportedResolutions() {
        return SUPPORTED_RESOLUTIONS.clone();
    }

    /**
     * 获取支持的时长列表
     */
    public static Integer[] getSupportedDurations() {
        return SUPPORTED_DURATIONS.clone();
    }

    /**
     * 获取模型显示名称
     */
    public static String getModelDisplayName(String model) {
        if ("doubao-seedance-1-0-lite-i2v-250428".equals(model)) {
            return "轻量级模型";
        } else if ("doubao-seedance-1-0-pro-250528".equals(model)) {
            return "专业级模型";
        }
        return model;
    }

    /**
     * 标准化参数（设置默认值 + 智能参数修正）
     */
    public static void normalizeParameters(Map<String, Object> params) {
        // 设置默认模型
        if (!params.containsKey("model") || params.get("model") == null) {
            params.put("model", "doubao-seedance-1-0-lite-i2v-250428");
        }

        // 设置默认分辨率
        if (!params.containsKey("resolution") || params.get("resolution") == null) {
            params.put("resolution", "480p");
        }

        // 设置默认时长
        if (!params.containsKey("duration") || params.get("duration") == null) {
            params.put("duration", 5);
        }

        // 设置默认摄像头固定参数
        if (!params.containsKey("camerafixed")) {
            params.put("camerafixed", false);
        }

        // 设置默认同步参数
        if (!params.containsKey("asyn")) {
            params.put("asyn", false);
        }

        // 🔥 智能参数修正：处理pro模型的限制
        String model = (String) params.get("model");
        if ("doubao-seedance-1-0-pro-250528".equals(model)) {

            // 修正1：pro模型不支持720p，自动替换为480p
            String resolution = (String) params.get("resolution");
            if ("720p".equals(resolution)) {
                params.put("resolution", "480p");
                log.info("🔧 智能修正：pro模型不支持720p，已自动替换为480p");
            }

            // 修正2：pro模型不支持尾帧，自动移除end_image_url
            String endImageUrl = (String) params.get("end_image_url");
            if (endImageUrl != null && !endImageUrl.trim().isEmpty()) {
                params.remove("end_image_url");
                log.info("🔧 智能修正：pro模型不支持尾帧，已自动移除end_image_url参数");
            }
        }

        log.debug("参数标准化和智能修正完成: {}", params);
    }
}
