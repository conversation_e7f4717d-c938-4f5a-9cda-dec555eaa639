(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~cf6231d7"],{"302c":function(e,t,r){"use strict";r.d(t,"a",(function(){return y}));var n=r("cffa"),a=r("1dac");function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){s(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var i,u={breakpoints:{mobile:768,tablet:1024,desktop:1200},getDeviceType:function(){var e=window.innerWidth;return e<this.breakpoints.mobile?"mobile":e<this.breakpoints.tablet?"tablet":e<this.breakpoints.desktop?"desktop":"large"},getAnimationParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.getDeviceType(),r=this.getMultipliers(t);return c(c({},e),{},{duration:(e.duration||1)*r.duration,delay:(e.delay||0)*r.delay,stagger:(e.stagger||0)*r.stagger})},getMultipliers:function(e){var t={mobile:{duration:.8,delay:.7,stagger:.8},tablet:{duration:.9,delay:.85,stagger:.9},desktop:{duration:1,delay:1,stagger:1},large:{duration:1.1,delay:1.1,stagger:1.1}};return t[e]||t.desktop},getDistance:function(e){var t=this.getDeviceType(),r={mobile:.6,tablet:.8,desktop:1,large:1.2};return e*(r[t]||1)},getScale:function(e){var t=this.getDeviceType(),r={mobile:-.05,tablet:-.02,desktop:0,large:.02};return e+(r[t]||0)},isMobile:function(){return"mobile"===this.getDeviceType()},isTouchDevice:function(){return"ontouchstart"in window||navigator.maxTouchPoints>0},supportsHover:function(){return window.matchMedia("(hover: hover)").matches},getPerformanceLevel:function(){var e=this.getDeviceType(),t=navigator.deviceMemory||4,r=navigator.hardwareConcurrency||4;return"mobile"===e&&t<4?"low":"mobile"===e&&t<8||r<4?"medium":"high"},getPerformanceAdjustedParams:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.getPerformanceLevel(),r={low:{duration:.7,complexity:.5,fps:30},medium:{duration:.85,complexity:.75,fps:45},high:{duration:1,complexity:1,fps:60}},n=r[t];return c(c({},e),{},{duration:(e.duration||1)*n.duration})}};window.addEventListener("resize",(function(){clearTimeout(i),i=setTimeout((function(){window.dispatchEvent(new CustomEvent("responsiveConfigUpdate",{detail:{deviceType:u.getDeviceType()}}))}),250)}));function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){f(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e){return g(e)||m(e)||b(e)||d()}function d(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function b(e,t){if(e){if("string"===typeof e)return v(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(e,t):void 0}}function m(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function g(e){if(Array.isArray(e))return v(e)}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}n["a"].registerPlugin(a["a"]);var y={init:function(e){var t=[],r=this.initPageEnterAnimation();r&&t.push(r);var n=this.initSidebarAnimation();n&&t.push(n);var a=this.initContentAnimation();a&&t.push(a);var o=this.initCardHoverEffects();o&&t.push.apply(t,h(o));var c=this.initLoadingAnimations();return c&&t.push(c),t.forEach((function(t){t.scrollTrigger?e.addScrollTrigger(t.scrollTrigger):t.timeline?e.addTimeline(t.timeline):e.add(t)})),t},initPageEnterAnimation:function(){var e=n["a"].timeline();return e.from(".usercenter-sidebar",{duration:.6,x:-100,opacity:0,ease:"power2.out"}).from(".usercenter-content",{duration:.6,x:50,opacity:0,ease:"power2.out"},"-=0.4"),{timeline:e}},initSidebarAnimation:function(){var e=document.querySelectorAll(".sidebar-menu-item");if(!e.length)return null;var t=n["a"].from(e,{duration:.4,y:20,opacity:0,stagger:.08,ease:"power2.out",delay:.3});return e.forEach((function(e){e.addEventListener("mouseenter",(function(){n["a"].to(e,{duration:.3,x:8,backgroundColor:"rgba(124, 138, 237, 0.1)",ease:"power2.out"})})),e.addEventListener("mouseleave",(function(){n["a"].to(e,{duration:.3,x:0,backgroundColor:"transparent",ease:"power2.out"})}))})),t},initContentAnimation:function(){var e=document.querySelectorAll(".content-section");return e.length?n["a"].from(e,{duration:.6,y:30,opacity:0,stagger:.1,ease:"power2.out",delay:.5}):null},initCardHoverEffects:function(){var e=document.querySelectorAll(".stats-card, .info-card, .action-card"),t=[];return e.forEach((function(e){var r=n["a"].timeline({paused:!0});r.to(e,{duration:.3,y:-8,scale:1.02,boxShadow:"0 12px 40px rgba(124, 138, 237, 0.15)",ease:"power2.out"}),e.addEventListener("mouseenter",(function(){return r.play()})),e.addEventListener("mouseleave",(function(){return r.reverse()})),t.push({timeline:r})})),t},initLoadingAnimations:function(){var e=document.querySelectorAll(".skeleton-loading");return e.length?n["a"].to(e,{duration:1.5,opacity:.3,ease:"power2.inOut",repeat:-1,yoyo:!0}):null},switchPage:function(e,t){var r=n["a"].timeline();return r.to(e,{duration:.3,x:-50,opacity:0,ease:"power2.in"}).fromTo(t,{x:50,opacity:0,display:"block"},{duration:.4,x:0,opacity:1,ease:"power2.out"}).set(e,{display:"none"}),r},animateCounter:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=u.getAnimationParams(l({duration:2,ease:"power2.out"},r));return n["a"].to(e,l(l({},a),{},{innerHTML:t,snap:{innerHTML:1},onUpdate:function(){var e=Math.round(this.targets()[0].innerHTML);e>=1e4?this.targets()[0].innerHTML=(e/1e4).toFixed(1)+"万":e>=1e3&&(this.targets()[0].innerHTML=(e/1e3).toFixed(1)+"k")}}))},animateTableRows:function(e){var t=document.querySelectorAll("".concat(e," tbody tr"));return t.length?n["a"].from(t,{duration:.4,y:20,opacity:0,stagger:.05,ease:"power2.out"}):null},animateProgressBar:function(e,t){return n["a"].to(e,{duration:1.5,width:"".concat(t,"%"),ease:"power2.out"})},animateChart:function(e){var t=document.querySelector(e);return t?n["a"].from(t,{duration:.8,scale:.8,opacity:0,ease:"back.out(1.7)"}):null},showNotification:function(e){var t=n["a"].timeline();return n["a"].set(e,{x:300,opacity:0,display:"block"}),t.to(e,{duration:.5,x:0,opacity:1,ease:"back.out(1.7)"}).to(e,{duration:.3,x:300,opacity:0,ease:"power2.in",delay:3}).set(e,{display:"none"}),t},animateModal:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=e.querySelector(".modal-overlay"),a=e.querySelector(".modal-content");if(t){var o=n["a"].timeline();return n["a"].set(e,{display:"flex"}),o.fromTo(r,{opacity:0},{duration:.3,opacity:1,ease:"power2.out"}).fromTo(a,{scale:.8,opacity:0},{duration:.4,scale:1,opacity:1,ease:"back.out(1.7)"},"-=0.2"),o}var c=n["a"].timeline();return c.to(a,{duration:.3,scale:.8,opacity:0,ease:"power2.in"}).to(r,{duration:.2,opacity:0,ease:"power2.in"},"-=0.1").set(e,{display:"none"}),c}}},4099:function(e,t,r){e.exports=r.p+"img/nodata.fa342acc.png"},4124:function(e,t,r){"use strict";var n=r("a34a"),a=r.n(n),o=r("b775"),c=r("4ec3"),s=r("8b05");function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){p(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t,r,n,a,o,c){try{var s=e[o](c),i=s.value}catch(u){return void r(u)}s.done?t(i):Promise.resolve(i).then(n,a)}function f(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function c(e){l(o,n,a,c,s,"next",e)}function s(e){l(o,n,a,c,s,"throw",e)}c(void 0)}))}}var h={getPluginCategories:function(){var e=f(a.a.mark((function e(){var t,r,n,o=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=!(o.length>0&&void 0!==o[0])||o[0],!t){e.next=6;break}if(r=Object(s["d"])(),!r){e.next=6;break}return e.abrupt("return",Promise.resolve({success:!0,result:r}));case 6:return e.prev=6,e.next=9,Object(c["f"])("plugin_category");case 9:return n=e.sent,n.success&&n.result&&Object(s["a"])(n.result),e.abrupt("return",n);case 14:throw e.prev=14,e.t0=e["catch"](6),e.t0;case 18:case"end":return e.stop()}}),e,null,[[6,14]])})));function t(){return e.apply(this,arguments)}return t}(),getPluginList:function(e){return Object(o["b"])({url:"/plubshop/aigcPlubShop/list",method:"get",params:u({status:1},e)})},getPluginDetail:function(e){return Object(o["b"])({url:"/plubshop/aigcPlubShop/getPluginDetail",method:"get",params:{id:e}})},usePlugin:function(e){return Object(o["b"])({url:"/api/aigc/verify-apikey",method:"post",data:e})},getCategoryStats:function(){return Object(o["b"])({url:"/plubshop/aigcPlubShop/categoryStats",method:"get"})}};t["a"]=h},"4a27":function(e,t,r){e.exports=r.p+"img/guaz.57d7177b.png"},"5e65":function(e,t,r){e.exports=r.p+"img/duban.9a5f5bac.png"},"6eb7":function(e,t,r){},"6f1b":function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"a",(function(){return c})),r.d(t,"c",(function(){return s}));var n=r("b775"),a="/api/creator/workflow";function o(e){return Object(n["b"])({url:"".concat(a,"/list/").concat(e),method:"GET"})}function c(e){return Object(n["b"])({url:"".concat(a,"/add"),method:"POST",data:e})}function s(e,t){return Object(n["b"])({url:"".concat(a,"/edit/").concat(e),method:"PUT",data:t})}},7062:function(e,t,r){"use strict";r.d(t,"a",(function(){return b}));var n=r("a34a"),a=r.n(n),o=r("b27c"),c=r("ef4b");function s(e,t,r,n,a,o,c){try{var s=e[o](c),i=s.value}catch(u){return void r(u)}s.done?t(i):Promise.resolve(i).then(n,a)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function c(e){s(o,n,a,c,i,"next",e)}function i(e){s(o,n,a,c,i,"throw",e)}c(void 0)}))}}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),e}var b=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};f(this,e),this.options=p({enableLog:!0,enablePerformanceTest:!0,enableStressTest:!1},t),this.testResults=[],this.performanceMetrics={totalTests:0,successCount:0,failureCount:0,averageResponseTime:0,minResponseTime:1/0,maxResponseTime:0}}return d(e,[{key:"log",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(this.options.enableLog){var n=(new Date).toISOString(),a={timestamp:n,level:e,message:t,data:r};this.testResults.push(a)}}},{key:"testBasicHeartbeat",value:function(){var e=i(a.a.mark((function e(){var t,r,n,s,i,u,p=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=p.length>0&&void 0!==p[0]?p[0]:"default",r=Date.now(),this.log("info","开始测试基础心跳发送 - 页面类型: ".concat(t)),e.prev=3,n={pageType:t,apiKey:Object(c["b"])(t),testMode:!0,testId:"test_".concat(Date.now())},e.next=7,o["a"].send(n);case 7:if(s=e.sent,i=Date.now()-r,this.updatePerformanceMetrics(i,s.success),!s.success){e.next=15;break}return this.log("success","基础心跳测试成功 - 响应时间: ".concat(i,"ms"),s),e.abrupt("return",{success:!0,responseTime:i,response:s});case 15:return this.log("error","基础心跳测试失败 - 响应时间: ".concat(i,"ms"),s),e.abrupt("return",{success:!1,responseTime:i,response:s});case 17:e.next=25;break;case 19:return e.prev=19,e.t0=e["catch"](3),u=Date.now()-r,this.updatePerformanceMetrics(u,!1),this.log("error","基础心跳测试异常 - 响应时间: ".concat(u,"ms"),e.t0),e.abrupt("return",{success:!1,responseTime:u,error:e.t0.message});case 25:case"end":return e.stop()}}),e,this,[[3,19]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"testSmartHeartbeat",value:function(){var e=i(a.a.mark((function e(){var t,r,n,s,i,u,p,l=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=l.length>0&&void 0!==l[0]?l[0]:"default",r=Date.now(),this.log("info","开始测试智能心跳 - 页面类型: ".concat(t)),e.prev=3,n={pageType:t,apiKey:Object(c["b"])(t),testMode:!0,testId:"smart_test_".concat(Date.now())},s={enableCache:!0,enableRetry:!0,retryOptions:{maxRetries:2,retryDelay:500}},e.next=8,o["a"].smart(n,s);case 8:if(i=e.sent,u=Date.now()-r,this.updatePerformanceMetrics(u,i.success),!i.success){e.next=16;break}return this.log("success","智能心跳测试成功 - 响应时间: ".concat(u,"ms"),i),e.abrupt("return",{success:!0,responseTime:u,response:i});case 16:return this.log("error","智能心跳测试失败 - 响应时间: ".concat(u,"ms"),i),e.abrupt("return",{success:!1,responseTime:u,response:i});case 18:e.next=26;break;case 20:return e.prev=20,e.t0=e["catch"](3),p=Date.now()-r,this.updatePerformanceMetrics(p,!1),this.log("error","智能心跳测试异常 - 响应时间: ".concat(p,"ms"),e.t0),e.abrupt("return",{success:!1,responseTime:p,error:e.t0.message});case 26:case"end":return e.stop()}}),e,this,[[3,20]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"testBatchOperations",value:function(){var e=i(a.a.mark((function e(){var t,r,n,c,s;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=Date.now(),this.log("info","开始测试批量操作"),e.prev=2,r=["user1","user2","user3","user4","user5"],e.next=6,o["a"].batchStatus(r,{includeDetails:!0});case 6:if(n=e.sent,c=Date.now()-t,this.updatePerformanceMetrics(c,n.success),!n.success){e.next=14;break}return this.log("success","批量操作测试成功 - 响应时间: ".concat(c,"ms"),n),e.abrupt("return",{success:!0,responseTime:c,response:n});case 14:return this.log("error","批量操作测试失败 - 响应时间: ".concat(c,"ms"),n),e.abrupt("return",{success:!1,responseTime:c,response:n});case 16:e.next=24;break;case 18:return e.prev=18,e.t0=e["catch"](2),s=Date.now()-t,this.updatePerformanceMetrics(s,!1),this.log("error","批量操作测试异常 - 响应时间: ".concat(s,"ms"),e.t0),e.abrupt("return",{success:!1,responseTime:s,error:e.t0.message});case 24:case"end":return e.stop()}}),e,this,[[2,18]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"testStatsRetrieval",value:function(){var e=i(a.a.mark((function e(){var t,r,n,s,i;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=Date.now(),this.log("info","开始测试统计数据获取"),e.prev=2,r=Object(c["b"])("admin"),e.next=6,o["a"].getRealTime(r,{timeRange:"1h",metrics:["online_users","api_calls"]});case 6:if(n=e.sent,s=Date.now()-t,this.updatePerformanceMetrics(s,n.success),!n.success){e.next=14;break}return this.log("success","统计数据测试成功 - 响应时间: ".concat(s,"ms"),n),e.abrupt("return",{success:!0,responseTime:s,response:n});case 14:return this.log("error","统计数据测试失败 - 响应时间: ".concat(s,"ms"),n),e.abrupt("return",{success:!1,responseTime:s,response:n});case 16:e.next=24;break;case 18:return e.prev=18,e.t0=e["catch"](2),i=Date.now()-t,this.updatePerformanceMetrics(i,!1),this.log("error","统计数据测试异常 - 响应时间: ".concat(i,"ms"),e.t0),e.abrupt("return",{success:!1,responseTime:i,error:e.t0.message});case 24:case"end":return e.stop()}}),e,this,[[2,18]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"runFullTestSuite",value:function(){var e=i(a.a.mark((function e(){var t,r,n,o,c;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.log("info","开始运行完整测试套件"),t=Date.now(),r={basicHeartbeat:{},smartHeartbeat:{},batchOperations:{},statsRetrieval:{}},e.prev=3,e.next=6,this.testBasicHeartbeat("home");case 6:return r.basicHeartbeat=e.sent,e.next=9,this.testSmartHeartbeat("market");case 9:return r.smartHeartbeat=e.sent,e.next=12,this.testBatchOperations();case 12:return r.batchOperations=e.sent,e.next=15,this.testStatsRetrieval();case 15:return r.statsRetrieval=e.sent,n=Date.now()-t,o=Object.values(r).filter((function(e){return e.success})).length,c=Object.keys(r).length,this.log("info","测试套件完成 - 总时间: ".concat(n,"ms, 成功: ").concat(o,"/").concat(c)),e.abrupt("return",{success:o===c,totalTime:n,successCount:o,totalCount:c,results:r,performanceMetrics:this.performanceMetrics});case 23:return e.prev=23,e.t0=e["catch"](3),this.log("error","测试套件执行异常",e.t0),e.abrupt("return",{success:!1,error:e.t0.message,results:r,performanceMetrics:this.performanceMetrics});case 27:case"end":return e.stop()}}),e,this,[[3,23]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"updatePerformanceMetrics",value:function(e,t){this.performanceMetrics.totalTests++,t?this.performanceMetrics.successCount++:this.performanceMetrics.failureCount++,this.performanceMetrics.minResponseTime=Math.min(this.performanceMetrics.minResponseTime,e),this.performanceMetrics.maxResponseTime=Math.max(this.performanceMetrics.maxResponseTime,e);var r=this.performanceMetrics.averageResponseTime*(this.performanceMetrics.totalTests-1)+e;this.performanceMetrics.averageResponseTime=r/this.performanceMetrics.totalTests}},{key:"getTestReport",value:function(){return{testResults:this.testResults,performanceMetrics:this.performanceMetrics,summary:{totalTests:this.performanceMetrics.totalTests,successRate:this.performanceMetrics.totalTests>0?(this.performanceMetrics.successCount/this.performanceMetrics.totalTests*100).toFixed(2)+"%":"0%",averageResponseTime:Math.round(this.performanceMetrics.averageResponseTime)+"ms",responseTimeRange:"".concat(Math.round(this.performanceMetrics.minResponseTime),"ms - ").concat(Math.round(this.performanceMetrics.maxResponseTime),"ms")}}}},{key:"cleanup",value:function(){this.testResults=[],this.performanceMetrics={totalTests:0,successCount:0,failureCount:0,averageResponseTime:0,minResponseTime:1/0,maxResponseTime:0},o["a"].clearCache(),this.log("info","测试数据已清理")}}]),e}()},"8cf3":function(e,t){e.exports="data:image/png;base64,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"},b27c:function(e,t,r){"use strict";r.d(t,"a",(function(){return Z}));var n=r("a34a"),a=r.n(n),o=r("0fea");function c(e){return p(e)||u(e)||i(e)||s()}function s(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(e,t){if(e){if("string"===typeof e)return l(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function p(e){if(Array.isArray(e))return l(e)}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function h(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){d(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b(e){return b="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function m(e,t,r,n,a,o,c){try{var s=e[o](c),i=s.value}catch(u){return void r(u)}s.done?t(i):Promise.resolve(i).then(n,a)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function c(e){m(o,n,a,c,s,"next",e)}function s(e){m(o,n,a,c,s,"throw",e)}c(void 0)}))}}function v(e){return y.apply(this,arguments)}function y(){return y=g(a.a.mark((function e(t){var r,n,c=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(c.length>1&&void 0!==c[1]?c[1]:{},e.prev=1,t&&"object"===b(t)){e.next=4;break}throw new Error("心跳数据不能为空且必须为对象类型");case 4:return r=h({timestamp:Date.now(),userAgent:navigator.userAgent,pageUrl:window.location.href},t),e.next=7,Object(o["i"])("/jeecg-boot/api/heartbeat",r);case 7:return n=e.sent,e.abrupt("return",X(n,"发送心跳"));case 11:return e.prev=11,e.t0=e["catch"](1),e.abrupt("return",{success:!1,message:e.t0.message||"心跳发送失败",code:e.t0.code||500,result:null,timestamp:Date.now()});case 15:case"end":return e.stop()}}),e,null,[[1,11]])}))),y.apply(this,arguments)}function w(e){return A.apply(this,arguments)}function A(){return A=g(a.a.mark((function e(t){var r,n,o,c,s,i,u,p,l,f,h,d=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=d.length>1&&void 0!==d[1]?d[1]:{},n=r.maxRetries,o=void 0===n?3:n,c=r.retryDelay,s=void 0===c?1e3:c,i=r.backoffMultiplier,u=void 0===i?2:i,p=null,l=0;case 4:if(!(l<=o)){e.next=25;break}return e.prev=5,e.next=8,v(t);case 8:if(f=e.sent,!f.success){e.next=11;break}return e.abrupt("return",f);case 11:if(!(f.code>=400&&f.code<500)){e.next=13;break}return e.abrupt("return",f);case 13:p=new Error(f.message||"心跳发送失败"),e.next=22;break;case 16:return e.prev=16,e.t0=e["catch"](5),e.delegateYield(a.a.mark((function t(){var r;return a.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(p=e.t0,l!==o){t.next=3;break}return t.abrupt("return","break");case 3:return r=s*Math.pow(u,l),t.next=6,new Promise((function(e){return setTimeout(e,r)}));case 6:case"end":return t.stop()}}),t)}))(),"t1",19);case 19:if(h=e.t1,"break"!==h){e.next=22;break}return e.abrupt("break",25);case 22:l++,e.next=4;break;case 25:return e.abrupt("return",{success:!1,message:"心跳发送失败，已重试".concat(o,"次: ").concat(p.message),code:500,result:null,timestamp:Date.now()});case 26:case"end":return e.stop()}}),e,null,[[5,16]])}))),A.apply(this,arguments)}function O(e){return j.apply(this,arguments)}function j(){return j=g(a.a.mark((function e(t){var r,n,c=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=c.length>1&&void 0!==c[1]?c[1]:{},e.prev=1,t){e.next=4;break}throw new Error("用户ID不能为空");case 4:return e.next=6,Object(o["c"])("/jeecg-boot/api/user/online-status",{userId:t,includeDetails:r.includeDetails||!1,timestamp:Date.now()});case 6:return n=e.sent,e.abrupt("return",X(n,"获取用户在线状态"));case 10:return e.prev=10,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取用户在线状态失败"));case 14:case"end":return e.stop()}}),e,null,[[1,10]])}))),j.apply(this,arguments)}function x(){return D.apply(this,arguments)}function D(){return D=g(a.a.mark((function e(){var t,r,n,c=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},e.prev=1,r={includeHistory:t.includeHistory||!1,timeRange:t.timeRange||"24h",groupBy:t.groupBy||"hour",timestamp:Date.now()},e.next=5,Object(o["c"])("/jeecg-boot/api/online-users/stats",r);case 5:return n=e.sent,e.abrupt("return",X(n,"获取在线用户统计"));case 9:return e.prev=9,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取在线用户统计失败"));case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),D.apply(this,arguments)}function T(e){return k.apply(this,arguments)}function k(){return k=g(a.a.mark((function e(t){var r,n,c,s=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=s.length>1&&void 0!==s[1]?s[1]:{},e.prev=1,t){e.next=4;break}throw new Error("用户ID不能为空");case 4:return n={userId:t,includeScore:r.includeScore||!0,includeHistory:r.includeHistory||!1,timeRange:r.timeRange||"7d",timestamp:Date.now()},e.next=7,Object(o["c"])("/jeecg-boot/api/user/activity-info",n);case 7:return c=e.sent,e.abrupt("return",X(c,"获取用户活跃度信息"));case 11:return e.prev=11,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取用户活跃度信息失败"));case 15:case"end":return e.stop()}}),e,null,[[1,11]])}))),k.apply(this,arguments)}function P(e){return E.apply(this,arguments)}function E(){return E=g(a.a.mark((function e(t){var r,n,c,s=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=s.length>1&&void 0!==s[1]?s[1]:{},e.prev=1,Array.isArray(t)&&0!==t.length){e.next=4;break}throw new Error("用户ID列表不能为空且必须为数组类型");case 4:if(!(t.length>100)){e.next=6;break}throw new Error("批量查询用户数量不能超过100个");case 6:return n={userIds:t,includeDetails:r.includeDetails||!1,timestamp:Date.now()},e.next=9,Object(o["i"])("/jeecg-boot/api/user/batch-online-status",n);case 9:return c=e.sent,e.abrupt("return",X(c,"批量获取用户在线状态"));case 13:return e.prev=13,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"批量获取用户在线状态失败"));case 17:case"end":return e.stop()}}),e,null,[[1,13]])}))),E.apply(this,arguments)}function S(e){return M.apply(this,arguments)}function M(){return M=g(a.a.mark((function e(t){var r,n,c,s=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=s.length>1&&void 0!==s[1]?s[1]:{},e.prev=1,t){e.next=4;break}throw new Error("API密钥不能为空");case 4:return n={apiKey:t,includeHistory:r.includeHistory||!1,timeRange:r.timeRange||"1h",metrics:r.metrics||["online_users","api_calls","active_sessions"],timestamp:Date.now()},e.next=7,Object(o["i"])("/jeecg-boot/api/dashboard-data",n);case 7:return c=e.sent,e.abrupt("return",X(c,"获取实时统计数据"));case 11:return e.prev=11,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取实时统计数据失败"));case 15:case"end":return e.stop()}}),e,null,[[1,11]])}))),M.apply(this,arguments)}function R(e,t){return I.apply(this,arguments)}function I(){return I=g(a.a.mark((function e(t,r){var n,c,s,i=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=i.length>2&&void 0!==i[2]?i[2]:{},e.prev=1,t){e.next=4;break}throw new Error("用户ID不能为空");case 4:if(r){e.next=6;break}throw new Error("API密钥不能为空");case 6:return c={apiKey:r,timeRange:n.timeRange||"30d",includeDetails:n.includeDetails||!0,groupBy:n.groupBy||"day",timestamp:Date.now()},e.next=9,Object(o["i"])("/jeecg-boot/api/admin/usage-stats/".concat(t),c);case 9:return s=e.sent,e.abrupt("return",X(s,"获取用户使用统计"));case 13:return e.prev=13,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取用户使用统计失败"));case 17:case"end":return e.stop()}}),e,null,[[1,13]])}))),I.apply(this,arguments)}function B(e){return C.apply(this,arguments)}function C(){return C=g(a.a.mark((function e(t){var r,n,c,s=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=s.length>1&&void 0!==s[1]?s[1]:{},e.prev=1,n={pageType:t||"default",userId:r.userId,timestamp:Date.now()},e.next=5,Object(o["c"])("/jeecg-boot/api/heartbeat/config",n);case 5:return c=e.sent,e.abrupt("return",X(c,"获取心跳配置"));case 9:return e.prev=9,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取心跳配置失败"));case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),C.apply(this,arguments)}function L(e){return H.apply(this,arguments)}function H(){return H=g(a.a.mark((function e(t){var r;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,t){e.next=3;break}throw new Error("会话ID不能为空");case 3:return e.next=5,Object(o["c"])("/jeecg-boot/api/heartbeat/status",{sessionId:t,timestamp:Date.now()});case 5:return r=e.sent,e.abrupt("return",X(r,"检查心跳状态"));case 9:return e.prev=9,e.t0=e["catch"](0),e.abrupt("return",q(e.t0,"检查心跳状态失败"));case 13:case"end":return e.stop()}}),e,null,[[0,9]])}))),H.apply(this,arguments)}function N(){return U.apply(this,arguments)}function U(){return U=g(a.a.mark((function e(){var t,r,n,c=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},e.prev=1,r={userId:t.userId,startTime:t.startTime,endTime:t.endTime,pageSize:t.pageSize||20,pageNo:t.pageNo||1,timestamp:Date.now()},e.next=5,Object(o["c"])("/jeecg-boot/api/heartbeat/history",r);case 5:return n=e.sent,e.abrupt("return",X(n,"获取心跳历史记录"));case 9:return e.prev=9,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取心跳历史记录失败"));case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),U.apply(this,arguments)}function Q(){return G.apply(this,arguments)}function G(){return G=g(a.a.mark((function e(){var t,r,n,c=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},e.prev=1,r={timeRange:t.timeRange||"1h",metrics:t.metrics||["response_time","success_rate","error_rate"],groupBy:t.groupBy||"minute",timestamp:Date.now()},e.next=5,Object(o["c"])("/jeecg-boot/api/heartbeat/performance",r);case 5:return n=e.sent,e.abrupt("return",X(n,"获取性能监控数据"));case 9:return e.prev=9,e.t0=e["catch"](1),e.abrupt("return",q(e.t0,"获取性能监控数据失败"));case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))),G.apply(this,arguments)}function K(e){return F.apply(this,arguments)}function F(){return F=g(a.a.mark((function e(t){var r,n,o,c,s,i,u,p,l,f,h,d,b,m=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=m.length>1&&void 0!==m[1]?m[1]:{},n=r.enableCache,o=void 0===n||n,c=r.cacheKey,s=void 0===c?"heartbeat_cache":c,i=r.cacheTTL,u=void 0===i?3e4:i,p=r.enableRetry,l=void 0===p||p,f=r.retryOptions,h=void 0===f?{}:f,e.prev=2,!o){e.next=7;break}if(d=W(s),!(d&&Date.now()-d.timestamp<u)){e.next=7;break}return e.abrupt("return",d);case 7:if(!l){e.next=13;break}return e.next=10,w(t,h);case 10:e.t0=e.sent,e.next=16;break;case 13:return e.next=15,v(t);case 15:e.t0=e.sent;case 16:return b=e.t0,o&&b.success&&V(s,b),e.abrupt("return",b);case 21:return e.prev=21,e.t1=e["catch"](2),e.abrupt("return",q(e.t1,"智能心跳发送失败"));case 25:case"end":return e.stop()}}),e,null,[[2,21]])}))),F.apply(this,arguments)}function J(e){return z.apply(this,arguments)}function z(){return z=g(a.a.mark((function e(t){var r,n,o,s,i,u,p,l,f,h=arguments;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=h.length>1&&void 0!==h[1]?h[1]:{},n=r.concurrency,o=void 0===n?5:n,s=r.timeout,void 0===s?3e4:s,e.prev=2,Array.isArray(t)&&0!==t.length){e.next=5;break}throw new Error("请求列表不能为空且必须为数组类型");case 5:i=[],u=0;case 7:if(!(u<t.length)){e.next=17;break}return p=t.slice(u,u+o),l=p.map(function(){var e=g(a.a.mark((function e(t){var r,n;return a.a.wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.prev=0,r=t.method,n=t.params,e.t0=r,e.next="getUserOnlineStatus"===e.t0?5:"getUserActivityInfo"===e.t0?8:"getOnlineUserStats"===e.t0?11:14;break;case 5:return e.next=7,O(n.userId,n.options);case 7:return e.abrupt("return",e.sent);case 8:return e.next=10,T(n.userId,n.options);case 10:return e.abrupt("return",e.sent);case 11:return e.next=13,x(n.options);case 13:return e.abrupt("return",e.sent);case 14:throw new Error("不支持的方法类型: ".concat(r));case 15:e.next=20;break;case 17:return e.prev=17,e.t1=e["catch"](0),e.abrupt("return",q(e.t1,"批量请求失败: ".concat(t.method)));case 20:case"end":return e.stop()}}),e,null,[[0,17]])})));return function(t){return e.apply(this,arguments)}}()),e.next=12,Promise.allSettled(l);case 12:f=e.sent,i.push.apply(i,c(f.map((function(e){return"fulfilled"===e.status?e.value:e.reason}))));case 14:u+=o,e.next=7;break;case 17:return e.abrupt("return",{success:!0,message:"批量数据获取完成",code:200,result:{total:i.length,success:i.filter((function(e){return e.success})).length,failed:i.filter((function(e){return!e.success})).length,data:i},timestamp:Date.now()});case 20:return e.prev=20,e.t0=e["catch"](2),e.abrupt("return",q(e.t0,"批量数据获取失败"));case 24:case"end":return e.stop()}}),e,null,[[2,20]])}))),z.apply(this,arguments)}function X(e,t){try{if(!e)throw new Error("响应数据为空");var r={success:!1!==e.success,message:e.message||"".concat(t,"成功"),code:e.code||200,result:e.result||e.data||e,timestamp:e.timestamp||Date.now()};return!1===e.success&&(r.success=!1,r.message=e.message||"".concat(t,"失败"),r.code=e.code||500),r}catch(n){return q(n,"".concat(t,"响应处理失败"))}}function q(e,t){return{success:!1,message:e.message||t||"操作失败",code:e.code||e.status||500,result:null,timestamp:Date.now(),error:{type:e.name||"Error",stack:void 0}}}function W(e){try{var t=sessionStorage.getItem("heartbeat_".concat(e));return t?JSON.parse(t):null}catch(r){return null}}function V(e,t){try{sessionStorage.setItem("heartbeat_".concat(e),JSON.stringify(t))}catch(r){}}function Y(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{var t=Object.keys(sessionStorage),r=t.filter((function(t){return t.startsWith("heartbeat_")&&(""===e||t.includes(e))}));r.forEach((function(e){return sessionStorage.removeItem(e)}))}catch(n){}}var Z={send:v,sendWithRetry:w,smart:K,getUserStatus:O,getStats:x,getActivity:T,batchStatus:P,batchFetch:J,getConfig:B,checkStatus:L,getHistory:N,getMetrics:Q,getRealTime:S,getUsage:R,clearCache:Y}},bc9a:function(e,t,r){"use strict";r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return c})),r.d(t,"e",(function(){return s})),r.d(t,"b",(function(){return i})),r.d(t,"d",(function(){return u}));var n=r("b775");var a="/api/creator/agent";function o(e){return Object(n["b"])({url:"".concat(a,"/list"),method:"GET",params:e})}function c(e){return Object(n["b"])({url:"".concat(a,"/add"),method:"POST",data:e})}function s(e,t){return Object(n["b"])({url:"".concat(a,"/edit/").concat(e),method:"PUT",data:t})}function i(e){return Object(n["b"])({url:"".concat(a,"/delete/").concat(e),method:"DELETE"})}function u(){return Object(n["b"])({url:"".concat(a,"/revenue/stats"),method:"GET"})}},c612:function(e,t,r){e.exports=r.p+"img/zaiban.6fd68faa.png"},c6cf8:function(e,t,r){e.exports=r.p+"img/pdf4.a7783614.jpg"},cf05:function(e,t,r){e.exports=r.p+"img/logo.dc7a0b69.png"},d5ac:function(e,t){e.exports="data:image/png;base64,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"},db01:function(e,t,r){"use strict";r.d(t,"g",(function(){return a})),r.d(t,"a",(function(){return o})),r.d(t,"i",(function(){return c})),r.d(t,"h",(function(){return s})),r.d(t,"e",(function(){return i})),r.d(t,"f",(function(){return u})),r.d(t,"b",(function(){return p})),r.d(t,"d",(function(){return l})),r.d(t,"c",(function(){return f}));var n=r("b775");function a(){return Object(n["b"])({url:"/sys/sensitiveWord/importFromHoubb",method:"post"})}function o(e){return Object(n["b"])({url:"/sys/sensitiveWord/check",method:"post",params:{text:e}})}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"*";return Object(n["b"])({url:"/sys/sensitiveWord/replace",method:"post",params:{text:e,replacement:t}})}function s(){return Object(n["b"])({url:"/sys/sensitiveWord/refreshCache",method:"post"})}function i(){return Object(n["b"])({url:"/sys/sensitiveWord/statistics",method:"get"})}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return Object(n["b"])({url:"/sys/sensitiveWord/topHitWords",method:"get",params:{limit:e}})}function p(){return Object(n["b"])({url:"/sys/sensitiveWord/categoryStatistics",method:"get"})}function l(){return Object(n["b"])({url:"/sys/sensitiveWord/levelStatistics",method:"get"})}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:7;return Object(n["b"])({url:"/sys/sensitiveWord/hitTrend",method:"get",params:{days:e}})}},e0a5:function(e,t,r){"use strict";r.d(t,"a",(function(){return a})),r.d(t,"e",(function(){return o})),r.d(t,"d",(function(){return c})),r.d(t,"c",(function(){return s})),r.d(t,"b",(function(){return i}));var n=r("b775");function a(e,t){return Object(n["b"])({url:"/api/auth/checkUsername",method:"get",params:{username:e,type:t}})}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"register";return Object(n["b"])({url:"/api/auth/sendSmsCode",method:"post",params:{phone:e,scene:t}})}function c(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"register";return Object(n["b"])({url:"/api/auth/sendEmailCode",method:"post",params:{email:e,scene:t}})}function s(e){return Object(n["b"])({url:"/api/auth/register",method:"post",data:e})}function i(e,t){return Object(n["b"])({url:"/api/auth/wechat/qrcode",method:"get",params:{scene:e,inviteCode:t}})}},ef4b:function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return u})),r.d(t,"b",(function(){return l}));var c={timeout:3e4,retryAttempts:3,retryDelay:1e3,backoffMultiplier:2,enableCache:!0,cacheTTL:3e4,cachePrefix:"heartbeat_",maxBatchSize:100,concurrency:5,enablePerformanceLog:!0,slowRequestThreshold:2e3,enableErrorLog:!0,enableRetry:!0,retryableErrors:["NETWORK_ERROR","TIMEOUT","SERVER_ERROR_500","BAD_GATEWAY_502","SERVICE_UNAVAILABLE_503","GATEWAY_TIMEOUT_504"]},s={home:{interval:2e4,priority:"high",enableCache:!0,cacheTTL:15e3},market:{interval:3e4,priority:"medium",enableCache:!0,cacheTTL:3e4},profile:{interval:3e4,priority:"medium",enableCache:!0,cacheTTL:3e4},admin:{interval:15e3,priority:"highest",enableCache:!1,cacheTTL:0},tutorial:{interval:6e4,priority:"low",enableCache:!0,cacheTTL:6e4},default:{interval:3e4,priority:"medium",enableCache:!0,cacheTTL:3e4}};function i(){var e=!1,t=!0;return{enableDebugLog:e,enableVerboseLog:e,enablePerformanceLog:e,enableCache:t,cacheTTL:e?1e4:3e4,retryAttempts:e?1:3,retryDelay:e?500:1e3,timeout:e?1e4:3e4,enableErrorLog:!0,enableErrorReport:t,enablePerformanceMonitor:t,slowRequestThreshold:e?1e3:2e3}}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=i(),n=s[e]||s.default;return a(a(a(a(a({},c),r),n),t),{},{pageType:e,timestamp:Date.now(),userAgent:navigator.userAgent,pageUrl:window.location.href})}var p={HOME:"home-page-heartbeat-key",MARKET:"market-page-heartbeat-key",PROFILE:"usercenter-page-heartbeat-key",ADMIN:"admin-dashboard-heartbeat-key",TUTORIAL:"tutorial-page-heartbeat-key",DEFAULT:"default-page-heartbeat-key",MONITORING:"monitoring-api-key",ANALYTICS:"analytics-api-key",PERFORMANCE:"performance-api-key"};function l(e){var t={home:p.HOME,market:p.MARKET,profile:p.PROFILE,admin:p.ADMIN,tutorial:p.TUTORIAL,default:p.DEFAULT};return t[e]||p.DEFAULT}},ff1f:function(e,t,r){"use strict";r.d(t,"b",(function(){return s})),r.d(t,"f",(function(){return i})),r.d(t,"g",(function(){return u})),r.d(t,"e",(function(){return p})),r.d(t,"d",(function(){return l})),r.d(t,"a",(function(){return f})),r.d(t,"c",(function(){return h}));var n=r("b775");function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach((function(t){c(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function c(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(n["b"])({url:"/sys/sysAnnouncementSend/getMyAnnouncementSend",method:"get",params:o({pageNo:1,pageSize:50},e)}).then((function(e){if(e.success&&e.result){var t=e.result,r=t.records||[],n=r.map((function(e){return o(o({},e),{},{id:e.anntId,title:e.titile,content:e.msgContent,isRead:"1"===e.readFlag?1:0,readFlag:"1"===e.readFlag,createTime:e.sendTime})}));return o(o({},e),{},{result:{records:n,total:t.total||n.length}})}return e})).catch((function(e){throw e}))}function i(e){return Object(n["b"])({url:"/sys/sysAnnouncementSend/editByAnntIdAndUserId",method:"put",data:{anntId:e,readFlag:"1"}}).then((function(e){return e})).catch((function(e){throw e}))}function u(e){return Object(n["b"])({url:"/sys/sysAnnouncementSend/editByAnntIdAndUserId",method:"put",data:{anntId:e,readFlag:"0"}}).then((function(e){return e})).catch((function(e){throw e}))}function p(){return Object(n["b"])({url:"/sys/sysAnnouncementSend/readAll",method:"put"}).then((function(e){return e})).catch((function(e){throw e}))}function l(){return Object(n["b"])({url:"/sys/sysAnnouncementSend/getMyAnnouncementSend",method:"get",params:{pageNo:1,pageSize:100,readFlag:"0"}}).then((function(e){if(e.success&&e.result){var t=e.result,r=t.records||[],n=r.map((function(e){return o(o({},e),{},{id:e.anntId,title:e.titile,content:e.msgContent,isRead:0,readFlag:!1,createTime:e.sendTime})}));return o(o({},e),{},{result:n})}return e})).catch((function(e){throw e}))}function f(){return s({pageNo:1,pageSize:1e3}).then((function(e){if(e.success&&e.result&&e.result.records){var t=e.result.records,r=t.filter((function(e){return!e.readFlag})).length,n=t.filter((function(e){return e.readFlag})).length,a=e.result.total||t.length;return o(o({},e),{},{result:{total:a,unreadCount:r,readCount:n}})}return{success:!0,result:{total:0,unreadCount:0,readCount:0}}})).catch((function(e){return{success:!1,result:{total:0,unreadCount:0,readCount:0}}}))}function h(){return f().then((function(e){return e.success&&e.result?o(o({},e),{},{result:{unreadCount:e.result.unreadCount||0}}):e}))}}}]);