(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["common~708c2b60"],{"01c1":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"homepageContainer",staticClass:"homepage-container"},[a("div",{ref:"animatedBg",staticClass:"animated-background"},[a("div",{staticClass:"bg-gradient"}),a("div",{ref:"particles",staticClass:"floating-particles"})]),a("WebsiteHeader",{attrs:{transparent:!0}}),a("section",{ref:"carouselSection",staticClass:"carousel-section"},[a("div",{staticClass:"carousel-container"},[t.loading.carousel?a("div",{staticClass:"carousel-skeleton"},[t._m(0)]):a("AdvancedCarousel",{attrs:{slides:t.carouselSlides},on:{"slide-action":t.handleSlideAction}})],1)]),a("section",{ref:"heroSection",staticClass:"hero-section"},[a("div",{staticClass:"hero-container"},[a("div",{ref:"heroContent",staticClass:"hero-content"},[a("div",{ref:"heroBadge",staticClass:"hero-badge"},[a("a-icon",{attrs:{type:"rocket"}}),a("span",[t._v("全新AI内容生成平台正式上线")]),a("div",{staticClass:"badge-glow"})],1),a("h1",{ref:"heroTitle",staticClass:"hero-title"},[a("span",{staticClass:"title-line"},[t._v("智界")]),a("span",{staticClass:"title-line gradient-text"},[t._v("AIGC")]),a("span",{staticClass:"title-subtitle"},[t._v("AI驱动的内容生成平台")])]),a("p",{ref:"heroDescription",staticClass:"hero-description"},[t._v("\n          基于前沿人工智能技术，为企业和个人提供智能内容生成解决方案。\n          "),a("br"),t._v("\n          从小红书爆款内容到专业视频剪辑，开启AI创作新时代。\n        ")]),a("div",{ref:"heroButtons",staticClass:"hero-buttons"},[a("button",{staticClass:"btn-hero-primary",on:{click:t.handlePrimaryAction}},[a("span",[t._v("立即体验")]),a("a-icon",{attrs:{type:"arrow-right"}}),a("div",{staticClass:"btn-glow"})],1),a("button",{staticClass:"btn-hero-secondary",on:{click:t.handleDemoAction}},[a("a-icon",{attrs:{type:"play-circle"}}),a("span",[t._v("观看演示")])],1)])]),a("div",{ref:"featuresShowcase",staticClass:"features-showcase"},[t.loading.features?a("div",{staticClass:"features-skeleton"},t._l(4,(function(t){return a("div",{key:t,staticClass:"feature-skeleton-card"},[a("div",{staticClass:"skeleton-icon"}),a("div",{staticClass:"skeleton-title"}),a("div",{staticClass:"skeleton-description"})])})),0):a("div",{staticClass:"features-grid"},t._l(t.features,(function(e,s){return a("div",{key:s,ref:"featureCards",refInFor:!0,staticClass:"feature-card",class:"feature-"+(s+1)},[a("div",{staticClass:"feature-icon"},[a("a-icon",{attrs:{type:e.icon}})],1),a("h3",{staticClass:"feature-title"},[t._v(t._s(e.title))]),a("p",{staticClass:"feature-description"},[t._v(t._s(e.description))]),a("div",{staticClass:"feature-glow"})])})),0)])])]),a("section",{ref:"statsSection",staticClass:"stats-section"},[a("div",{staticClass:"stats-container"},[a("div",{ref:"statsHeader",staticClass:"stats-header"},[a("h2",{staticClass:"section-title"},[t._v("平台数据")]),a("p",{staticClass:"section-subtitle"},[t._v("用数据说话，见证智界AIGC的强大实力")])]),t.loading.stats?a("div",{staticClass:"stats-skeleton"},t._l(4,(function(t){return a("div",{key:t,staticClass:"stat-skeleton-item"},[a("div",{staticClass:"skeleton-number"}),a("div",{staticClass:"skeleton-label"})])})),0):a("div",{ref:"statsGrid",staticClass:"stats-grid"},t._l(t.stats,(function(e,s){return a("div",{key:s,ref:"statItems",refInFor:!0,staticClass:"stat-item"},[a("div",{staticClass:"stat-number",attrs:{"data-target":e.target}},[t._v(t._s(e.number))]),a("div",{staticClass:"stat-label"},[t._v(t._s(e.label))]),a("div",{staticClass:"stat-glow"})])})),0)])]),a("section",{ref:"ctaSection",staticClass:"cta-section"},[a("div",{staticClass:"cta-container"},[a("div",{ref:"ctaContent",staticClass:"cta-content"},[a("h2",{staticClass:"cta-title"},[t._v("准备好开启AI创作之旅了吗？")]),a("p",{staticClass:"cta-subtitle"},[t._v("立即注册，免费体验智界AIGC的强大功能")]),a("div",{staticClass:"cta-buttons"},[a("button",{staticClass:"btn-cta-primary",on:{click:t.handleRegisterAction}},[a("a-icon",{attrs:{type:"user-add"}}),a("span",[t._v("免费注册")]),a("div",{staticClass:"btn-glow"})],1)])])])]),a("WebsiteFooter"),a("QuantumJump"),a("OfficialNoticeModal",{on:{close:t.handleNoticeClose,"no-more":t.handleNoticeNoMore}})],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"skeleton-slide"},[a("div",{staticClass:"skeleton-image"}),a("div",{staticClass:"skeleton-content"},[a("div",{staticClass:"skeleton-badge"}),a("div",{staticClass:"skeleton-title"}),a("div",{staticClass:"skeleton-description"}),a("div",{staticClass:"skeleton-button"})])])}],n=a("a34a"),r=a.n(n),o=a("cffa"),c=a("1dac"),l=a("9420"),u=a("ccb3"),d=a("35b1"),f=a("1f70e"),h=a("bd9e9"),p=a("db9e"),m=a("a73df"),v=a("8d13");function g(t,e,a,s,i,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(s,i)}function C(t){return function(){var e=this,a=arguments;return new Promise((function(s,i){var n=t.apply(e,a);function r(t){g(n,s,i,r,o,"next",t)}function o(t){g(n,s,i,r,o,"throw",t)}r(void 0)}))}}o["a"].registerPlugin(c["a"],l["a"]);var b={name:"Home",mixins:[m["a"]],components:{WebsiteHeader:u["default"],WebsiteFooter:d["default"],AdvancedCarousel:f["default"],QuantumJump:h["default"],OfficialNoticeModal:p["default"]},metaInfo:{title:"智界AIGC - AI驱动的小红书内容生成平台",meta:[{name:"description",content:"智界AIGC是专业的AI内容生成平台，专注小红书爆款内容创作。提供AI图文生成、视频制作、自动发布、剪映小助手等功能，助力内容创作者轻松打造爆款作品。"},{name:"keywords",content:"智界AIGC,AI内容生成,小红书,爆款内容,AI写作,视频制作,自动发布,剪映小助手,内容创作,人工智能"},{property:"og:title",content:"智界AIGC - AI驱动的小红书内容生成平台"},{property:"og:description",content:"专业的AI内容生成平台，专注小红书爆款内容创作，提供AI图文生成、视频制作、自动发布等功能。"},{property:"og:type",content:"website"},{property:"og:url",content:"https://www.aigcview.cn"},{property:"og:image",content:"https://www.aigcview.cn/images/og-image.jpg"},{name:"twitter:card",content:"summary_large_image"},{name:"twitter:title",content:"智界AIGC - AI驱动的小红书内容生成平台"},{name:"twitter:description",content:"专业的AI内容生成平台，专注小红书爆款内容创作，提供AI图文生成、视频制作、自动发布等功能。"}],link:[{rel:"canonical",href:"https://www.aigcview.cn"}]},data:function(){return{heartbeatConfig:Object(v["a"])("home",{apiKey:"home-page-heartbeat-key",enableDebugLog:!1}),loading:{carousel:!0,features:!0,stats:!0},carouselSlides:[],features:[],stats:[]}},mounted:function(){var t=C(r.a.mark((function t(){var e,a,s=this;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=performance.now(),t.prev=1,this.trackPerformance("page_load_start"),t.next=5,this.loadPageData();case 5:this.initAnimations(),this.initParticles(),this.$nextTick((function(){setTimeout((function(){var t=s.$refs.homepageContainer||s.$el;t&&t.classList.add("loaded")}),200)})),a=performance.now()-e,this.trackPerformance("page_load_complete",{loadTime:a}),t.next=19;break;case 13:throw t.prev=13,t.t0=t["catch"](1),this.$nextTick((function(){var t=s.$refs.homepageContainer||s.$el;t&&t.classList.add("loaded")})),this.trackPerformance("page_load_error",{error:t.t0.message}),t.t0;case 19:case"end":return t.stop()}}),t,this,[[1,13]])})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){c["a"].getAll().forEach((function(t){t.kill()}))},methods:{loadPageData:function(){var t=C(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Promise.all([this.loadCarouselData(),this.loadFeaturesData(),this.loadStatsData()]);case 3:t.next=10;break;case 6:throw t.prev=6,t.t0=t["catch"](0),t.t0;case 10:case"end":return t.stop()}}),t,this,[[0,6]])})));function e(){return t.apply(this,arguments)}return e}(),loadCarouselData:function(){var t=C(r.a.mark((function t(){var e,a=this;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,this.$http.get("/aigc/aigcHomeCarousel/list",{params:{status:"1",pageNo:1,pageSize:10}});case 4:if(e=t.sent,!(e.success&&e.result&&e.result.records)){t.next=14;break}this.carouselSlides=e.result.records.sort((function(t,e){return(t.sortOrder||t.sort_order||0)-(e.sortOrder||e.sort_order||0)})).map((function(t,e){return{image:a.getCarouselImage(t.imageUrl||t.image_url,e),badge:t.badge||"",title:t.title||"",description:t.description||"",primaryAction:{text:t.buttonText||t.button_text||"了解更多",link:t.buttonLink||t.button_link||"#"}}})),this.loading.carousel=!1,t.next=15;break;case 14:throw new Error("API返回数据格式错误");case 15:t.next=22;break;case 17:throw t.prev=17,t.t0=t["catch"](0),this.loading.carousel=!1,t.t0;case 22:case"end":return t.stop()}}),t,this,[[0,17]])})));function e(){return t.apply(this,arguments)}return e}(),getCarouselImage:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t&&(t.startsWith("http://")||t.startsWith("https://")))return t;if(t&&""!==t.trim()){if(window.getFileAccessHttpUrl){var a=window.getFileAccessHttpUrl(t);return a}var s="".concat(window._CONFIG["staticDomainURL"],"/").concat(t);return s}var i=["https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=600&fit=crop&crop=center","https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=1200&h=600&fit=crop&crop=center","https://images.unsplash.com/photo-1552664730-d307ca884978?w=1200&h=600&fit=crop&crop=center"],n=i[e%i.length];return n},loadFeaturesData:function(){var t=C(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.$http.get("/aigc/websiteFeatures/list",{params:{status:"1",pageNo:1,pageSize:10}});case 3:if(e=t.sent,!(e.success&&e.result&&e.result.records)){t.next=10;break}this.features=e.result.records.sort((function(t,e){return(t.sortOrder||t.sort_order||0)-(e.sortOrder||e.sort_order||0)})).map((function(t){return{icon:t.icon||"star",title:t.title||"",description:t.description||"",link:t.linkUrl||t.link_url||"#"}})),this.loading.features=!1,t.next=11;break;case 10:throw new Error("API返回数据格式错误");case 11:t.next=18;break;case 13:throw t.prev=13,t.t0=t["catch"](0),this.loading.features=!1,t.t0;case 18:case"end":return t.stop()}}),t,this,[[0,13]])})));function e(){return t.apply(this,arguments)}return e}(),loadStatsData:function(){var t=C(r.a.mark((function t(){var e;return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=4,this.$http.get("/aigc/websiteStats/list",{params:{status:"1",pageNo:1,pageSize:10}});case 4:if(e=t.sent,!(e.success&&e.result&&e.result.records)){t.next=14;break}this.stats=e.result.records.sort((function(t,e){return(t.sortOrder||t.sort_order||0)-(e.sortOrder||e.sort_order||0)})).map((function(t){var e=t.statValue||t.stat_value||"0",a=t.statLabel||t.stat_label||"",s=t.targetNumber||t.target_number||0;return{number:e,label:a,target:s,originalValue:e}})).filter((function(t){return!("注册用户"===t.label||"注册用户数"===t.label)})),this.loading.stats=!1,t.next=15;break;case 14:throw new Error("API返回数据格式错误");case 15:t.next=22;break;case 17:throw t.prev=17,t.t0=t["catch"](0),this.loading.stats=!1,t.t0;case 22:case"end":return t.stop()}}),t,this,[[0,17]])})));function e(){return t.apply(this,arguments)}return e}(),reloadPage:function(){var t=C(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.loading={carousel:!0,features:!0,stats:!0},t.prev=1,t.next=4,this.loadPageData();case 4:this.initAnimations(),this.initParticles(),this.$message.success("页面重新加载成功"),t.next=13;break;case 9:throw t.prev=9,t.t0=t["catch"](1),t.t0;case 13:case"end":return t.stop()}}),t,this,[[1,9]])})));function e(){return t.apply(this,arguments)}return e}(),goHome:function(){window.location.reload()},trackPerformance:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{var a={event:t,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent};if(Object.assign(a,e),"undefined"!==typeof performance&&performance.getEntriesByType)try{var s=performance.getEntriesByType("paint"),i=s.find((function(t){return"first-paint"===t.name})),n=s.find((function(t){return"first-contentful-paint"===t.name}));a.pageMetrics={firstPaint:i?i.startTime:0,firstContentfulPaint:n?n.startTime:0}}catch(r){}if("undefined"!==typeof performance&&performance.memory)try{a.memory={used:performance.memory.usedJSHeapSize,total:performance.memory.totalJSHeapSize,limit:performance.memory.jsHeapSizeLimit}}catch(o){}}catch(c){}},sendToAnalytics:function(t){window.gtag&&window.gtag("event",t.event,{custom_parameter:JSON.stringify(t)})},handlePrimaryAction:function(){this.$router.push("/market")},handleDemoAction:function(){this.$message.info("产品演示正在制作中，敬请期待...")},handleRegisterAction:function(){this.$router.push("/login")},handleSlideAction:function(t){var e=t.action;switch(e.text){case"立即体验":this.$message.info("即将跳转到产品体验页面...");break;case"了解更多":this.$message.info("即将跳转到功能介绍页面...");break;case"企业咨询":this.$message.info("即将跳转到企业服务页面...");break;default:this.$message.info("点击了: ".concat(e.text))}},initAnimations:function(){var t=this;this.$nextTick((function(){t.initHeroAnimations(),t.initFeaturesAnimations(),t.initStatsAnimations(),t.initCTAAnimations()}))},initHeroAnimations:function(){var t=o["a"].timeline({delay:.1});t.from(this.$refs.carouselSection,{duration:.3,y:20,opacity:0,scale:.98,ease:"power2.out"}).from(this.$refs.heroBadge,{duration:.3,y:20,opacity:0,scale:.95,ease:"power2.out"},"-=0.2").from(".title-line",{duration:.4,y:30,opacity:0,stagger:.05,ease:"power2.out"},"-=0.25").from(".title-subtitle",{duration:.4,y:20,opacity:0,ease:"power2.out"},"-=0.35").from(this.$refs.heroDescription,{duration:.3,y:15,opacity:0,ease:"power2.out"},"-=0.35").from(this.$refs.heroButtons,{duration:.3,y:15,opacity:0,ease:"power2.out"},"-=0.25").from(this.$refs.featuresShowcase,{duration:.4,y:20,opacity:0,ease:"power2.out"},"-=0.35"),o["a"].to(this.$refs.animatedBg,{yPercent:-30,ease:"none",scrollTrigger:{trigger:this.$refs.heroSection,start:"top bottom",end:"bottom top",scrub:1}})},initFeaturesAnimations:function(){o["a"].set(".feature-card",{opacity:1,y:0,scale:1,rotationY:0,clearProps:"transform"}),o["a"].from(".feature-card",{duration:.6,y:50,opacity:0,scale:.9,stagger:.05,ease:"power2.out",scrollTrigger:{trigger:this.$refs.featuresShowcase,start:"top 95%",toggleActions:"play none none none"},onComplete:function(){o["a"].set(".feature-card",{y:0,scale:1,rotationY:0,clearProps:"transform"})}}),this.$nextTick((function(){document.querySelectorAll(".feature-card").forEach((function(t){t.addEventListener("mouseenter",(function(){o["a"].to(t,{duration:.3,scale:1.05,y:-10,rotationY:3,ease:"power2.out"}),o["a"].to(t.querySelector(".feature-glow"),{duration:.3,opacity:1,scale:1.1,ease:"power2.out"})})),t.addEventListener("mouseleave",(function(){o["a"].to(t,{duration:.3,scale:1,y:0,rotationY:0,ease:"power2.out",clearProps:"transform"}),o["a"].to(t.querySelector(".feature-glow"),{duration:.3,opacity:0,scale:1,ease:"power2.out"})}))}))}))},initStatsAnimations:function(){o["a"].from(this.$refs.statsHeader,{duration:.5,y:30,opacity:0,ease:"power2.out",scrollTrigger:{trigger:this.$refs.statsSection,start:"top 85%",toggleActions:"play none none none"}}),o["a"].from(".stat-item",{duration:.6,y:40,opacity:0,scale:.95,stagger:.05,ease:"power2.out",scrollTrigger:{trigger:this.$refs.statsGrid,start:"top 80%",toggleActions:"play none none none"}});var t=this;this.$nextTick((function(){var e=document.querySelectorAll(".stat-number");e.forEach((function(e,a){var s=t.stats[a];s&&s.originalValue&&(e.innerHTML=s.originalValue,o["a"].from(e,{duration:.8,opacity:0,scale:.8,ease:"power2.out",scrollTrigger:{trigger:e,start:"top 85%",toggleActions:"play none none none"}}))}))}))},initCTAAnimations:function(){o["a"].from(this.$refs.ctaContent,{duration:.5,y:30,opacity:0,ease:"power2.out",scrollTrigger:{trigger:this.$refs.ctaSection,start:"top 85%",toggleActions:"play none none none"}})},initParticles:function(){var t=this.$refs.particles;if(t)for(var e=0;e<50;e++){var a=document.createElement("div");a.className="particle",a.style.cssText="\n          position: absolute;\n          width: ".concat(4*Math.random()+2,"px;\n          height: ").concat(4*Math.random()+2,"px;\n          background: rgba(59, 130, 246, ").concat(.3*Math.random()+.1,");\n          border-radius: 50%;\n          left: ").concat(100*Math.random(),"%;\n          top: ").concat(100*Math.random(),"%;\n        "),t.appendChild(a),o["a"].to(a,{duration:20*Math.random()+10,y:-window.innerHeight,x:"+=".concat(200*Math.random()-100),opacity:0,ease:"none",repeat:-1,delay:5*Math.random()})}},handleNoticeClose:function(){},handleNoticeNoMore:function(){this.$message.success("已设置不再提示，本次会话期间不会再显示")}}},y=b,_=(a("d4f2"),a("2877")),w=Object(_["a"])(y,s,i,!1,null,"154caf81",null);e["default"]=w.exports},"1d439":function(t,e,a){"use strict";var s=a("ee26"),i=a.n(s);i.a},"1dfe":function(t,e,a){},2099:function(t,e,a){"use strict";var s=a("b1c9"),i=a.n(s);i.a},5333:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"2rem","text-align":"center","min-height":"100vh",background:"#f5f5f5"}},[a("h1",{staticStyle:{color:"#333","font-size":"3rem","margin-bottom":"1rem"}},[t._v("\n    智界AIGC\n  ")]),a("p",{staticStyle:{color:"#666","font-size":"1.2rem","margin-bottom":"2rem"}},[t._v("\n    AI驱动的内容生成平台\n  ")]),a("p",{staticStyle:{color:"#999"}},[t._v("\n    这是简化版的首页，用于测试路由是否正常工作\n  ")]),a("p",{staticStyle:{color:"#999"}},[t._v("\n    当前路由: "+t._s(t.$route.path)+"\n  ")]),a("p",{staticStyle:{color:"#999"}},[t._v("\n    当前时间: "+t._s((new Date).toLocaleString())+"\n  ")]),a("div",{staticStyle:{"margin-top":"2rem"}},[a("button",{staticStyle:{margin:"0.5rem",padding:"1rem 2rem",background:"#3b82f6",color:"white",border:"none","border-radius":"8px",cursor:"pointer"},on:{click:t.goToMarket}},[t._v("\n      前往商城\n    ")]),a("button",{staticStyle:{margin:"0.5rem",padding:"1rem 2rem",background:"#10b981",color:"white",border:"none","border-radius":"8px",cursor:"pointer"},on:{click:t.goToLogin}},[t._v("\n      前往登录\n    ")])])])},i=[],n={name:"SimpleHome",methods:{goToMarket:function(){this.$router.push("/market")},goToLogin:function(){this.$router.push("/login")}}},r=n,o=a("2877"),c=Object(o["a"])(r,s,i,!1,null,null,null);e["default"]=c.exports},5348:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"carousel-test-page"},[a("h1",[t._v("轮播图功能测试")]),a("div",{staticClass:"test-info"},[a("p",[t._v("当前幻灯片: "+t._s(t.currentSlideIndex+1)+" / "+t._s(t.testSlides.length))]),a("p",[t._v("自动播放: "+t._s(t.isAutoPlaying?"开启":"关闭"))]),a("p",[t._v("幻灯片数据: "+t._s(t.testSlides.length)+" 张")])]),a("div",{staticClass:"manual-controls"},[a("button",{staticClass:"control-btn",on:{click:t.prevSlide}},[t._v("上一张")]),a("button",{staticClass:"control-btn",on:{click:t.nextSlide}},[t._v("下一张")]),a("button",{staticClass:"control-btn",on:{click:t.toggleAutoPlay}},[t._v("\n      "+t._s(t.isAutoPlaying?"暂停":"播放")+"\n    ")]),a("button",{staticClass:"control-btn",on:{click:t.goToFirst}},[t._v("回到第一张")])]),a("div",{staticClass:"carousel-wrapper"},[a("AdvancedCarousel",{ref:"carousel",attrs:{slides:t.testSlides,autoPlay:t.isAutoPlaying,interval:3e3},on:{"slide-change":t.onSlideChange}})],1),a("div",{staticClass:"debug-info"},[a("h3",[t._v("调试信息")]),a("pre",[t._v(t._s(t.debugInfo))])])])},i=[],n=a("1f70e"),r={name:"CarouselTest",components:{AdvancedCarousel:n["default"]},data:function(){return{currentSlideIndex:0,isAutoPlaying:!0,debugInfo:{},testSlides:[{image:"/plugImg.jpg",badge:"测试1",title:"第一张测试图片",description:"这是第一张测试图片，用于验证轮播图基础功能",primaryAction:{text:"测试按钮1"}},{image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=1200&h=500&fit=crop&crop=center",badge:"测试2",title:"第二张测试图片",description:"这是第二张测试图片，用于验证切换功能",primaryAction:{text:"测试按钮2"}},{image:"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=500&fit=crop&crop=center",badge:"测试3",title:"第三张测试图片",description:"这是第三张测试图片，用于验证循环播放",primaryAction:{text:"测试按钮3"}}]}},mounted:function(){var t=this;this.updateDebugInfo(),this.$nextTick((function(){t.$refs.carousel}))},methods:{onSlideChange:function(t){this.currentSlideIndex=t,this.updateDebugInfo()},prevSlide:function(){this.$refs.carousel&&this.$refs.carousel.prevSlideHandler()},nextSlide:function(){this.$refs.carousel&&this.$refs.carousel.nextSlideHandler()},toggleAutoPlay:function(){this.isAutoPlaying=!this.isAutoPlaying,this.$refs.carousel&&this.$refs.carousel.toggleAutoPlay()},goToFirst:function(){this.$refs.carousel&&this.$refs.carousel.goToSlide(0)},updateDebugInfo:function(){this.debugInfo={currentSlide:this.currentSlideIndex,totalSlides:this.testSlides.length,autoPlay:this.isAutoPlaying,timestamp:(new Date).toLocaleTimeString()}}}},o=r,c=(a("d27a"),a("2877")),l=Object(c["a"])(o,s,i,!1,null,"383bfe9a",null);e["default"]=l.exports},b1c9:function(t,e,a){},d27a:function(t,e,a){"use strict";var s=a("f520"),i=a.n(s);i.a},d4f2:function(t,e,a){"use strict";var s=a("1dfe"),i=a.n(s);i.a},db9e:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"official-notice-modal-wrapper"},[t.visible?a("div",{staticClass:"modal-overlay",on:{click:t.handleOverlayClick}}):t._e(),t.visible?a("div",{staticClass:"modal-container",class:{"modal-show":t.showAnimation}},[a("div",{staticClass:"modal-content"},[a("div",{staticClass:"modal-header"},[a("div",{staticClass:"header-icon"},[a("a-icon",{attrs:{type:"notification"}})],1),a("h2",{staticClass:"header-title"},[t._v("官方公告")]),a("button",{staticClass:"close-btn",on:{click:t.handleClose}},[a("a-icon",{attrs:{type:"close"}})],1)]),a("div",{staticClass:"modal-body"},[a("div",{staticClass:"content-layout"},[a("div",{staticClass:"left-content"},[a("div",{staticClass:"notice-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"gift"}}),t._v("\n                优惠活动\n              ")],1),a("div",{staticClass:"activity-list"},[a("div",{staticClass:"activity-item"},[a("div",{staticClass:"activity-badge"},[t._v("活动一")]),a("p",[t._v("即日起至2025年10月7日，购买VIP、SVIP年费赠送半年会员时长。")]),a("div",{staticClass:"activity-footer"},[a("button",{staticClass:"activity-btn",on:{click:t.goToMembership}},[a("a-icon",{attrs:{type:"crown"}}),t._v("\n                      前往开通\n                    ")],1)])]),a("div",{staticClass:"activity-item"},[a("div",{staticClass:"activity-badge"},[t._v("活动二")]),t._m(0),a("div",{staticClass:"activity-footer"},[a("button",{staticClass:"activity-btn",on:{click:t.goToRecharge}},[a("a-icon",{attrs:{type:"wallet"}}),t._v("\n                      前往充值\n                    ")],1)])])])]),a("div",{staticClass:"notice-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"arrow-down"}}),t._v("\n                降价通知\n              ")],1),t._m(1),a("p",{staticClass:"price-note"},[t._v("计费详情请到插件中心查看")])])]),a("div",{staticClass:"right-content"},[a("div",{staticClass:"notice-section"},[a("h3",{staticClass:"section-title"},[a("a-icon",{attrs:{type:"sync"}}),t._v("\n                更新公告\n              ")],1),t._m(2)])])])]),a("div",{staticClass:"modal-footer"},[a("button",{staticClass:"btn-no-more",on:{click:t.handleNoMore}},[a("a-icon",{attrs:{type:"eye-invisible"}}),t._v("\n          不再提示\n        ")],1),a("button",{staticClass:"btn-confirm",on:{click:t.handleClose}},[a("a-icon",{attrs:{type:"check"}}),t._v("\n          我知道了\n        ")],1)])])]):t._e()])},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"recharge-activity"},[a("p",{staticClass:"activity-title"},[t._v("充值活动：")]),a("div",{staticClass:"recharge-list"},[a("div",{staticClass:"recharge-item"},[t._v("充50元赠4元")]),a("div",{staticClass:"recharge-item"},[t._v("充100元赠12元")]),a("div",{staticClass:"recharge-item"},[t._v("充500元赠80元")]),a("div",{staticClass:"recharge-item"},[t._v("充1000元赠200元")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"price-list"},[a("div",{staticClass:"price-item"},[a("span",{staticClass:"plugin-name"},[t._v("豆包图生视频")]),a("span",{staticClass:"discount"},[t._v("永久降价15%")])]),a("div",{staticClass:"price-item"},[a("span",{staticClass:"plugin-name"},[t._v("豆包文生图")]),a("span",{staticClass:"discount"},[t._v("永久降价20%")])]),a("div",{staticClass:"price-item"},[a("span",{staticClass:"plugin-name"},[t._v("视频生成音频")]),a("span",{staticClass:"discount"},[t._v("永久降价20%")])])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"update-list"},[a("div",{staticClass:"update-item"},[a("div",{staticClass:"update-number"},[t._v("1.")]),a("p",[t._v("因备案手续问题，本站更换域名为："),a("strong",[t._v("www.aigcview.cn")]),t._v("。原域名需七个工作日后方可正常访问。")])]),a("div",{staticClass:"update-item"},[a("div",{staticClass:"update-number"},[t._v("2.")]),a("p",[t._v("因运营成本压力，现更改小红书分享插件使用权限为每天5次免费调用。VIP及以上权限用户可无限制使用。")])]),a("div",{staticClass:"update-item"},[a("div",{staticClass:"update-number"},[t._v("3.")]),a("div",{staticClass:"pro-update"},[a("p",{staticClass:"pro-title"},[t._v("超级剪映小助手PRO重磅更新：")]),a("div",{staticClass:"pro-features"},[a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_images_pro工具")]),t._v("：三个动画参数都增加为不同素材添加不同动画功能")]),a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_captions_pro工具")]),t._v("：三个动画参数都增加了为不同素材添加不同动画功能")]),a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_videos_pro工具")]),t._v("：转场效果参数增加为不同素材添加不同转场效果功能")]),a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_audios_pro工具")]),t._v("：特效音参数增加为不同素材添加不同特效音功能")]),a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_msks_pro工具")]),t._v("：蒙版类型参数增加为不同素材添加不同蒙版类型功能")]),a("div",{staticClass:"feature-item"},[t._v("• "),a("strong",[t._v("Add_keyframes_pro工具")]),t._v("：关键帧位置比例参数增加为不同素材添加不关键帧位置比例功能；values参数增加为不同素材添加不同values数值功能；ctype参数加为不同素材添加不同关键帧类型功能")])]),a("p",{staticClass:"pro-note"},[t._v("让您的自动化剪辑更便捷、更具想象力，详情请查看使用说明")])])])])}],n={name:"OfficialNoticeModal",data:function(){return{visible:!1,showAnimation:!1}},mounted:function(){this.checkShouldShow()},beforeDestroy:function(){document.body.style.overflow=""},methods:{checkShouldShow:function(){var t=this,e=sessionStorage.getItem("official-notice-no-more");e||setTimeout((function(){t.showModal()}),1e3)},showModal:function(){var t=this;this.visible=!0,document.body.style.overflow="hidden",this.$nextTick((function(){setTimeout((function(){t.showAnimation=!0}),50)}))},hideModal:function(){var t=this;this.showAnimation=!1,setTimeout((function(){t.visible=!1,document.body.style.overflow=""}),300)},handleClose:function(){this.hideModal(),this.$emit("close")},handleOverlayClick:function(){this.handleClose()},handleNoMore:function(){sessionStorage.setItem("official-notice-no-more","true"),this.hideModal(),this.$emit("no-more")},goToMembership:function(){this.hideModal(),this.$router.push("/membership")},goToRecharge:function(){this.hideModal(),this.$router.push("/usercenter?page=credits")}}},r=n,o=(a("1d439"),a("2877")),c=Object(o["a"])(r,s,i,!1,null,"4e2e3bfc",null);e["default"]=c.exports},df29:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("nav",{ref:"navbar",staticClass:"home-navbar",class:{scrolled:t.isScrolled}},[a("div",{staticClass:"nav-container"},[a("div",{ref:"navBrand",staticClass:"nav-brand"},[a("LogoImage",{attrs:{size:"medium",hover:!0,"container-class":"brand-logo-container","image-class":"brand-logo-image","fallback-class":"brand-logo-fallback"}}),a("span",{staticClass:"brand-text"},[t._v("智界AIGC")])],1),a("div",{ref:"navMenu",staticClass:"nav-menu"},t._l(t.menuItems,(function(e){return a("a",{key:e.name,staticClass:"nav-link",attrs:{href:e.href}},[a("a-icon",{staticClass:"nav-icon",attrs:{type:e.icon}}),a("span",{staticClass:"nav-text"},[t._v(t._s(e.name))])],1)})),0),a("div",{ref:"navActions",staticClass:"nav-actions"},[a("button",{staticClass:"btn-secondary"},[t._v("登录")])]),a("button",{ref:"mobileMenuBtn",staticClass:"mobile-menu-btn",on:{click:t.toggleMobileMenu}},[a("a-icon",{attrs:{type:t.mobileMenuOpen?"close":"menu"}})],1)])])},i=[],n=a("a34a"),r=a.n(n),o=a("cffa"),c=a("8bd7");function l(t,e,a,s,i,n,r){try{var o=t[n](r),c=o.value}catch(l){return void a(l)}o.done?e(c):Promise.resolve(c).then(s,i)}function u(t){return function(){var e=this,a=arguments;return new Promise((function(s,i){var n=t.apply(e,a);function r(t){l(n,s,i,r,o,"next",t)}function o(t){l(n,s,i,r,o,"throw",t)}r(void 0)}))}}var d={name:"HomeHeader",components:{LogoImage:c["default"]},data:function(){return{isScrolled:!1,mobileMenuOpen:!1,menuItems:[]}},mounted:function(){var t=u(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.loadMenuData();case 2:this.initScrollListener(),this.initNavbarAnimations();case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),beforeDestroy:function(){window.removeEventListener("scroll",this.handleScroll)},methods:{loadMenuData:function(){var t=u(r.a.mark((function t(){return r.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{this.menuItems=[{name:"首页",href:"#home",icon:"home"},{name:"商城",href:"#market",icon:"shop"},{name:"客户案例",href:"#cases",icon:"trophy"},{name:"教程中心",href:"#tutorials",icon:"book"},{name:"签到奖励",href:"#signin",icon:"gift"},{name:"订阅会员",href:"#membership",icon:"crown"},{name:"邀请奖励",href:"#affiliate",icon:"team"},{name:"个人中心",href:"#usercenter",icon:"user"}]}catch(e){this.menuItems=[{name:"首页",href:"#home",icon:"home"},{name:"商城",href:"#market",icon:"shop"},{name:"个人中心",href:"#usercenter",icon:"user"}]}case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),initScrollListener:function(){window.addEventListener("scroll",this.handleScroll)},handleScroll:function(){this.isScrolled=window.scrollY>50},toggleMobileMenu:function(){this.mobileMenuOpen=!this.mobileMenuOpen},initNavbarAnimations:function(){o["a"].from([this.$refs.navBrand,this.$refs.navMenu,this.$refs.navActions],{duration:.4,y:-20,opacity:0,ease:"power2.out",stagger:.05})}}},f=d,h=(a("2099"),a("2877")),p=Object(h["a"])(f,s,i,!1,null,"2a8a8cf4",null);e["default"]=p.exports},ee26:function(t,e,a){},f520:function(t,e,a){}}]);